# 垃圾焚烧数据分析智能体提示词修改说明

## 修改目标
将原有的纯文本Word文档生成功能升级为**图文并茂**的Word文档生成功能，支持在Word文档中嵌入由ECharts配置生成的图表图片。

## 主要修改内容

### 1. 角色定义更新
- **原来**：生成标准化的垃圾焚烧企业数据分析报告
- **现在**：生成**图文并茂**的垃圾焚烧企业数据分析报告，最终输出为可下载的Word文档

### 2. 任务流程增强
- **第4步**：从"生成完整的markdown格式分析报告" → "生成包含**图表标记**的markdown格式分析报告"
- **第5步**：从"配置echarts图表用于前端渲染" → "为每个图表生成对应的ECharts配置"
- **第7步**：从"转换为Word文档" → "转换为**图文并茂的Word文档**"

### 3. 新增图表标记语法
在markdown报告中使用以下语法嵌入图表：
```markdown
![图表标题](chart_id)
```

### 4. 推荐图表类型定义
定义了6种标准图表类型：
- `chart_temperature_trend` - 炉膛温度变化趋势图（折线图）
- `chart_waste_input` - 垃圾投加量变化图（柱状图）
- `chart_emission_trend` - 污染物排放趋势图（折线图）
- `chart_status_distribution` - 工况分布饼图（饼图）
- `chart_material_consumption` - 环保耗材消耗对比图（柱状图）
- `chart_compliance_comparison` - 达标情况对比图（柱状图）

### 5. 工作流调用格式更新
- **原来**：传入纯markdown文本
- **现在**：传入JSON格式，包含markdown和charts两个字段：
```json
{
  "markdown": "包含图表标记的完整markdown报告内容",
  "charts": {
    "chart_id": { /* ECharts配置 */ }
  }
}
```

### 6. 输出格式规范化
标准四段式输出：
1. **数据表格**（开头）
2. **图文并茂分析内容**（中间）- 包含图表标记
3. **ECharts配置对象**（中间）- JSON格式
4. **图文并茂Word文档下载**（结尾）

## 技术实现原理

### 服务器端支持
1. **app.py** - 提供 `/office/word/convert_with_charts` 接口
2. **markdown_processor.py** - 解析图表标记，调用图表生成器
3. **chart_generator.py** - 将ECharts配置转换为PNG图片
4. **word_generator.py** - 将图片嵌入Word文档

### 处理流程
1. 智能体生成包含图表标记的markdown报告
2. 智能体生成对应的ECharts配置对象
3. 调用工作流，传入markdown和charts
4. 服务器解析图表标记，生成PNG图片
5. 将图片嵌入Word文档的对应位置
6. 返回包含图表的Word文档下载链接

## 使用效果

### 原来的输出
- 纯文本Word文档
- 只有数据表格和文字分析
- 缺乏可视化效果

### 现在的输出
- 图文并茂的Word文档
- 包含数据表格、文字分析和嵌入式图表
- 图表自动从ECharts配置生成为高质量PNG图片
- 专业美观的可视化效果

## 质量控制增强

### 新增图表质量控制
1. **图表标记准确性** - 确保markdown中的图表标记语法正确
2. **配置完整性** - 每个图表ID都有对应的完整ECharts配置
3. **数据一致性** - 图表数据与文字分析中的数据保持一致
4. **可视化效果** - 图表类型选择合适，能够清晰展示数据趋势
5. **标记对应性** - 确保图表标记与ECharts配置键值完全一致

## 用户体验提升

### 智能体使用者
- 明确的图表标记语法指导
- 标准化的图表类型推荐
- 完整的输出格式示例

### 最终用户
- 获得图文并茂的专业报告
- 直观的数据可视化展示
- 便于理解和决策的图表分析

## 兼容性说明
- 保持原有的数据查询流程不变（text2sql → sql_execute）
- 保持原有的分析逻辑和专业标准不变
- 新增的图表功能为增强功能，不影响原有功能

## 总结
此次修改将智能体从"纯文本报告生成器"升级为"图文并茂报告生成器"，大幅提升了报告的可视化效果和用户体验，同时保持了原有的专业性和准确性。
