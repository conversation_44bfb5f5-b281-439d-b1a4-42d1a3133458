{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "Bilstm_multivar.ipynb", "provenance": [{"file_id": "1IxQcZpOXeCS9CtXRmPjnFz7k5vwDTcqP", "timestamp": 1596386367690}], "toc_visible": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "metadata": {"id": "ukOLd5z9o2NQ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 357}, "executionInfo": {"status": "ok", "timestamp": 1598965655056, "user_tz": -60, "elapsed": 780, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "694322a9-e0f3-4313-8e41-584a6049aa88"}, "source": ["  !nvidia-smi"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["<PERSON><PERSON> Sep  1 13:07:34 2020       \n", "+-----------------------------------------------------------------------------+\n", "| NVIDIA-SMI 450.66       Driver Version: 418.67       CUDA Version: 10.1     |\n", "|-------------------------------+----------------------+----------------------+\n", "| GPU  Name        Persistence-M| Bus-Id        Disp.A | Volatile Uncorr. ECC |\n", "| Fan  Temp  Perf  Pwr:Usage/Cap|         Memory-Usage | GPU-Util  Compute M. |\n", "|                               |                      |               MIG M. |\n", "|===============================+======================+======================|\n", "|   0  Tesla T4            Off  | 00000000:00:04.0 Off |                    0 |\n", "| N/A   42C    P8    11W /  70W |      0MiB / 15079MiB |      0%      Default |\n", "|                               |                      |                 ERR! |\n", "+-------------------------------+----------------------+----------------------+\n", "                                                                               \n", "+-----------------------------------------------------------------------------+\n", "| Processes:                                                                  |\n", "|  GPU   GI   CI        PID   Type   Process name                  GPU Memory |\n", "|        ID   ID                                                   Usage      |\n", "|=============================================================================|\n", "|  No running processes found                                                 |\n", "+-----------------------------------------------------------------------------+\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "AIdpFAYUpDRR", "colab_type": "code", "colab": {}}, "source": ["import pandas as pd\n", "from matplotlib import pyplot as plt\n", "import numpy as np\n", "from sklearn.metrics import mean_squared_error"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "GdwmDyu4pG2O", "colab_type": "text"}, "source": ["# 导入数据"]}, {"cell_type": "code", "metadata": {"id": "Iib6DHLKDN84", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 54}, "executionInfo": {"status": "ok", "timestamp": 1598965655275, "user_tz": -60, "elapsed": 986, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "1e0ec3db-379a-46d7-a10e-2eb24a87bd55"}, "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "E9MpBLKwpIlm", "colab_type": "code", "colab": {}}, "source": ["dataset=pd.read_csv('/content/drive/My Drive/air_inference/data/Bloomsbury_clean.csv')"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "ihnTMkg0pWjr", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 289}, "executionInfo": {"status": "ok", "timestamp": 1598965656262, "user_tz": -60, "elapsed": 1959, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "5f5a3add-ac07-4a83-ca02-6b0b04c1317d"}, "source": ["dataset.head()"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>site</th>\n", "      <th>code</th>\n", "      <th>date</th>\n", "      <th>nox</th>\n", "      <th>no2</th>\n", "      <th>no</th>\n", "      <th>o3</th>\n", "      <th>pm2.5</th>\n", "      <th>ws</th>\n", "      <th>wd</th>\n", "      <th>air_temp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>London Bloomsbury</td>\n", "      <td>CLL2</td>\n", "      <td>2018-01-01 00:00:00</td>\n", "      <td>38.719371</td>\n", "      <td>27.599582</td>\n", "      <td>7.252141</td>\n", "      <td>47.360318</td>\n", "      <td>7.497625</td>\n", "      <td>4.598855</td>\n", "      <td>257.279906</td>\n", "      <td>5.378717</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>London Bloomsbury</td>\n", "      <td>CLL2</td>\n", "      <td>2018-01-01 01:00:00</td>\n", "      <td>38.976582</td>\n", "      <td>27.836512</td>\n", "      <td>7.265368</td>\n", "      <td>47.042127</td>\n", "      <td>7.449653</td>\n", "      <td>4.603798</td>\n", "      <td>257.009139</td>\n", "      <td>5.412134</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>London Bloomsbury</td>\n", "      <td>CLL2</td>\n", "      <td>2018-01-01 02:00:00</td>\n", "      <td>39.251382</td>\n", "      <td>28.072885</td>\n", "      <td>7.290429</td>\n", "      <td>46.715825</td>\n", "      <td>7.416401</td>\n", "      <td>4.621557</td>\n", "      <td>256.762603</td>\n", "      <td>5.453970</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>London Bloomsbury</td>\n", "      <td>CLL2</td>\n", "      <td>2018-01-01 03:00:00</td>\n", "      <td>37.985254</td>\n", "      <td>27.997451</td>\n", "      <td>6.513879</td>\n", "      <td>46.400863</td>\n", "      <td>7.358787</td>\n", "      <td>4.636919</td>\n", "      <td>256.538550</td>\n", "      <td>5.502388</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>London Bloomsbury</td>\n", "      <td>CLL2</td>\n", "      <td>2018-01-01 04:00:00</td>\n", "      <td>38.973919</td>\n", "      <td>28.512513</td>\n", "      <td>6.822754</td>\n", "      <td>46.033610</td>\n", "      <td>7.302818</td>\n", "      <td>4.658491</td>\n", "      <td>256.342472</td>\n", "      <td>5.554477</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                site  code                 date  ...        ws          wd  air_temp\n", "0  London Bloomsbury  CLL2  2018-01-01 00:00:00  ...  4.598855  257.279906  5.378717\n", "1  London Bloomsbury  CLL2  2018-01-01 01:00:00  ...  4.603798  257.009139  5.412134\n", "2  London Bloomsbury  CLL2  2018-01-01 02:00:00  ...  4.621557  256.762603  5.453970\n", "3  London Bloomsbury  CLL2  2018-01-01 03:00:00  ...  4.636919  256.538550  5.502388\n", "4  London Bloomsbury  CLL2  2018-01-01 04:00:00  ...  4.658491  256.342472  5.554477\n", "\n", "[5 rows x 11 columns]"]}, "metadata": {"tags": []}, "execution_count": 8}]}, {"cell_type": "markdown", "metadata": {"id": "qsw13S-QuS9t", "colab_type": "text"}, "source": ["# 多变量进行预测"]}, {"cell_type": "code", "metadata": {"id": "3zTUeXsCuZ3V", "colab_type": "code", "colab": {}}, "source": ["var_origin=dataset[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']].values"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "hw0hFQeWup8R", "colab_type": "text"}, "source": ["数据进行归一化操作"]}, {"cell_type": "code", "metadata": {"id": "T9bUOj3purhd", "colab_type": "code", "colab": {}}, "source": ["from sklearn.preprocessing import MinMaxScaler\n", "scaler = MinMaxScaler(feature_range=(0, 1))\n", "scaled = scaler.fit_transform(var_origin)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "k0vvmm5puuTJ", "colab_type": "text"}, "source": ["将数据转为cuda类型"]}, {"cell_type": "code", "metadata": {"id": "5b-DE_7huwo4", "colab_type": "code", "colab": {}}, "source": ["import torch\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "var= torch.FloatTensor(scaled).to(device)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "vAm8BF6Nu3eI", "colab_type": "text"}, "source": ["划分训练集，验证集和测试集"]}, {"cell_type": "code", "metadata": {"id": "S5Z9FaVUu7ji", "colab_type": "code", "colab": {}}, "source": ["def splitData(var,per_val,per_test):\n", "    num_val=int(len(var)*per_val)\n", "    num_test=int(len(var)*per_test)\n", "    train_size=int(len(var)-num_val-num_test)\n", "    train_data=var[0:train_size]\n", "    val_data=var[train_size:train_size+num_val]\n", "    test_data=var[train_size+num_val:train_size+num_val+num_test]\n", "    return train_data,val_data,test_data"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "hAbQ9M0pu-6W", "colab_type": "text"}, "source": ["我们的验证集合测试集都取10%"]}, {"cell_type": "code", "metadata": {"id": "12s54oyKu_qb", "colab_type": "code", "colab": {}}, "source": ["train_data,val_data,test_data=splitData(var,0.1,0.1)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "B9MMKedCvBcQ", "colab_type": "text"}, "source": ["查看长度"]}, {"cell_type": "code", "metadata": {"id": "3CL_EC11vDhf", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1598965669386, "user_tz": -60, "elapsed": 15061, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "7b7745dd-dbbc-4287-fadf-3444a36cc4c2"}, "source": ["print('The length of train data, validation data and test data are:',len(train_data),',',len(val_data),',',len(test_data))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The length of train data, validation data and test data are: 14016 , 1752 , 1752\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "xM8DYu0RvGSt", "colab_type": "text"}, "source": ["\n", "取一定大小的窗口进行滑动，每个窗口的label值是窗口下一个预测的第一个空气污染物的值"]}, {"cell_type": "code", "metadata": {"id": "Neov5unqwMkx", "colab_type": "code", "colab": {}}, "source": ["train_window = 240\n", "def create_train_sequence(input_data, tw):\n", "    inout_seq = []\n", "    L = len(input_data)\n", "    for i in range(L-tw):\n", "        train_seq = input_data[i:i+tw]\n", "        train_label = input_data[i+tw:i+tw+1]\n", "        inout_seq.append((train_seq ,train_label))\n", "    return inout_seq"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "Nq2VNCQZwQNb", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1598965669388, "user_tz": -60, "elapsed": 15055, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "0667805e-073c-4e09-f4d7-15fd818db5ae"}, "source": ["train_inout_seq = create_train_sequence(train_data, train_window)\n", "print('The total number of train windows is',len(train_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of train windows is 13776\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "SRKEVERCwOQs", "colab_type": "text"}, "source": ["注意，与上面创建train_data的sequence不同，验证集数据(实验是96个验证集数据)只是label。其数据部分还是需要借助于train集中的数据，大小为一个窗口。而这一个窗口的数据并不会在训练过程中被使用"]}, {"cell_type": "code", "metadata": {"id": "pYIJhKWkwWSW", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1598965669388, "user_tz": -60, "elapsed": 15048, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "bc6a8584-fd41-46b6-9caa-ee66fc881d73"}, "source": ["def create_val_sequence(train_data,val_data, tw):\n", "    temp=torch.cat((train_data,val_data))   #先将训练集和测试集合并\n", "    inout_seq = []\n", "    L = len(val_data)\n", "    for i in range(L):\n", "        val_seq = temp[-(train_window+L)+i:-L+i]\n", "        val_label = test_data[i:i+1]\n", "        inout_seq.append((val_seq ,val_label))\n", "\n", "    return inout_seq\n", "\n", "val_inout_seq = create_val_sequence(train_data, val_data,train_window)\n", "print('The total number of validation windows is',len(val_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of validation windows is 1752\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "WodbuYEG6Inl", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1598965669606, "user_tz": -60, "elapsed": 15260, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "ed8b5c3a-9423-4179-e020-6af9e033fbc2"}, "source": ["val_inout_seq[0][1].shape"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["torch.Si<PERSON>([1, 8])"]}, "metadata": {"tags": []}, "execution_count": 18}]}, {"cell_type": "code", "metadata": {"id": "9Rr2_qTvTKkm", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1598965669607, "user_tz": -60, "elapsed": 15255, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "328db419-1c62-414c-ef24-d81af02ee0d3"}, "source": ["def create_test_sequence(train_data,val_data,test_data, tw):\n", "    temp=torch.cat((train_data,val_data))   #先将训练集和测试集合并\n", "    temp=torch.cat((temp,test_data))\n", "    inout_seq = []\n", "    L = len(test_data)\n", "    for i in range(L):\n", "        test_seq = temp[-(train_window+L)+i:-L+i]\n", "        test_label = test_data[i:i+1]\n", "        inout_seq.append((test_seq ,test_label))\n", "\n", "    return inout_seq\n", "\n", "test_inout_seq = create_test_sequence(train_data, val_data, test_data,train_window)\n", "print('The total number of validation windows is',len(val_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of validation windows is 1752\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "C-1_1zW9wdih", "colab_type": "text"}, "source": ["# 定义LSTM"]}, {"cell_type": "markdown", "metadata": {"id": "sFYIJDyqwhH5", "colab_type": "text"}, "source": ["与单变量lstm不同的是，这次的数据的维度为8维，而单变量只有1维"]}, {"cell_type": "code", "metadata": {"id": "zF00gWiSwgqg", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1598965669607, "user_tz": -60, "elapsed": 15246, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "ff25c010-8f00-4123-e1c1-a822f4dbf224"}, "source": ["train_data.shape"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([14016, 8])"]}, "metadata": {"tags": []}, "execution_count": 20}]}, {"cell_type": "code", "metadata": {"id": "hyM64rs-yieR", "colab_type": "code", "colab": {}}, "source": ["from torch import nn\n", "import torch.nn.init as init\n", "import torch.nn.functional as F\n", "class LSTM(nn.Module):\n", "    def __init__(self,input_size=8,hidden_layer_size=50,output_size=1,num_layers=1):\n", "        super().__init__()\n", "        self.hidden_layer_size=hidden_layer_size\n", "        self.lstm=nn.LSTM(input_size,hidden_layer_size,num_layers,bidirectional=True)\n", "        self.linear1=nn.Linear(hidden_layer_size,hidden_layer_size)\n", "        self.linear2=nn.Linear(hidden_layer_size,output_size)\n", "        self.hidden_cell=(torch.zeros(num_layers*2,1,self.hidden_layer_size),torch.zeros(num_layers*2,1,self.hidden_layer_size))\n", "        init_rnn(self.lstm,'xavier')\n", "        \n", "    def forward(self,input_seq):\n", "        lstm_out, self.hidden_cell = self.lstm(input_seq.reshape(len(input_seq),1,8), self.hidden_cell)\n", "        out=self.linear1(lstm_out.view(len(input_seq)*2, -1))\n", "        out=torch.tanh(out)\n", "        predictions = self.linear2(out)\n", "        return predictions[-1]\n", "\n", "\n", "#设定初始化\n", "def init_rnn(x, type='uniform'):\n", "    for layer in x._all_weights:\n", "        for w in layer:\n", "            if 'weight' in w:\n", "                if type == 'xavier':\n", "                    init.xavier_normal_(getattr(x, w))\n", "                elif type == 'uniform':\n", "                    stdv = 1.0 / math.sqrt(x.hidden_size)\n", "                    init.uniform_(getattr(x, w), -stdv, stdv)\n", "                elif type == 'normal':\n", "                    stdv = 1.0 / math.sqrt(x.hidden_size)\n", "                    init.normal_(getattr(x, w), .0, stdv)\n", "                else:\n", "                    raise ValueError\n"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "Y4wFgdDb3yRu", "colab_type": "text"}, "source": ["# 训练模型"]}, {"cell_type": "markdown", "metadata": {"id": "khFmQ2awIfht", "colab_type": "text"}, "source": ["在训练模型我们需要了解到，该实验是变量预测单变量，根据单变量数量进行多次预测。。。比方说，我们现在有8个attributes,用来预测1个attribute(比如nox)。。。刚开始我是准备8个attributes预测8个attributes，但是实验结果真的是太差了，还不如单变量预测单变量。。。"]}, {"cell_type": "code", "metadata": {"id": "MNLpUCgxTa9e", "colab_type": "code", "colab": {}}, "source": ["import copy\n", "\n", "epochs=5\n", "\n", "\n", "#为了实现多变量预测多个单变量，我这里用了五个LSTM模型\n", "model_nox=LSTM().to(device)\n", "model_no2=LSTM().to(device)\n", "model_no=LSTM().to(device)\n", "model_o3=LSTM().to(device)\n", "model_pm25=LSTM().to(device)\n", "\n", "loss_function=nn.MSELoss()\n", "optimizer_nox = torch.optim.SGD(model_nox.parameters(), lr=0.03,momentum=0.2, weight_decay=6e-4)\n", "optimizer_no2 = torch.optim.SGD(model_no2.parameters(), lr=0.03,momentum=0.4, weight_decay=6e-4)\n", "optimizer_no = torch.optim.SGD(model_no.parameters(), lr=0.03,momentum=0.4, weight_decay=6e-4)\n", "optimizer_o3 = torch.optim.SGD(model_o3.parameters(), lr=0.01,momentum=0.2, weight_decay=6e-4)\n", "optimizer_pm25 = torch.optim.SGD(model_pm25.parameters(), lr=0.03,momentum=0.2, weight_decay=6e-4)\n", "\n", "attr_dic={\n", "    'nox':model_nox,\n", "    'no2':model_no2,\n", "    'no':model_no,\n", "    'o3':model_o3,\n", "    'pm2.5':model_pm25\n", "}\n", "\n", "index_dic={\n", "    'nox':0,\n", "    'no2':1,\n", "    'no':2,\n", "    'o3':3,\n", "    'pm2.5':4\n", "    \n", "}\n", "\n", "optimizer_dic={\n", "    'nox':optimizer_nox,\n", "    'no2':optimizer_no2,\n", "    'no':optimizer_no,\n", "    'o3':optimizer_o3,\n", "    'pm2.5':optimizer_pm25\n", "}\n", "\n", "loss_train_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "loss_val_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "value_train_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "value_val_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "yNE6Evl4i_40", "colab_type": "code", "colab": {}}, "source": ["def train_model(attr,model):\n", "  model.train()\n", "  print('训练',attr,'模型')\n", "  for i in range(epochs):\n", "    #train\n", "    add=0\n", "    for seq,label in train_inout_seq:   \n", "        optimizer_dic[attr].zero_grad()\n", "        seq=seq.to(device)\n", "        label=label.to(device)\n", "        model.hidden_cell = (torch.zeros(2, 1, model.hidden_layer_size).to(device),torch.zeros(2, 1, model.hidden_layer_size).to(device))\n", "        y_pred = model(seq)\n", "\n", "        if(i==epochs-1):  #对最后一次epoch的值进行记录\n", "          value_train_dic[attr].append(y_pred)\n", "\n", "        single_loss = loss_function(y_pred[0], label[0,index_dic[attr]])   #这里只预测label[i]的数值，即某个单一的空气污染物\n", "        add+=single_loss\n", "        single_loss .backward()\n", "        optimizer_dic[attr].step()\n", "    loss_train=add/len(train_inout_seq)\n", "    loss_train_dic[attr].append(loss_train)\n", "\n", "\n", "    #val\n", "    add=0 \n", "    t=0\n", "\n", "    val_inputs=train_data[-train_window:]\n", "    fut_pred = len(val_data)\n", "\n", "    for seq,label in val_inout_seq:\n", "      with torch.no_grad():\n", "        seq = val_inputs[-train_window:].to(device)\n", "        label=label.to(device)\n", "        y_pred=model(seq)\n", "        single_loss=loss_function(y_pred[0],label[0,index_dic[attr]])  \n", "\n", "        add+=single_loss\n", "\n", "        if(i==epochs):  #对最后一次epoch的值进行记录\n", "          value_val_dic[attr].append(y_pred)\n", "\n", "        temp=copy.deepcopy(val_data[t])\n", "        temp[index_dic[attr]]=y_pred\n", "        temp=temp.view(1,-1)\n", "        \n", "        val_inputs=torch.cat((val_inputs,temp),0)\n", "        t+=0\n", "\n", "    loss_val=add/len(val_inout_seq)\n", "    loss_val_dic[attr].append(loss_val)\n", "\n", "    print(f'epoch: {i:3}  train_loss:{loss_train:10.8f} val_loss:{loss_val:10.8f}')\n", "  print('----------------------')"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "bTXI3MxItlVd", "colab_type": "code", "colab": {}}, "source": ["from sklearn.metrics import mean_squared_error\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "def test_model(attr,model):\n", "  temp=torch.cat((train_data,val_data))\n", "  test_inputs=temp[-train_window:,:]\n", "\n", "  fut_pred = len(test_data)\n", "  test_list=[]\n", "  test_results=copy.deepcopy(test_data)\n", "\n", "  model.eval()\n", "\n", "  for i in range(fut_pred):\n", "      seq = test_inputs[-train_window:].to(device)\n", "      with torch.no_grad():\n", "          model.hidden = (torch.zeros(1, 1, model.hidden_layer_size),\n", "                          torch.zeros(1, 1, model.hidden_layer_size))\n", "          y_pred=model(seq)\n", "          temp=copy.deepcopy(test_data[i]).view(1,-1)\n", "          # temp[index_dic[attr]]=y_pred\n", "          # temp=temp.view(1,-1)\n", "          test_inputs=torch.cat((test_inputs,temp),0)\n", "          test_results[i]=y_pred\n", "\n", "\n", "\n", "\n", "  actual_predictions = scaler.inverse_transform(np.array(test_results.cpu()))\n", "\n", "\n", "\n", "\n", "  x = np.arange(len(train_data)+len(val_data), len(dataset), 1)\n", "  plt.figure(figsize=(8, 6))\n", "  plt.grid(True)\n", "\n", "  plt.plot(dataset.loc[len(dataset)-len(test_data):,attr].values,color=\"red\",label='real value')\n", "  plt.plot(actual_predictions[:,index_dic[attr]],label='prediction')\n", "\n", "  plt.title('hours vs '+attr)\n", "  plt.ylabel(attr)\n", "  plt.xlabel('hour')\n", "\n", "  plt.legend(loc='upper right',fontsize=15)\n", "\n", "  y_true=dataset.loc[len(dataset)-len(test_data):,attr].values\n", "  y_pred=actual_predictions[:,index_dic[attr]]\n", "  print('mse: ',mean_squared_error(y_true, y_pred))\n", "  print('mae: ',mean_absolute_error(y_true, y_pred))"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "lWjfxDrJtpZ2", "colab_type": "code", "colab": {}}, "source": ["def predict_future(attr,model):\n", "  temp=torch.cat((train_data,val_data))\n", "  test_inputs=temp[-train_window:,:]\n", "\n", "  fut_pred = 96\n", "  test_list=[]\n", "  test_results=copy.deepcopy(test_data)\n", "\n", "  model.eval()\n", "\n", "  for i in range(fut_pred):\n", "      seq = test_inputs[-train_window:].to(device)\n", "      with torch.no_grad():\n", "          model.hidden = (torch.zeros(1, 1, model.hidden_layer_size),\n", "                          torch.zeros(1, 1, model.hidden_layer_size))\n", "          y_pred=model(seq)\n", "          temp=copy.deepcopy(test_data[i])\n", "          temp[index_dic[attr]]=y_pred\n", "          temp=temp.view(1,-1)\n", "          test_inputs=torch.cat((test_inputs,temp),0)\n", "          test_results[i]=y_pred\n", "\n", "\n", "\n", "\n", "  actual_predictions = scaler.inverse_transform(np.array(test_results.cpu()))\n", "\n", "\n", "\n", "\n", "\n", "  plt.figure(figsize=(8, 6))\n", "  plt.grid(True)\n", "\n", "  plt.plot(dataset.loc[len(dataset)-len(test_data):len(dataset)-len(test_data)+fut_pred,attr].values,color=\"red\",label='real value')\n", "  plt.plot(actual_predictions[:fut_pred,index_dic[attr]],label='prediction')\n", "\n", "  plt.title('hours vs '+attr)\n", "  plt.ylabel(attr)\n", "  plt.xlabel('hour')\n", "\n", "  plt.legend(loc='upper right',fontsize=15)\n", "\n", "  y_true=dataset.loc[len(dataset)-len(test_data):len(dataset)-len(test_data)+fut_pred-1,attr].values\n", "  y_pred=actual_predictions[:fut_pred,index_dic[attr]]\n", "\n", "  print('mse: ',mean_squared_error(y_true, y_pred))\n", "  print('mae: ',mean_absolute_error(y_true, y_pred))\n", "\n", "  y_pred=pd.DataFrame(y_pred)\n", "\n", "  y_pred.to_csv('/content/drive/My Drive/air_inference/result24/bilstm'+attr+'.csv',index=False)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "dcEij6uAMKg5", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 136}, "executionInfo": {"status": "ok", "timestamp": 1598968025130, "user_tz": -60, "elapsed": 798258, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "70ce4525-ad98-4d6d-96ba-61305aae4c30"}, "source": ["train_model('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 nox 模型\n", "epoch:   0  train_loss:0.00101271 val_loss:0.05859372\n", "epoch:   1  train_loss:0.00122110 val_loss:0.05827784\n", "epoch:   2  train_loss:0.00139540 val_loss:0.05807886\n", "epoch:   3  train_loss:0.00151246 val_loss:0.05784544\n", "epoch:   4  train_loss:0.00159955 val_loss:0.05759565\n", "----------------------\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "RRsvxtbiuDaF", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598968036869, "user_tz": -60, "elapsed": 11745, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "e2de0807-5b59-4d4d-ea8d-b186f16a5919"}, "source": ["test_model('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  2750.163879221245\n", "mae:  45.8778982739422\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "xFp9gXvetwqR", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598968037425, "user_tz": -60, "elapsed": 561, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "94892d66-6aad-4e7e-dd1e-b4ca0568def0"}, "source": ["predict_future('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  4441.970272025831\n", "mae:  59.18444957769022\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "gvn89ILdjgYX", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 345}, "executionInfo": {"status": "error", "timestamp": 1598966910902, "user_tz": -60, "elapsed": 1256498, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "6bb1c2c4-18bf-4adb-8940-5fe79689aa4d"}, "source": ["train_model('no2',model_no2)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 no2 模型\n"], "name": "stdout"}, {"output_type": "error", "ename": "KeyboardInterrupt", "evalue": "ignored", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-29-e0047df1bc56>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0mtrain_model\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'no2'\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mmodel_no2\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m<ipython-input-23-a6bce253dffe>\u001b[0m in \u001b[0;36mtrain_model\u001b[0;34m(attr, model)\u001b[0m\n\u001b[1;32m     17\u001b[0m         \u001b[0msingle_loss\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mloss_function\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0my_pred\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlabel\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mindex_dic\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mattr\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m   \u001b[0;31m#这里只预测label[i]的数值，即某个单一的空气污染物\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     18\u001b[0m         \u001b[0madd\u001b[0m\u001b[0;34m+=\u001b[0m\u001b[0msingle_loss\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 19\u001b[0;31m         \u001b[0msingle_loss\u001b[0m \u001b[0;34m.\u001b[0m\u001b[0mbackward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     20\u001b[0m         \u001b[0moptimizer_dic\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mattr\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mstep\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     21\u001b[0m     \u001b[0mloss_train\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0madd\u001b[0m\u001b[0;34m/\u001b[0m\u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtrain_inout_seq\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.6/dist-packages/torch/tensor.py\u001b[0m in \u001b[0;36mbackward\u001b[0;34m(self, gradient, retain_graph, create_graph)\u001b[0m\n\u001b[1;32m    183\u001b[0m                 \u001b[0mproducts\u001b[0m\u001b[0;34m.\u001b[0m \u001b[0mDefaults\u001b[0m \u001b[0mto\u001b[0m\u001b[0;31m \u001b[0m\u001b[0;31m`\u001b[0m\u001b[0;31m`\u001b[0m\u001b[0;32mFalse\u001b[0m\u001b[0;31m`\u001b[0m\u001b[0;31m`\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    184\u001b[0m         \"\"\"\n\u001b[0;32m--> 185\u001b[0;31m         \u001b[0mtorch\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mautograd\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mbackward\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mgradient\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mretain_graph\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcreate_graph\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    186\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    187\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mregister_hook\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mhook\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.6/dist-packages/torch/autograd/__init__.py\u001b[0m in \u001b[0;36mbackward\u001b[0;34m(tensors, grad_tensors, retain_graph, create_graph, grad_variables)\u001b[0m\n\u001b[1;32m    125\u001b[0m     Variable._execution_engine.run_backward(\n\u001b[1;32m    126\u001b[0m         \u001b[0mtensors\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mgrad_tensors\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mretain_graph\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcreate_graph\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 127\u001b[0;31m         allow_unreachable=True)  # allow_unreachable flag\n\u001b[0m\u001b[1;32m    128\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    129\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}]}, {"cell_type": "code", "metadata": {"id": "uiobF6CHvCYW", "colab_type": "code", "colab": {}}, "source": ["test_model('no2',model_no2)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "rGozhTX7tyrF", "colab_type": "code", "colab": {}}, "source": ["predict_future('no2',model_no2)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "5yEx1bQSLxJu", "colab_type": "code", "colab": {}}, "source": ["train_model('no',model_no)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "7xIGo5JwP5eQ", "colab_type": "code", "colab": {}}, "source": ["test_model('no',model_no)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "ZK9QhZxEt0yJ", "colab_type": "code", "colab": {}}, "source": ["predict_future('no',model_no)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "x2tnzs_tQBu1", "colab_type": "code", "colab": {}}, "source": ["train_model('o3',model_o3)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "13s3RdWuULJ3", "colab_type": "code", "colab": {}}, "source": ["test_model('o3',model_o3)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "-l3y1s4it2oO", "colab_type": "code", "colab": {}}, "source": ["predict_future('o3',model_o3)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "4gC2bva0UT_m", "colab_type": "code", "colab": {}}, "source": ["train_model('pm2.5',model_pm25)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "lf7IELqHYaEJ", "colab_type": "code", "colab": {}}, "source": ["test_model('pm2.5',model_pm25)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "R7KukTlzNOCL", "colab_type": "code", "colab": {}}, "source": ["predict_future('pm2.5',model_pm25)"], "execution_count": null, "outputs": []}]}