import argparse
import os
import yaml
from model import EnvironmentViolationDetector

def parse_args():
    parser = argparse.ArgumentParser(description="训练环境违规检测模型")
    parser.add_argument('--data', type=str, default='data.yaml', help='数据集配置文件路径')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    parser.add_argument('--img-size', type=int, default=640, help='图像大小')
    parser.add_argument('--weights', type=str, default='', help='初始权重路径')
    parser.add_argument('--device', type=str, default='', help='训练设备 (如 cuda:0 或 cpu)')
    return parser.parse_args()

def main():
    args = parse_args()
    
    # 确保数据集配置文件存在
    if not os.path.exists(args.data):
        print(f"错误: 数据集配置文件 {args.data} 不存在")
        return
    
    # 打印训练配置
    print("训练配置:")
    print(f"  数据集配置: {args.data}")
    print(f"  训练轮数: {args.epochs}")
    print(f"  批次大小: {args.batch_size}")
    print(f"  图像大小: {args.img_size}px")
    print(f"  初始权重: {'预训练YOLOv8' if not args.weights else args.weights}")
    print(f"  训练设备: {'自动选择' if not args.device else args.device}")
    
    # 初始化模型
    model = EnvironmentViolationDetector(model_path=args.weights if args.weights else None)
    
    # 在自定义数据集上微调模型
    print("\n开始训练...")
    model.fine_tune(
        data_yaml=args.data,
        epochs=args.epochs,
        batch_size=args.batch_size,
        img_size=args.img_size
    )
    
    print("\n训练完成！模型保存在 'runs/detect/train/' 目录中")
    
    # 导出ONNX模型以便部署
    print("\n导出ONNX模型...")
    model.export_model(format="onnx")
    print("ONNX模型导出完成！")

if __name__ == "__main__":
    main() 