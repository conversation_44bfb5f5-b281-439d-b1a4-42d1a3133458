#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合模型污染源反算系统测试脚本
结合高斯烟羽模型、WRF气象模型、遗传算法、模式搜索算法和机器学习
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from suyuan import HybridSourceInversion, visualize_pm25_results
import numpy as np
import pandas as pd

def generate_synthetic_pm25_data(true_source_x=0, true_source_y=0, true_emission_rate=1.39, num_points=15):
    """
    生成合成PM2.5测试数据 - 模拟论文中的实验场景
    参数基于论文中杭州亚运板球场馆的实际情况
    """
    from suyuan import GaussianPlumeModel, AtmosphericStabilityClassifier

    model = GaussianPlumeModel()
    stability_classifier = AtmosphericStabilityClassifier()
    data = []

    print(f"生成合成数据: 真实源位置({true_source_x}, {true_source_y}), 排放率{true_emission_rate} g/s")

    # 模拟论文中6个传感器的布局，扩展到15个点
    sensor_positions = [
        # 基于论文表4的传感器位置，进行适当扩展
        (100, 100), (200, 150), (300, 200), (-150, 120), (-200, -100), (50, -180),
        (250, -50), (-100, 200), (180, 250), (-250, 50), (120, -120), (-80, -200),
        (300, 100), (-200, 150), (80, 300)
    ]

    for i, (x_offset, y_offset) in enumerate(sensor_positions[:num_points]):
        x_r = true_source_x + x_offset
        y_r = true_source_y + y_offset
        z_r = 1.5  # 地面监测高度

        # 模拟论文中的气象条件
        # 风速范围 1-6 m/s，主要是D类稳定度
        u = 1.5 + 2.5 * np.random.random()
        theta_deg = 45 + 30 * np.random.normal()  # 东偏南45°附近

        # 根据风速确定大气稳定度
        stability = stability_classifier.classify_stability(
            u, solar_radiation='中', is_daytime=True
        )

        # 计算真实浓度
        true_conc = model.compute_concentration(
            true_source_x, true_source_y, 10.0, true_emission_rate,
            u, theta_deg, stability, x_r, y_r, z_r
        )

        # 添加观测噪声（模拟传感器误差）
        noise_factor = 1 + 0.15 * np.random.normal()  # 15%的观测噪声
        observed_conc = max(0.1, true_conc * noise_factor)  # 确保非负且有最小值

        data.append((x_r, y_r, z_r, observed_conc, u, theta_deg, stability))

        print(f"传感器{i+1}: 位置({x_r:.0f}, {y_r:.0f}), 浓度{observed_conc:.2f} μg/m³, "
              f"风速{u:.1f} m/s, 风向{theta_deg:.0f}°, 稳定度{stability}")

    return data, (true_source_x, true_source_y, true_emission_rate)

def generate_sensor_network_data():
    """生成模拟传感器网络数据用于数据预处理测试"""
    num_sensors = 6
    num_points = 1000  # 模拟1000个时间点的数据

    sensor_network_data = {}

    for sensor_id in range(1, num_sensors + 1):
        # 生成基础数据
        pm25_base = 50 + 30 * np.sin(np.linspace(0, 4*np.pi, num_points)) + 10 * np.random.randn(num_points)
        pm10_base = pm25_base * 1.5 + 5 * np.random.randn(num_points)

        # 添加一些异常值
        anomaly_indices = np.random.choice(num_points, size=int(0.02 * num_points), replace=False)
        pm25_base[anomaly_indices[:len(anomaly_indices)//2]] = -1  # 负值异常
        pm25_base[anomaly_indices[len(anomaly_indices)//2:]] = 9999  # 超量程异常

        # 添加一些缺失值
        missing_indices = np.random.choice(num_points, size=int(0.02 * num_points), replace=False)
        pm25_base[missing_indices] = np.nan

        sensor_network_data[f'sensor_{sensor_id}'] = {
            'PM2.5': pm25_base,
            'PM10': pm10_base,
            'temperature': 20 + 10 * np.sin(np.linspace(0, 2*np.pi, num_points)) + 2 * np.random.randn(num_points),
            'humidity': 60 + 20 * np.sin(np.linspace(0, 2*np.pi, num_points)) + 5 * np.random.randn(num_points),
            'wind_speed': 2 + np.random.exponential(1, num_points),
            'wind_direction': 45 + 30 * np.random.randn(num_points),
            'timestamp': pd.date_range('2024-10-01', periods=num_points, freq='15min')
        }

    return sensor_network_data

def test_data_preprocessing():
    """测试数据预处理功能"""
    print("=== 数据预处理模块测试 ===")

    # 1. 生成模拟传感器网络数据
    print("1. 生成模拟传感器网络数据...")
    sensor_data = generate_sensor_network_data()

    # 2. 测试数据预处理
    print("2. 测试数据预处理...")
    from suyuan import DataPreprocessor

    preprocessor = DataPreprocessor()
    processed_data = preprocessor.preprocess_sensor_network_data(sensor_data)

    # 3. 输出预处理结果
    print("\n预处理结果:")
    for sensor_id, data in processed_data.items():
        original_pm25 = sensor_data[sensor_id]['PM2.5']
        processed_pm25 = data['PM2.5']

        original_valid = np.sum(~np.isnan(original_pm25) & (original_pm25 >= 0) & (original_pm25 < 9000))
        processed_valid = np.sum(~np.isnan(processed_pm25) & (processed_pm25 >= 0))

        print(f"{sensor_id}: 原始有效数据 {original_valid}, 预处理后有效数据 {processed_valid}")

    print("数据预处理测试完成！\n")

def test_hybrid_system():
    """测试混合模型系统"""
    print("=== 混合模型污染源反算系统测试 ===\n")

    # 1. 生成测试数据
    print("1. 生成合成测试数据...")
    test_data, true_params = generate_synthetic_pm25_data(
        true_source_x=0, true_source_y=0, true_emission_rate=1.39, num_points=15
    )

    print(f"真实源参数: 位置({true_params[0]}, {true_params[1]}), 排放率{true_params[2]} g/s")
    print(f"生成了 {len(test_data)} 个观测点\n")

    # 2. 创建混合模型系统
    print("2. 初始化混合模型系统...")
    hybrid_system = HybridSourceInversion()

    # 3. 设置搜索边界（基于论文实验设置）
    search_bounds = [(-500, 500), (-500, 500), (0.1, 5.0)]  # x, y, q的搜索范围

    # 4. 运行遗传-模式搜索算法
    print("3. 运行遗传-模式搜索算法...")
    try:
        best_solution, best_fitness, ga_log = hybrid_system.genetic_algorithm_with_pattern_search(
            test_data,
            population_size=30,  # 较小的种群用于测试
            generations=50,      # 较少的代数用于测试
            search_bounds=search_bounds
        )

        # 5. 双向验证
        print("\n4. 执行双向验证...")
        validation_results = hybrid_system.dual_validation_mechanism(best_solution, test_data)

        # 6. 输出结果
        print(f"\n=== 分析结果 ===")
        print(f"真实源位置: ({true_params[0]}, {true_params[1]})")
        print(f"估计源位置: ({best_solution[0]:.1f}, {best_solution[1]:.1f})")
        position_error = np.sqrt((best_solution[0] - true_params[0])**2 + (best_solution[1] - true_params[1])**2)
        print(f"位置误差: {position_error:.1f} m")

        print(f"\n真实排放率: {true_params[2]} g/s")
        print(f"估计排放率: {best_solution[2]:.3f} g/s")
        emission_error = abs(best_solution[2] - true_params[2]) / true_params[2] * 100
        print(f"排放率相对误差: {emission_error:.1f}%")

        print(f"\n模型性能:")
        print(f"目标函数值: {best_fitness:.6f}")
        print(f"验证RMSE: {validation_results['rmse']:.3f}")
        print(f"验证MAE: {validation_results['mae']:.3f}")
        print(f"验证相关系数: {validation_results['correlation']:.3f}")

        # 7. 分析收敛性
        print(f"\n算法收敛性:")
        if ga_log:
            final_gen = ga_log[-1]
            print(f"最终代数: {final_gen['generation']}")
            print(f"最大适应度: {final_gen['max_fitness']:.6f}")
            print(f"平均适应度: {final_gen['avg_fitness']:.6f}")

        # 8. 可视化结果
        print("\n5. 生成可视化结果...")
        try:
            # 准备结果字典用于可视化
            viz_results = {
                'source_location': best_solution[:2],
                'emission_rate': best_solution[2],
                'fitness': best_fitness,
                'validation': validation_results,
                'ga_log': ga_log
            }

            # 提取传感器位置
            sensor_positions = [(data_point[0], data_point[1]) for data_point in test_data]

            # 调用专门的PM2.5可视化函数
            visualize_pm25_results(viz_results, sensor_positions)

        except Exception as e:
            print(f"可视化失败: {e}")
            print("跳过可视化步骤")

        return True

    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_components():
    """测试各个组件"""
    print("\n=== 组件测试 ===")
    
    # 测试WRF气象模型
    print("1. 测试WRF气象模型...")
    from suyuan import WRFMeteorologicalModel
    wrf = WRFMeteorologicalModel()
    u, theta, stability = wrf.get_meteorological_data(100, 100, 0)
    print(f"气象数据: 风速={u:.2f} m/s, 风向={theta:.1f}°, 稳定度={stability}")
    
    # 测试高斯烟羽模型
    print("\n2. 测试高斯烟羽模型...")
    from suyuan import GaussianPlumeModel
    gaussian = GaussianPlumeModel()
    conc = gaussian.compute_concentration(500, 500, 10, 5.0, 2.0, 45, 'D', 600, 600, 1.5)
    print(f"计算浓度: {conc:.6f}")
    
    # 测试扩散轨迹模拟
    print("\n3. 测试扩散轨迹模拟...")
    trajectory = gaussian.simulate_dispersion_trajectory(500, 500, 10, 5.0, time_steps=5)
    print(f"轨迹模拟完成，时间步数: {len(trajectory)}")
    
    # 测试机器学习增强器
    print("\n4. 测试机器学习增强器...")
    from suyuan import MachineLearningEnhancer
    ml_enhancer = MachineLearningEnhancer()
    
    # 生成训练数据
    training_data = []
    for i in range(20):
        x_s, y_s, Q = 500, 500, 5.0
        x_r = 400 + 200 * np.random.random()
        y_r = 400 + 200 * np.random.random()
        z_r = 1.5
        u = 2.0 + np.random.random()
        theta_deg = 45 + 10 * np.random.random()
        stability = np.random.choice(['C', 'D', 'E'])
        
        # 使用高斯模型计算"真实"浓度
        true_conc = gaussian.compute_concentration(x_s, y_s, 10, Q, u, theta_deg, stability, x_r, y_r, z_r)
        training_data.append((x_s, y_s, Q, x_r, y_r, z_r, u, theta_deg, stability, true_conc))
    
    ml_enhancer.train(training_data)
    
    # 测试预测
    pred_conc = ml_enhancer.predict(500, 500, 5.0, 600, 600, 1.5, 2.0, 45, 'D')
    print(f"机器学习预测浓度: {pred_conc:.6f}")
    
    print("\n所有组件测试完成！")

if __name__ == "__main__":
    print("开始测试基于遗传-模式搜索算法的微尺度管控区域PM2.5溯源系统...\n")

    # 测试数据预处理
    test_data_preprocessing()

    # 测试各个组件
    test_individual_components()

    # 测试完整系统
    success = test_hybrid_system()

    if success:
        print("\n✅ 测试成功完成！")
        print("系统已准备好用于微尺度管控区域的PM2.5污染源溯源分析")
    else:
        print("\n❌ 测试过程中出现问题")
