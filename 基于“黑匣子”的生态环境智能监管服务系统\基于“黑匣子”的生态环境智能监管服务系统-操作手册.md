**基于“黑匣子”的生态环境智能监管服务系统V1.0**

操作说明书

# 目录

[1.登录与退出 [1](#登录与退出)](#登录与退出)

> [1.1登录 [1](#登录)](#登录)
>
> [1.2退出 [2](#退出)](#退出)

[2.首页 [2](#首页)](#首页)

> [2.1算法管理 [2](#算法管理)](#算法管理)
>
> [2.2 AI视频监控可视化平台 [6](#ai视频监控可视化平台)](#ai视频监控可视化平台)
>
> [2.3 AI环保工程师 [7](#ai环保工程师)](#ai环保工程师)

[3.边缘盒子 [10](#边缘盒子)](#边缘盒子)

> [3.1点位管理 [10](#点位管理)](#点位管理)
>
> [3.2盒子管理 [13](#盒子管理)](#盒子管理)
>
> [3.3视频预览 [16](#视频预览)](#视频预览)

[4.告警管理 [19](#告警管理)](#告警管理)

[5.音柱管理 [28](#音柱管理)](#音柱管理)

[6.推送管理 [31](#推送管理)](#推送管理)

> [6.1工作推送 [31](#工作推送)](#工作推送)
>
> [6.2接口推送 [33](#接口推送)](#接口推送)

[7.标注中心 [35](#标注中心)](#标注中心)

> [7.1 标注统计 [35](#标注统计)](#标注统计)
>
> [7.2 标注组 [38](#标注组)](#标注组)
>
> [7.3 标注项目 [43](#标注项目)](#标注项目)
>
> [7.4 增量训练 [45](#增量训练)](#增量训练)

[8.系统管理 [48](#系统管理)](#系统管理)

> [8.1 组织账号管理 [48](#组织账号管理)](#组织账号管理)
>
> [8.2 角色管理 [53](#角色管理)](#角色管理)
>
> [8.3 菜单管理 [54](#菜单管理)](#菜单管理)
>
> [8.4 算法测试 [57](#算法测试)](#算法测试)

# 1.登录与退出

## 1.1登录

输入用户名、密码。

![](media/image1.png)

## 1.2退出

点击右上角用户名称下拉菜单的【退出登录】，退出当前登录账号。

![](media/image2.png)

# 2.首页

## 2.1算法管理

1.  进入【算法管理】查看各类算法以及对应的使用场景

> ![](media/image3.png)

2.  点击搜索场景右侧的【搜索】，可以搜索相应的算法卡片

3.  点击搜索场景右侧的【新增】，可以添加算法编码、算法描述、卡片图片和卡片描述

> ![](media/image4.png)

4.  点击算法卡片下的【编辑】，可以更改算法的名称，图片和描述

> ![](media/image5.png)

5.  点击算法卡片下的【下载算法】，可以看到盒子名称、盒子编码、已下载算法版本、算法最新版本和可下载算法版本

> ![](media/image6.png)

6.  点击【下载】，可下载最新算法。

> ![](media/image7.png)

7.  点击算法卡片下的【更多】，可以选择删除该算法卡片

> ![](media/image8.png)

## 2.2 AI视频监控可视化平台

1.  点击【进入大屏】，进入AI视频监控可视化平台，在此界面可以看到多种数据的可视化效果，包括摄像头管理、本月预警时间前十统计、告警列表、系统统计、今日告警统计、违规行为截图记录、视频轮询和告警分析

![](media/image9.png)

2.  点击视频轮询旁【已屏】，可以将大屏切换为直播放一个实时视频画面

![](media/image10.png)

## 2.3 AI环保工程师

1.  点击首页【AI环保工程师】，进入AI环保工程师界面，用户可以在此页进行提问，并可以切换不同场景

![](media/image11.png)

2.  点击【视频识别】，选择实时识别后，进入实时视频画面

![](media/image12.png)

3.  点击【视频识别】，选择图片/视频，识别图片或者视频

![](media/image13.png)

4.  点击【数字人】，进入数字人界面，可以和数字人进行交互

![](media/image14.png)

![](media/image15.png)

# 3.边缘盒子

## 3.1点位管理

1.  进入【点位管理】页面后，可以看到左侧三个盒子分别为测试101、拱墅试点1和滨江试点136，右侧为所有摄像头，包括序号、摄像头名称、使用开关，运行状态、关联算法数量和可执行操作。

> ![](media/image16.png)

2.  点击【测试101】，可以看到序号、摄像头名称、使用开关，运行状态、关联算法数量和可执行操作

> ![](media/image17.png)

3.  点击【编辑】，可以对摄像头的各项参数进行编辑

> ![](media/image18.png)

4.  点击【删除】，会弹出警告，确认是否删除摄像头

> ![](media/image19.png)

5.  点击【新增摄像头】，可以填入新增摄像头的各项参数

> ![](media/image20.png)

6.  点击【搜索】，可以根据摄像头序号检索对应摄像头

7.  点击【拱墅试点1】，可以看到序号、摄像头名称、使用开关，运行状态、关联算法数量和可执行操作

> ![](media/image21.png)

8.  点击【滨江试点136】，以看到序号、摄像头名称、使用开关，运行状态、关联算法数量和可执行操作

> ![](media/image22.png)

## 3.2盒子管理

1.点击【盒子管理】后，可以看到序号、盒子名称、盒子编号、盒子路数，IP地址、所属组织、可执行操作

![](media/image23.png)

2.通过输入名称或者选择所属组织，点击【搜索】可以找到对应的盒子

![](media/image24.png)

3.点击【重置】，即可重置搜索结果将全部盒子展示

![](media/image25.png)

4.点击【新增盒子】后，可以对盒子的各项参数进行编辑

![](media/image26.png)

5.点击【编辑】，可以编辑盒子的各项参数

![](media/image27.png)

6.点击【删除】，会弹出警告，确认是否删除盒子

![](media/image28.png)

## 3.3视频预览

1.点击【视频预览】，会有实时视频流，右侧是实时数据和弹窗

![](media/image29.png)

2.点击【四屏】，将实时视频画面分为四块

![](media/image30.png)

3.点击【六屏】，将实时视频画面分为六块

![](media/image31.png)

4.关闭右侧【弹窗】，界面中不再实时弹出告警信息

![](media/image32.png)

5.点击下方的页数，可以切换不同地点的实时视频画面

![](media/image33.png)

# 4.告警管理

1.  点击【全部】，可以看到所有的告警信息，每条报警信息包含盒子名称、摄像头名称、报警类型和报警时间

> ![](media/image34.png)

2.  点击【行人检测】，可以看到属于行人检测类型的报警信息

> ![](media/image35.png)

3.  点击【黑烟检测】，可以看到属于黑烟检测类别的报警信息

> ![](media/image36.png)

4.  点击【扬尘检测】，可以看到属于扬尘检测类别的报警信息

> ![](media/image37.png)

5.  点击【裸土检测】，可以看到属于裸土检测类别的报警信息

> ![](media/image38.png)

6.  点击【反光衣检测】，可以看到属于反光衣检测类别的报警信息

> ![](media/image39.png)

7.  点击【未戴安全帽】检测，可以看到属于未戴安全帽检测类别的报警信息

> ![](media/image40.png)

8.  点击【危险区域闯入识别】，可以看到属于危险区域闯入识别类别的的、报警信息

> ![](media/image41.png)

9.  点击【烟火】检测，可以看到属于烟火检测类别的报警信息

> ![](media/image42.png)

10. 点击【消防车道占用】，可以看到属于消防车道占用类别的报警信息

> ![](media/image43.png)

11. 点击【明火检测】，可以看到属于明火检测类别的报警信息

> ![](media/image44.png)

12. 点击【开关门检测】，可以看到属于开关门检测类别的报警信息

> ![](media/image45.png)

13. 点击【违规喷漆检测】，可以看到属于违规喷漆检测类别的报警信息

> ![](media/image46.png)

14. 点击【电子围栏】，可以看到属于电子围栏类别的报警信息

> ![](media/image47.png)

15. 通过选择所属组织，摄像头和时间，点击【搜索】，可以搜索对应的报警信息

> ![](media/image48.png)

16. 点击【重置】，即可清空搜索条件展示出所有的报警信息

> ![](media/image49.png)

17. 点击【6图】，即可将默认的展示4张图片变为展示6张图片

> ![](media/image50.png)

18. 点击【12图】，切换为展示12张图片

> ![](media/image51.png)

19. 点击【数据保存期限】，可以选择数据保存的时间，默认为近365天

> ![](media/image52.png)

20. 点击【数据导出】，可以将数据导出到本地

21. 点击下方的页数，可以查看更多的报警信息

# 5.音柱管理

1.  点击【音柱管理】后，可查看组织对应的音柱服务，包括序号、设备名称、是否开启、设备音量和可执行操作

> ![](media/image53.png)

2.  点击【新增】，可以新增音柱服务，包括服务地址和服务名称

> ![](media/image54.png)

3.  点击音柱对应的【是否开启】，可以选择开启或关闭音柱

> ![](media/image55.png)

4.  滑动【设备音量】按钮，可以调节音柱音量

> ![](media/image56.png)

5.  点击【修改名称】，可以修改音柱的名称

> ![](media/image57.png)

6.  点击【绑定组织】，可以选择该音柱的所属组织

> ![](media/image58.png)

7.  点击连微-AI创新后的【编辑】，可以修改对应企业名称

# 6.推送管理

## 6.1工作推送

1.点击【工作推送】后，进入工作推送界面

![](media/image59.png)

2.可在此界面输入【企业微信群Webhook地址】和【钉钉群Webhook地址】和【加密（选填）】签名

3.点击【启用】将工作推送到企业微信

![](media/image60.png)

4.点击【启用】将工作推送到钉钉群

![](media/image61.png)

5.点击【提交】可以确认是否完成修改

![](media/image62.png)

## 6.2接口推送

1.点击【接口推送】后，进入接口推送界面

![](media/image63.png)

2.可以在【第三方接口地址】填入http接口

3.点击【启用】或【不启用】可以设置接口推送状态

![](media/image64.png)

4.点击【提交】确认修改

![](media/image65.png)

5．点击【下载示例文档】，可以将接口示例文档下载到本地

![](media/image66.png)

# 7.标注中心

## 7.1 标注统计

1.  点击【标注统计】，可以查看标注情况的折线图

> ![](media/image67.png)

2.  点击【图片数量】，可以查看图片数量的标注情况

> ![](media/image68.png)

3.  点击【标注框数量】，可以查看标注框的标注情况

> ![](media/image69.png)

4.  点击【合格数量】，可以查看标注的合格情况

> ![](media/image70.png)

5.  点击【不合格数量】，可以查看标注的不合格数量

> ![](media/image71.png)

6.  选择项目，可以查看不同项目的标注情况

> ![](media/image72.png)

## 7.2 标注组

1.  点击【标注组】，可以查看进行标注工作的小组，包括序号、组名、成员数、创建时间和可执行操作

> ![](media/image73.png)

2.  点击【搜索】，可根据条件搜索对应小组

> ![](media/image74.png)

3.  点击【重置】，清空搜索条件

> ![](media/image75.png)

4.  点击【新增分组】，可以增加新的小组

> ![](media/image76.png)

5.  点击【编辑】，可以修改对应组名

> ![](media/image77.png)

6.  点击分配成员进入成员分配界面，包括序号、用户ID、用户名称、用户角色、创建时间和可执行操作

> ![](media/image78.png)

7.  点击新增用户，可以向小组内添加用户，在此界面点击【搜索】，可以寻找相应用户，点击【重置】可以清空搜索条件

> ![](media/image79.png)
>
> ![](media/image80.png)

8.  点击【分配角色】，可以选择将用户设置为管理员、标注员或质检员

> ![](media/image81.png)

9.  点击【删除】，可以删除相应用户

> ![](media/image82.png)

10. 在标注组界面点击【删除】，弹出警告确认删除标注组

> ![](media/image83.png)

## 7.3 标注项目

1.  点击【标注项目】，可以查看各个项目的标注情况，包括项目名称、项目状态、标注进度，图片数量，创建人和创建时间

> ![](media/image84.png)

2.  点击【新增项目】，填入基本信息、标签管理、其它信息和上传数据集后，可以增加标注项目

> ![](media/image85.png)

3.  点击【标注示例】，可以查看标注示例文档和上传示例

> ![](media/image86.png)

4.  通过选择标注情况和输入名称，可以搜索到对应的项目

> ![](media/image87.png)

5.  点击项目下方的【手指】按钮，可以对图片进行标注

> ![](media/image88.png)

6.  点击项目下方的【下载】按钮，可以将该项目的标注结果下载到本地

> ![](media/image89.png)

7.  点击项目下方的【删除】按钮，可以选择是否删除该项目

> ![](media/image90.png)

## 7.4 增量训练

1.  点击【增量训练】，可以查看各个报警类型的图片，包括序号、告警类型、图片数量，摄像机名称、图片预览、更新时间和可执行操作

> ![](media/image91.png)

2.  点击【搜索】，可以通过输入检索对应的类型

> ![](media/image92.png)

3.  点击【重置】，清空搜索条件

> ![](media/image93.png)

4.  点击【预览】，可以查询对应的图片

> ![](media/image94.png)

5.  点击【创建项目】，填入基本信息、标签管理、其它信息和上传数据集后，可以创建对应的项目

> ![](media/image95.png)

# 8.系统管理

## 8.1 组织账号管理

1.  点击【组织账号管理】，可以查看组织和对应的成员

> ![](media/image96.png)

2.  点击【全部】，可以查看所有用户，包括序号、用户姓名、手机号、账号名称、角色、所属组织、是否启用和可执行操作

> ![](media/image97.png)

3.  点击左侧大气事业部、AI创新实验室、滨江区，可以看到属于这些组织的用户

> ![](media/image98.png)
>
> ![](media/image99.png)
>
> ![](media/image100.png)

4.  点击组织管理【编辑】，可以对组织进行编辑。点击【添加】可以增加部门，点击【编辑】，可以修改部门名称，点击【删除】可以删除对应数据

> ![](media/image101.png)
>
> ![](media/image102.png)
>
> ![](media/image103.png)
>
> ![](media/image104.png)

5.  点击【搜索】，可以根据填入的条件搜索对应用户

6.  点击【重置】清除所有条件

> ![](media/image105.png)

7.  点击【新增用户】，填入用户信息后可以增加用户

> ![](media/image106.png)

8.  点击用户操作下的【编辑】，可以编辑用户信息并改变帐号状态

> ![](media/image107.png)

9.  点击用户操作下的【删除】，可以将该用户删除

## 8.2 角色管理

1.  点击【角色管理】，可以查看不同的角色信息，包括角色名称、角色编码、关联菜单和盒子管理

> ![](media/image108.png)

2.  点击盒子管理下的【选择盒子】，可以看到盒子的各项参数，可以选择绑定盒子

> ![](media/image109.png)

3.  点击【创建角色】，可以填入角色名称、角色编码、关联菜单和盒子管理以创建新的角色

> ![](media/image110.png)

4.  点击角色旁【编辑】，可以编辑角色参数

## 8.3 菜单管理

1.  点击【菜单管理】，可以看到菜单信息，包括菜单名称、权重、路由地址、类型、菜单编码和可执行操作

> ![](media/image111.png)

2.  点击【查询】，可以搜索到相应内容

3.  点击【重置】，清除所有条件

> ![](media/image112.png)

4.  点击【新增】，可以编辑新菜单的各项参数并增加新的菜单

> ![](media/image113.png)

5.  点击菜单对应操作下的【编辑】，可以编辑菜单的类型、名称、路由、权重和编码

> ![](media/image114.png)

6.  点击菜单对应操作下的【删除】，可以确认是否删除该菜单

> ![](media/image115.png)

7.  点击菜单对应操作下的【新增下级】，可以为该菜单增加下级

> ![](media/image116.png)

## 8.4 算法测试

1.  点击【算法测试】，可以查看算法测试具体情况，包括序号、任务名称、任务状态、媒体类型、生成算法测试媒体地址、创建时间和可执行操作

> ![](media/image117.png)

2.  点击【搜索】，可以搜索到对应条件的任务

> ![](media/image118.png)

3.  点击【重置】，清除条件

> ![](media/image119.png)

4.  点击【新增算法测试】，填入参数后，可以新增任务测试

> ![](media/image120.png)

5.  点击【配置测试盒子】，配置盒子地址

> ![](media/image121.png)

6.  点击对应任务操作下的【执行】，可以执行测试任务

> ![](media/image122.png)

7.  点击对应任务操作下的【编辑】，可以编辑任务名称、媒体类型和算法

> ![](media/image123.png)

8.  点击对应任务操作下的【删除】，可以删除该记录

> ![](media/image124.png)
