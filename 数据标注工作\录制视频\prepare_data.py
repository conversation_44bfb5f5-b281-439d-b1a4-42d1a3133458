import os
import argparse
import shutil
import random
import yaml
from tqdm import tqdm

def parse_args():
    parser = argparse.ArgumentParser(description="准备环境违规检测模型的训练数据")
    parser.add_argument('--data_dir', type=str, required=True, help='包含图像和标注的数据目录')
    parser.add_argument('--output_dir', type=str, default='dataset', help='输出数据集目录')
    parser.add_argument('--split', type=float, nargs=3, default=[0.7, 0.2, 0.1], 
                       help='训练集、验证集、测试集的比例，默认为[0.7, 0.2, 0.1]')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    return parser.parse_args()

def create_dataset_structure(output_dir):
    """创建YOLOv8数据集结构"""
    # 创建主目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建训练、验证和测试子目录
    for split in ['train', 'val', 'test']:
        os.makedirs(os.path.join(output_dir, split, 'images'), exist_ok=True)
        os.makedirs(os.path.join(output_dir, split, 'labels'), exist_ok=True)
    
    print(f"创建数据集目录结构: {output_dir}")

def copy_files(file_list, src_dir, dst_dir, file_type):
    """复制文件到目标目录"""
    for filename in tqdm(file_list, desc=f"复制{file_type}文件"):
        src_path = os.path.join(src_dir, filename)
        dst_path = os.path.join(dst_dir, filename)
        shutil.copy2(src_path, dst_path)

def split_dataset(data_dir, output_dir, split_ratio, seed=42):
    """分割数据集为训练集、验证集和测试集"""
    # 设置随机种子以确保可重复性
    random.seed(seed)
    
    # 图像和标注目录
    img_dir = os.path.join(data_dir, 'images')
    label_dir = os.path.join(data_dir, 'labels')
    
    # 检查目录是否存在
    if not os.path.exists(img_dir):
        raise FileNotFoundError(f"图像目录不存在: {img_dir}")
    if not os.path.exists(label_dir):
        raise FileNotFoundError(f"标注目录不存在: {label_dir}")
    
    # 获取所有图像文件
    img_files = [f for f in os.listdir(img_dir) if f.endswith(('.jpg', '.jpeg', '.png'))]
    print(f"找到 {len(img_files)} 个图像文件")
    
    # 随机打乱文件列表
    random.shuffle(img_files)
    
    # 计算每个集合的大小
    train_size = int(len(img_files) * split_ratio[0])
    val_size = int(len(img_files) * split_ratio[1])
    
    # 分割数据集
    train_files = img_files[:train_size]
    val_files = img_files[train_size:train_size + val_size]
    test_files = img_files[train_size + val_size:]
    
    print(f"训练集: {len(train_files)} 张图像")
    print(f"验证集: {len(val_files)} 张图像")
    print(f"测试集: {len(test_files)} 张图像")
    
    # 复制图像文件
    copy_files(train_files, img_dir, os.path.join(output_dir, 'train', 'images'), '训练图像')
    copy_files(val_files, img_dir, os.path.join(output_dir, 'val', 'images'), '验证图像')
    copy_files(test_files, img_dir, os.path.join(output_dir, 'test', 'images'), '测试图像')
    
    # 复制对应的标注文件
    train_labels = [os.path.splitext(f)[0] + '.txt' for f in train_files]
    val_labels = [os.path.splitext(f)[0] + '.txt' for f in val_files]
    test_labels = [os.path.splitext(f)[0] + '.txt' for f in test_files]
    
    copy_files(train_labels, label_dir, os.path.join(output_dir, 'train', 'labels'), '训练标注')
    copy_files(val_labels, label_dir, os.path.join(output_dir, 'val', 'labels'), '验证标注')
    copy_files(test_labels, label_dir, os.path.join(output_dir, 'test', 'labels'), '测试标注')

def create_data_yaml(output_dir):
    """创建数据集配置文件"""
    yaml_path = os.path.join(output_dir, 'data.yaml')
    
    data = {
        'path': os.path.abspath(output_dir),
        'train': 'train/images',
        'val': 'val/images',
        'test': 'test/images',
        'nc': 7,
        'names': [
            '裸土未覆盖',
            '工地扬尘',
            '土方作业未降尘',
            '夜间违规施工',
            '露天烧烤',
            '垃圾焚烧',
            '渣土车未覆盖'
        ]
    }
    
    with open(yaml_path, 'w', encoding='utf-8') as f:
        yaml.dump(data, f, allow_unicode=True, default_flow_style=False)
    
    print(f"创建数据集配置文件: {yaml_path}")
    return yaml_path

def main():
    args = parse_args()
    
    # 验证分割比例之和是否为1
    if sum(args.split) != 1.0:
        print("警告: 分割比例之和不为1，将进行归一化处理")
        total = sum(args.split)
        args.split = [ratio / total for ratio in args.split]
    
    print("数据集准备配置:")
    print(f"  数据目录: {args.data_dir}")
    print(f"  输出目录: {args.output_dir}")
    print(f"  分割比例: 训练={args.split[0]:.1f}, 验证={args.split[1]:.1f}, 测试={args.split[2]:.1f}")
    print(f"  随机种子: {args.seed}")
    
    # 创建数据集目录结构
    create_dataset_structure(args.output_dir)
    
    # 分割数据集
    split_dataset(args.data_dir, args.output_dir, args.split, args.seed)
    
    # 创建数据集配置文件
    yaml_path = create_data_yaml(args.output_dir)
    
    print("\n数据集准备完成!")
    print(f"数据集位置: {args.output_dir}")
    print(f"配置文件: {yaml_path}")
    print("\n您可以使用以下命令开始训练:")
    print(f"python train.py --data {yaml_path} --epochs 100 --batch-size 16 --img-size 640")

if __name__ == "__main__":
    main() 