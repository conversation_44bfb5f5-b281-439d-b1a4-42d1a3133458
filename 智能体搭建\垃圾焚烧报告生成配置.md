# 垃圾焚烧企业数据分析报告生成配置

## 一、Dify平台所需插件和工具

### 1. 必需的内置工具
- **text2sql工具**：将自然语言转换为SQL查询语句
- **sql_execute工具**：执行SQL查询并返回结果数据
- **代码执行工具**：用于数据处理和图表生成

### 2. 推荐的第三方插件
- **Mermaid图表插件**：用于生成工艺流程图
- **ECharts图表插件**：用于生成各类数据可视化图表
- **Markdown渲染插件**：确保报告格式正确显示
- **PDF导出插件**：支持将报告导出为PDF格式

### 3. 自定义代码工具配置

#### Python数据处理代码模板
```python
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json

def process_furnace_data(query_result):
    """处理炉号数据并生成统计信息"""
    df = pd.DataFrame(query_result)
    
    # 基础统计
    stats = {
        'total_records': len(df),
        'furnace_count': len(df['unit_no'].unique()) if 'unit_no' in df.columns else 0,
        'date_range': {
            'start': df['data_date'].min() if 'data_date' in df.columns else None,
            'end': df['data_date'].max() if 'data_date' in df.columns else None
        }
    }
    
    # 按炉号统计
    furnace_stats = {}
    if 'unit_no' in df.columns:
        for unit_no in df['unit_no'].unique():
            unit_data = df[df['unit_no'] == unit_no]
            furnace_stats[f'炉{unit_no}'] = {
                'records': len(unit_data),
                'avg_pm': unit_data['pm_daily'].mean() if 'pm_daily' in unit_data.columns else 0,
                'avg_nox': unit_data['nox_daily'].mean() if 'nox_daily' in unit_data.columns else 0,
                'exceed_days': unit_data['exceed_flag'].sum() if 'exceed_flag' in unit_data.columns else 0
            }
    
    return stats, furnace_stats

def generate_echarts_config(data, chart_type='multi_line'):
    """生成ECharts图表配置"""
    if chart_type == 'temperature_trend':
        return {
            "title": {"text": "各炉号炉膛温度变化趋势"},
            "tooltip": {"trigger": "axis"},
            "legend": {"data": ["1#炉", "2#炉", "3#炉"]},
            "xAxis": {"type": "category", "data": data.get('dates', [])},
            "yAxis": {"type": "value", "name": "温度(℃)"},
            "series": [
                {"name": "1#炉", "type": "line", "data": data.get('temp_1', [])},
                {"name": "2#炉", "type": "line", "data": data.get('temp_2', [])},
                {"name": "3#炉", "type": "line", "data": data.get('temp_3', [])}
            ]
        }
    elif chart_type == 'pollutant_concentration':
        return {
            "title": {"text": "污染物浓度变化曲线"},
            "tooltip": {"trigger": "axis"},
            "legend": {"data": ["NOx", "SO₂", "HCl", "CO", "烟尘"]},
            "xAxis": {"type": "category", "data": data.get('dates', [])},
            "yAxis": {"type": "value", "name": "浓度(mg/Nm³)"},
            "series": [
                {"name": "NOx", "type": "line", "data": data.get('nox', [])},
                {"name": "SO₂", "type": "line", "data": data.get('so2', [])},
                {"name": "HCl", "type": "line", "data": data.get('hcl', [])},
                {"name": "CO", "type": "line", "data": data.get('co', [])},
                {"name": "烟尘", "type": "line", "data": data.get('pm', [])}
            ]
        }
    elif chart_type == 'operation_status':
        return {
            "title": {"text": "工况统计分布"},
            "tooltip": {"trigger": "item"},
            "series": [{
                "type": "pie",
                "radius": "50%",
                "data": data.get('status_data', [])
            }]
        }
    
    return {}
```

## 二、报告生成工作流程

### 1. 数据查询阶段
```
用户输入："生成2024年7月垃圾焚烧企业数据分析报告"
↓
text2sql工具：生成多个SQL查询语句
↓
sql_execute工具：执行查询获取数据
```

### 2. 数据处理阶段
```
代码执行工具：处理查询结果
↓
生成统计信息和图表数据
↓
格式化为报告所需格式
```

### 3. 报告生成阶段
```
按照标准模板结构输出
↓
插入数据表格和统计信息
↓
生成Mermaid工艺流程图
↓
生成ECharts数据图表
```

## 三、关键SQL查询模板

### 1. 垃圾处理量统计
```sql
SELECT 
    unit_no AS '炉号',
    SUM(waste_amount) AS '垃圾处理量(吨)',
    AVG(waste_amount) AS '日均处理量(吨)',
    COUNT(*) AS '运行天数'
FROM incinerator_daily 
WHERE plant_code = '{plant_code}'
    AND data_date BETWEEN '{start_date}' AND '{end_date}'
GROUP BY unit_no
ORDER BY unit_no;
```

### 2. 工况统计分析
```sql
SELECT 
    status_mark AS '工况',
    COUNT(*) AS '小时数',
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) AS '占比(%)'
FROM incinerator_hourly 
WHERE plant_code = '{plant_code}'
    AND data_time BETWEEN '{start_time}' AND '{end_time}'
    AND is_valid = 1
GROUP BY status_mark
ORDER BY COUNT(*) DESC;
```

### 3. 污染物排放统计
```sql
SELECT 
    data_date AS '日期',
    unit_no AS '炉号',
    pm_daily AS 'PM',
    nox_daily AS 'NOx',
    so2_daily AS 'SO₂',
    hcl_daily AS 'HCl',
    co_daily AS 'CO',
    CASE WHEN exceed_flag = 1 THEN '超标' ELSE '达标' END AS '合规状态'
FROM incinerator_daily 
WHERE plant_code = '{plant_code}'
    AND data_date BETWEEN '{start_date}' AND '{end_date}'
ORDER BY data_date, unit_no;
```

### 4. 预警报警统计
```sql
SELECT 
    alarm_type AS '报警类型',
    COUNT(*) AS '报警次数',
    SUM(CASE WHEN alarm_level = 'warning' THEN 1 ELSE 0 END) AS '预警次数',
    SUM(CASE WHEN alarm_level = 'alarm' THEN 1 ELSE 0 END) AS '报警次数'
FROM alarm_records 
WHERE plant_code = '{plant_code}'
    AND alarm_time BETWEEN '{start_time}' AND '{end_time}'
GROUP BY alarm_type
ORDER BY COUNT(*) DESC;
```

## 四、图表配置模板

### 1. 工艺流程图（Mermaid）
```mermaid
graph TD
    A[垃圾投料系统] --> B[机械炉排炉]
    B --> C[SNCR炉内脱硝<br/>尿素溶液喷射]
    C --> D[半干法脱酸<br/>石灰浆喷射]
    D --> E[活性炭吸附<br/>重金属去除]
    E --> F[布袋除尘器<br/>颗粒物去除]
    F --> G[引风机]
    G --> H[烟囱排放<br/>在线监测]
    
    B --> I[余热锅炉]
    I --> J[汽轮发电机组]
    J --> K[电力输出]
    
    L[飞灰固化] --> M[安全填埋]
    N[炉渣处理] --> O[综合利用]
    
    style A fill:#e1f5fe
    style H fill:#ffebee
    style K fill:#e8f5e8
```

### 2. 温度趋势图（ECharts）
```json
{
  "title": {
    "text": "各炉号炉膛温度变化趋势（5分钟均值）",
    "subtext": "数据来源：DCS系统"
  },
  "tooltip": {
    "trigger": "axis",
    "axisPointer": {"type": "cross"}
  },
  "legend": {
    "data": ["1#炉", "2#炉", "3#炉"],
    "bottom": 0
  },
  "grid": {
    "left": "3%",
    "right": "4%",
    "bottom": "10%",
    "containLabel": true
  },
  "xAxis": {
    "type": "category",
    "data": ["时间数据"],
    "axisLabel": {"rotate": 45}
  },
  "yAxis": {
    "type": "value",
    "name": "温度(℃)",
    "min": 800,
    "max": 1100
  },
  "series": [
    {
      "name": "1#炉",
      "type": "line",
      "data": ["温度数据"],
      "itemStyle": {"color": "#FF6B6B"}
    },
    {
      "name": "2#炉", 
      "type": "line",
      "data": ["温度数据"],
      "itemStyle": {"color": "#4ECDC4"}
    },
    {
      "name": "3#炉",
      "type": "line", 
      "data": ["温度数据"],
      "itemStyle": {"color": "#45B7D1"}
    }
  ]
}
```

## 五、Dify平台配置步骤

### 1. 工具配置
1. 启用text2sql工具，配置数据库连接
2. 启用sql_execute工具，设置查询权限
3. 添加代码执行工具，上传Python处理脚本
4. 配置Mermaid和ECharts插件

### 2. 提示词配置
1. 导入修改后的智能体提示词
2. 设置报告生成触发关键词
3. 配置输出格式模板

### 3. 工作流设置
1. 创建报告生成工作流
2. 设置数据查询→处理→生成的流程
3. 配置错误处理和重试机制

### 4. 测试验证
1. 测试基础数据查询功能
2. 验证图表生成效果
3. 检查完整报告输出格式

通过以上配置，Dify平台的智能体将能够直接输出包含完整内容和图表的垃圾焚烧企业数据分析报告。
