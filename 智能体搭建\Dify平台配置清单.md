# Dify平台智能体配置清单

## 一、必需的插件和工具

### 1. 内置工具（必须启用）
- ✅ **text2sql工具**
  - 功能：将自然语言转换为SQL查询
  - 配置：连接到焚烧炉数据库
  - 权限：只读权限

- ✅ **sql_execute工具**
  - 功能：执行SQL查询并返回结果
  - 配置：设置查询超时时间（30秒）
  - 权限：SELECT权限

- ✅ **代码执行工具（Python）**
  - 功能：数据处理和图表配置生成
  - 配置：Python 3.8+环境
  - 依赖包：pandas, numpy, json

### 2. 第三方插件（推荐安装）

#### ECharts图表插件
```json
{
  "plugin_name": "echarts-renderer",
  "version": "5.4.0",
  "description": "用于渲染ECharts图表",
  "config": {
    "theme": "default",
    "renderer": "canvas",
    "width": 800,
    "height": 400
  }
}
```

#### Mermaid图表插件
```json
{
  "plugin_name": "mermaid-renderer", 
  "version": "10.0.0",
  "description": "用于渲染Mermaid流程图",
  "config": {
    "theme": "default",
    "flowchart": {
      "useMaxWidth": true,
      "htmlLabels": true
    }
  }
}
```

#### Markdown增强插件
```json
{
  "plugin_name": "markdown-enhanced",
  "version": "2.0.0", 
  "description": "增强Markdown渲染能力",
  "config": {
    "tables": true,
    "breaks": true,
    "linkify": true
  }
}
```

## 二、代码工具配置

### 1. Python数据处理脚本

#### 文件名：`data_processor.py`
```python
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta

class ReportDataProcessor:
    """报告数据处理器"""
    
    def __init__(self):
        self.pollutant_limits = {
            'pm_daily': 20,
            'nox_daily': 250, 
            'so2_daily': 80,
            'hcl_daily': 50,
            'co_daily': 80
        }
    
    def process_furnace_stats(self, query_result):
        """处理炉号统计数据"""
        df = pd.DataFrame(query_result)
        
        stats = {
            'total_records': len(df),
            'furnace_count': len(df['unit_no'].unique()) if 'unit_no' in df.columns else 0,
            'date_range': {
                'start': df['data_date'].min() if 'data_date' in df.columns else None,
                'end': df['data_date'].max() if 'data_date' in df.columns else None
            }
        }
        
        # 按炉号统计
        furnace_stats = {}
        if 'unit_no' in df.columns:
            for unit_no in df['unit_no'].unique():
                unit_data = df[df['unit_no'] == unit_no]
                furnace_stats[f'{unit_no}#炉'] = {
                    'records': len(unit_data),
                    'avg_pm': round(unit_data['pm_daily'].mean(), 1) if 'pm_daily' in unit_data.columns else 0,
                    'avg_nox': round(unit_data['nox_daily'].mean(), 1) if 'nox_daily' in unit_data.columns else 0,
                    'exceed_days': int(unit_data['exceed_flag'].sum()) if 'exceed_flag' in unit_data.columns else 0,
                    'compliance_rate': round((len(unit_data) - int(unit_data['exceed_flag'].sum())) / len(unit_data) * 100, 1) if 'exceed_flag' in unit_data.columns else 100
                }
        
        return stats, furnace_stats
    
    def generate_temperature_chart(self, temp_data):
        """生成温度趋势图配置"""
        return {
            "title": {
                "text": "各炉号炉膛温度变化趋势（日均值）",
                "subtext": "数据来源：DCS系统"
            },
            "tooltip": {
                "trigger": "axis",
                "axisPointer": {"type": "cross"}
            },
            "legend": {
                "data": ["1#炉", "2#炉", "3#炉", "标准线(850℃)"]
            },
            "grid": {
                "left": "3%",
                "right": "4%", 
                "bottom": "3%",
                "containLabel": True
            },
            "xAxis": {
                "type": "category",
                "data": temp_data.get('dates', [])
            },
            "yAxis": {
                "type": "value",
                "name": "温度(℃)",
                "min": 800,
                "max": 1100
            },
            "series": [
                {
                    "name": "1#炉",
                    "type": "line",
                    "data": temp_data.get('temp_1', []),
                    "itemStyle": {"color": "#FF6B6B"}
                },
                {
                    "name": "2#炉",
                    "type": "line", 
                    "data": temp_data.get('temp_2', []),
                    "itemStyle": {"color": "#4ECDC4"}
                },
                {
                    "name": "3#炉",
                    "type": "line",
                    "data": temp_data.get('temp_3', []),
                    "itemStyle": {"color": "#45B7D1"}
                },
                {
                    "name": "标准线(850℃)",
                    "type": "line",
                    "data": [850] * len(temp_data.get('dates', [])),
                    "itemStyle": {"color": "#FF4444"},
                    "lineStyle": {"type": "dashed"}
                }
            ]
        }
    
    def generate_pollutant_chart(self, pollutant_data):
        """生成污染物浓度图配置"""
        return {
            "title": {
                "text": "主要污染物浓度变化趋势",
                "subtext": "各炉号平均值"
            },
            "tooltip": {"trigger": "axis"},
            "legend": {
                "data": ["NOx", "SO₂", "HCl", "CO", "烟尘"]
            },
            "grid": {
                "left": "3%",
                "right": "4%",
                "bottom": "3%", 
                "containLabel": True
            },
            "xAxis": {
                "type": "category",
                "data": pollutant_data.get('dates', [])
            },
            "yAxis": {
                "type": "value",
                "name": "浓度(mg/Nm³)"
            },
            "series": [
                {
                    "name": "NOx",
                    "type": "line",
                    "data": pollutant_data.get('nox', []),
                    "itemStyle": {"color": "#FF6B6B"}
                },
                {
                    "name": "SO₂", 
                    "type": "line",
                    "data": pollutant_data.get('so2', []),
                    "itemStyle": {"color": "#4ECDC4"}
                },
                {
                    "name": "HCl",
                    "type": "line",
                    "data": pollutant_data.get('hcl', []),
                    "itemStyle": {"color": "#45B7D1"}
                },
                {
                    "name": "CO",
                    "type": "line", 
                    "data": pollutant_data.get('co', []),
                    "itemStyle": {"color": "#96CEB4"}
                },
                {
                    "name": "烟尘",
                    "type": "line",
                    "data": pollutant_data.get('pm', []),
                    "itemStyle": {"color": "#FFEAA7"}
                }
            ]
        }
    
    def generate_operation_status_chart(self, status_data):
        """生成工况统计饼图配置"""
        return {
            "title": {
                "text": "工况统计分布",
                "subtext": "各工况时间占比"
            },
            "tooltip": {
                "trigger": "item",
                "formatter": "{a} <br/>{b}: {c}小时 ({d}%)"
            },
            "series": [{
                "name": "工况统计",
                "type": "pie",
                "radius": "50%",
                "data": status_data.get('status_distribution', []),
                "emphasis": {
                    "itemStyle": {
                        "shadowBlur": 10,
                        "shadowOffsetX": 0,
                        "shadowColor": "rgba(0, 0, 0, 0.5)"
                    }
                }
            }]
        }

# 使用示例
processor = ReportDataProcessor()
```

### 2. 工作流配置脚本

#### 文件名：`workflow_config.py`
```python
def create_report_workflow():
    """创建报告生成工作流配置"""
    return {
        "workflow_name": "垃圾焚烧报告生成",
        "steps": [
            {
                "step_id": "1",
                "name": "识别报告类型",
                "type": "condition",
                "condition": "用户输入包含'报告'关键词",
                "next_step": "2"
            },
            {
                "step_id": "2", 
                "name": "数据查询",
                "type": "parallel",
                "tasks": [
                    {
                        "task_id": "2.1",
                        "tool": "text2sql",
                        "input": "垃圾处理量统计查询"
                    },
                    {
                        "task_id": "2.2",
                        "tool": "text2sql", 
                        "input": "工况统计查询"
                    },
                    {
                        "task_id": "2.3",
                        "tool": "text2sql",
                        "input": "污染物排放查询"
                    },
                    {
                        "task_id": "2.4",
                        "tool": "text2sql",
                        "input": "预警报警统计查询"
                    }
                ],
                "next_step": "3"
            },
            {
                "step_id": "3",
                "name": "执行SQL查询",
                "type": "parallel",
                "tasks": [
                    {
                        "task_id": "3.1",
                        "tool": "sql_execute",
                        "input": "来自步骤2.1的SQL"
                    },
                    {
                        "task_id": "3.2", 
                        "tool": "sql_execute",
                        "input": "来自步骤2.2的SQL"
                    },
                    {
                        "task_id": "3.3",
                        "tool": "sql_execute", 
                        "input": "来自步骤2.3的SQL"
                    },
                    {
                        "task_id": "3.4",
                        "tool": "sql_execute",
                        "input": "来自步骤2.4的SQL"
                    }
                ],
                "next_step": "4"
            },
            {
                "step_id": "4",
                "name": "数据处理",
                "type": "code_execution",
                "script": "data_processor.py",
                "input": "所有查询结果",
                "next_step": "5"
            },
            {
                "step_id": "5",
                "name": "生成报告",
                "type": "template_render",
                "template": "report_template.md",
                "input": "处理后的数据",
                "next_step": "end"
            }
        ]
    }
```

## 三、配置步骤

### 1. Dify平台基础配置
1. **创建新的智能体应用**
   - 应用类型：聊天助手
   - 模型选择：GPT-4或Claude-3
   - 上下文长度：8000 tokens

2. **导入提示词**
   - 复制修改后的智能体提示词
   - 设置系统角色和指令
   - 配置输出格式要求

3. **启用必需工具**
   - text2sql工具：配置数据库连接
   - sql_execute工具：设置查询权限
   - 代码执行工具：上传Python脚本

### 2. 插件安装配置
1. **安装ECharts插件**
   ```bash
   # 在Dify插件市场搜索并安装
   # 或通过API安装
   curl -X POST /api/plugins/install \
     -H "Content-Type: application/json" \
     -d '{"plugin_name": "echarts-renderer", "version": "5.4.0"}'
   ```

2. **安装Mermaid插件**
   ```bash
   # 类似方式安装Mermaid插件
   curl -X POST /api/plugins/install \
     -H "Content-Type: application/json" \
     -d '{"plugin_name": "mermaid-renderer", "version": "10.0.0"}'
   ```

### 3. 数据库连接配置
```json
{
  "database_config": {
    "type": "mysql",
    "host": "your_database_host",
    "port": 3306,
    "database": "incinerator_db",
    "username": "readonly_user",
    "password": "your_password",
    "charset": "utf8mb4",
    "timeout": 30
  }
}
```

### 4. 测试验证
1. **基础功能测试**
   - 输入："查询1号炉7月份的排放数据"
   - 验证：SQL生成和执行是否正常

2. **报告生成测试**
   - 输入："生成7月份垃圾焚烧企业数据分析报告"
   - 验证：完整报告结构和图表是否正确

3. **图表渲染测试**
   - 检查ECharts图表是否正常显示
   - 检查Mermaid流程图是否正确渲染

## 四、故障排除

### 常见问题及解决方案

1. **SQL查询失败**
   - 检查数据库连接配置
   - 验证用户权限设置
   - 确认表结构是否匹配

2. **图表不显示**
   - 检查插件是否正确安装
   - 验证图表配置JSON格式
   - 确认数据格式是否正确

3. **报告格式错误**
   - 检查Markdown渲染插件
   - 验证模板格式是否正确
   - 确认数据绑定是否成功

通过以上配置，Dify平台的智能体将能够完整地生成包含数据分析、图表展示和决策建议的垃圾焚烧企业数据分析报告。
