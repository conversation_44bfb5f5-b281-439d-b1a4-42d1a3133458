import argparse
import cv2
import os
import json
import time
from model import EnvironmentViolationDetector

def parse_args():
    parser = argparse.ArgumentParser(description="环境违规行为检测")
    parser.add_argument('--source', type=str, required=True, help='输入图像或视频文件路径，或摄像头编号(0)')
    parser.add_argument('--weights', type=str, default='', help='模型权重路径')
    parser.add_argument('--conf', type=float, default=0.5, help='置信度阈值')
    parser.add_argument('--output', type=str, default='output', help='输出目录')
    parser.add_argument('--save-json', action='store_true', help='将检测结果保存为JSON文件')
    return parser.parse_args()

def main():
    args = parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output, exist_ok=True)
    
    # 初始化模型
    print(f"加载模型 {'预训练YOLOv8' if not args.weights else args.weights}...")
    detector = EnvironmentViolationDetector(
        model_path=args.weights if args.weights else None,
        confidence=args.conf
    )
    
    # 检查输入源是摄像头、视频还是图像
    source = args.source
    is_camera = source.isdigit()
    is_video = not is_camera and (source.endswith('.mp4') or source.endswith('.avi') or source.endswith('.mov'))
    
    if is_camera:
        # 使用摄像头
        cap = cv2.VideoCapture(int(source))
        if not cap.isOpened():
            print(f"错误: 无法打开摄像头 {source}")
            return
        
        print(f"使用摄像头 {source} 进行实时检测。按 'q' 退出...")
        
        frame_count = 0
        start_time = time.time()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            # 处理帧
            processed_frame, detections = detector.detect(frame)
            
            # 计算FPS
            frame_count += 1
            elapsed_time = time.time() - start_time
            fps = frame_count / elapsed_time
            
            # 显示FPS
            cv2.putText(processed_frame, f"FPS: {fps:.2f}", (10, 30), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # 显示检测结果
            cv2.imshow("环境违规检测", processed_frame)
            
            # 按'q'退出
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
                
        cap.release()
        cv2.destroyAllWindows()
        
    elif is_video:
        # 处理视频文件
        cap = cv2.VideoCapture(source)
        if not cap.isOpened():
            print(f"错误: 无法打开视频 {source}")
            return
            
        # 获取视频属性
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # 创建输出视频文件
        output_path = os.path.join(args.output, os.path.basename(source))
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        print(f"处理视频: {source}")
        print(f"视频分辨率: {width}x{height}, FPS: {fps}, 总帧数: {total_frames}")
        print(f"输出视频将保存到: {output_path}")
        
        frame_count = 0
        all_detections = []
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            # 处理帧
            processed_frame, detections = detector.detect(frame)
            
            # 将检测结果写入输出视频
            out.write(processed_frame)
            
            # 更新进度
            frame_count += 1
            if frame_count % 10 == 0:
                progress = (frame_count / total_frames) * 100
                print(f"进度: {progress:.1f}% ({frame_count}/{total_frames})")
                
            # 保存检测结果
            if args.save_json:
                frame_detections = {
                    "frame": frame_count,
                    "timestamp": frame_count / fps,
                    "detections": detections
                }
                all_detections.append(frame_detections)
                
        cap.release()
        out.release()
        
        # 保存JSON检测结果
        if args.save_json:
            json_path = os.path.join(args.output, os.path.splitext(os.path.basename(source))[0] + "_detections.json")
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(all_detections, f, ensure_ascii=False, indent=4)
            print(f"检测结果已保存到: {json_path}")
            
        print(f"处理完成! 输出视频已保存到: {output_path}")
        
    else:
        # 处理单张图像
        if not os.path.exists(source):
            print(f"错误: 图像文件 {source} 不存在")
            return
            
        print(f"处理图像: {source}")
        
        # 读取图像
        image = cv2.imread(source)
        if image is None:
            print(f"错误: 无法读取图像 {source}")
            return
            
        # 检测图像中的违规行为
        processed_image, detections = detector.detect(image)
        
        # 保存处理后的图像
        output_path = os.path.join(args.output, os.path.basename(source))
        cv2.imwrite(output_path, processed_image)
        
        print(f"检测到 {len(detections)} 个违规行为:")
        for i, det in enumerate(detections):
            print(f"  {i+1}. {det['class']}: {det['confidence']:.2f}, 位置: {det['bbox']}")
            
        # 保存JSON检测结果
        if args.save_json:
            json_path = os.path.join(args.output, os.path.splitext(os.path.basename(source))[0] + "_detections.json")
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(detections, f, ensure_ascii=False, indent=4)
            print(f"检测结果已保存到: {json_path}")
            
        print(f"处理完成! 输出图像已保存到: {output_path}")
        
        # 显示结果
        cv2.imshow("检测结果", processed_image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()

if __name__ == "__main__":
    main() 