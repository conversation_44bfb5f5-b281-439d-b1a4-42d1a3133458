{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "convlstm_multivar_sites.ipynb", "provenance": [{"file_id": "1bE9BR1VLYohcN5Ko1Xfqnulh5XF0Unyu", "timestamp": 1596403069429}, {"file_id": "1IxQcZpOXeCS9CtXRmPjnFz7k5vwDTcqP", "timestamp": 1595516852382}], "collapsed_sections": ["C-1_1zW9wdih", "Y4wFgdDb3yRu", "ojNa4lnVWUSO"], "toc_visible": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "metadata": {"id": "ukOLd5z9o2NQ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 357}, "executionInfo": {"status": "ok", "timestamp": 1598557469941, "user_tz": -60, "elapsed": 1126, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "c8c9703a-bde7-45a9-b164-bf9427605ad5"}, "source": ["  !nvidia-smi"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["Thu Aug 27 19:44:29 2020       \n", "+-----------------------------------------------------------------------------+\n", "| NVIDIA-SMI 450.57       Driver Version: 418.67       CUDA Version: 10.1     |\n", "|-------------------------------+----------------------+----------------------+\n", "| GPU  Name        Persistence-M| Bus-Id        Disp.A | Volatile Uncorr. ECC |\n", "| Fan  Temp  Perf  Pwr:Usage/Cap|         Memory-Usage | GPU-Util  Compute M. |\n", "|                               |                      |               MIG M. |\n", "|===============================+======================+======================|\n", "|   0  Tesla T4            Off  | 00000000:00:04.0 Off |                    0 |\n", "| N/A   54C    P8    10W /  70W |      0MiB / 15079MiB |      0%      Default |\n", "|                               |                      |                 ERR! |\n", "+-------------------------------+----------------------+----------------------+\n", "                                                                               \n", "+-----------------------------------------------------------------------------+\n", "| Processes:                                                                  |\n", "|  GPU   GI   CI        PID   Type   Process name                  GPU Memory |\n", "|        ID   ID                                                   Usage      |\n", "|=============================================================================|\n", "|  No running processes found                                                 |\n", "+-----------------------------------------------------------------------------+\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "AIdpFAYUpDRR", "colab_type": "code", "colab": {}}, "source": ["import pandas as pd\n", "from matplotlib import pyplot as plt\n", "import numpy as np\n", "from sklearn.metrics import mean_squared_error"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "GdwmDyu4pG2O", "colab_type": "text"}, "source": ["# 导入数据"]}, {"cell_type": "code", "metadata": {"id": "Iib6DHLKDN84", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1598557502557, "user_tz": -60, "elapsed": 941, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "4b3baa24-425a-4078-9425-ef44c18dbbf1"}, "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "rylZ0333POOT", "colab_type": "text"}, "source": ["这次我们准备使用五个监测站的数据。假定Marylebone Road监测站的数据出现异常，此时我们用Bloomsbury, Eltham, Harlington和N_Kensington的数据来推断Marylebone的数据。注意，与之前多变量预测相同，该实验依旧是多变量预测单变量。分多次预测，实现多变量预测多变量。"]}, {"cell_type": "code", "metadata": {"id": "E9MpBLKwpIlm", "colab_type": "code", "colab": {}}, "source": ["Marylebone_Road=pd.read_csv('/content/drive/My Drive/air_inference/data/Marylebone_Road_clean.csv')\n", "Bloomsbury=pd.read_csv('/content/drive/My Drive/air_inference/data/Bloomsbury_clean.csv')\n", "Eltham=pd.read_csv('/content/drive/My Drive/air_inference/data/Eltham_clean.csv')\n", "Harlington=pd.read_csv('/content/drive/My Drive/air_inference/data/Harlington_clean.csv')\n", "N_Kensington=pd.read_csv('/content/drive/My Drive/air_inference/data/N_Kensington_clean.csv')"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "ihnTMkg0pWjr", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1598557499465, "user_tz": -60, "elapsed": 40, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "30e9a9a0-a4cb-45fe-e0d7-34a05cb5ba88"}, "source": ["Marylebone_Road.head()"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>site</th>\n", "      <th>code</th>\n", "      <th>date</th>\n", "      <th>nox</th>\n", "      <th>no2</th>\n", "      <th>no</th>\n", "      <th>o3</th>\n", "      <th>pm2.5</th>\n", "      <th>ws</th>\n", "      <th>wd</th>\n", "      <th>air_temp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>London Marylebone Road</td>\n", "      <td>MY1</td>\n", "      <td>2018-01-01 00:00:00</td>\n", "      <td>219.745313</td>\n", "      <td>81.948282</td>\n", "      <td>89.868883</td>\n", "      <td>20.680073</td>\n", "      <td>10.368619</td>\n", "      <td>4.594288</td>\n", "      <td>258.053368</td>\n", "      <td>5.187445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>London Marylebone Road</td>\n", "      <td>MY1</td>\n", "      <td>2018-01-01 01:00:00</td>\n", "      <td>221.095446</td>\n", "      <td>81.975320</td>\n", "      <td>90.731783</td>\n", "      <td>20.623365</td>\n", "      <td>10.251473</td>\n", "      <td>4.602090</td>\n", "      <td>257.695571</td>\n", "      <td>5.219898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>London Marylebone Road</td>\n", "      <td>MY1</td>\n", "      <td>2018-01-01 02:00:00</td>\n", "      <td>222.430163</td>\n", "      <td>82.046845</td>\n", "      <td>91.555615</td>\n", "      <td>20.582777</td>\n", "      <td>10.169084</td>\n", "      <td>4.613062</td>\n", "      <td>257.370167</td>\n", "      <td>5.255796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>London Marylebone Road</td>\n", "      <td>MY1</td>\n", "      <td>2018-01-01 03:00:00</td>\n", "      <td>222.483880</td>\n", "      <td>81.654154</td>\n", "      <td>91.846755</td>\n", "      <td>20.745120</td>\n", "      <td>10.014187</td>\n", "      <td>4.630636</td>\n", "      <td>257.105888</td>\n", "      <td>5.300848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>London Marylebone Road</td>\n", "      <td>MY1</td>\n", "      <td>2018-01-01 04:00:00</td>\n", "      <td>221.593263</td>\n", "      <td>81.130682</td>\n", "      <td>91.607309</td>\n", "      <td>20.959725</td>\n", "      <td>9.813301</td>\n", "      <td>4.661511</td>\n", "      <td>256.906359</td>\n", "      <td>5.350748</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     site code  ...          wd  air_temp\n", "0  London Marylebone Road  MY1  ...  258.053368  5.187445\n", "1  London Marylebone Road  MY1  ...  257.695571  5.219898\n", "2  London Marylebone Road  MY1  ...  257.370167  5.255796\n", "3  London Marylebone Road  MY1  ...  257.105888  5.300848\n", "4  London Marylebone Road  MY1  ...  256.906359  5.350748\n", "\n", "[5 rows x 11 columns]"]}, "metadata": {"tags": []}, "execution_count": 5}]}, {"cell_type": "code", "metadata": {"id": "pQ3uen_qQCza", "colab_type": "code", "colab": {}}, "source": ["Marylebone_Road=Marylebone_Road[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']]\n", "Bloomsbury=Bloomsbury[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']]\n", "Eltham=Eltham[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']]\n", "Harlington=Harlington[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']]\n", "N_Kensington=N_Kensington[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']]\n", "\n"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "IKaQ6QfRSmio", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1598557499466, "user_tz": -60, "elapsed": 27, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "d6dc05a9-e6bb-43f2-bc89-d376d52eee68"}, "source": ["Marylebone_Road\n", "Bloomsbury\n", "<PERSON><PERSON>\n", "Harlington\n", "N_Kensington"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>nox</th>\n", "      <th>no2</th>\n", "      <th>no</th>\n", "      <th>o3</th>\n", "      <th>pm2.5</th>\n", "      <th>ws</th>\n", "      <th>wd</th>\n", "      <th>air_temp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>18.634584</td>\n", "      <td>16.685778</td>\n", "      <td>1.270981</td>\n", "      <td>60.070209</td>\n", "      <td>6.872474</td>\n", "      <td>4.594288</td>\n", "      <td>258.053368</td>\n", "      <td>5.187445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>19.176644</td>\n", "      <td>17.122874</td>\n", "      <td>1.339436</td>\n", "      <td>59.637749</td>\n", "      <td>6.795094</td>\n", "      <td>4.602090</td>\n", "      <td>257.695571</td>\n", "      <td>5.219898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>19.723134</td>\n", "      <td>17.548214</td>\n", "      <td>1.418449</td>\n", "      <td>59.211174</td>\n", "      <td>6.717488</td>\n", "      <td>4.613062</td>\n", "      <td>257.370167</td>\n", "      <td>5.255796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20.133219</td>\n", "      <td>17.844811</td>\n", "      <td>1.492463</td>\n", "      <td>58.913020</td>\n", "      <td>6.621239</td>\n", "      <td>4.630636</td>\n", "      <td>257.105888</td>\n", "      <td>5.300848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>19.865833</td>\n", "      <td>17.747791</td>\n", "      <td>1.381354</td>\n", "      <td>58.671554</td>\n", "      <td>6.515320</td>\n", "      <td>4.661511</td>\n", "      <td>256.906359</td>\n", "      <td>5.350748</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17515</th>\n", "      <td>58.800669</td>\n", "      <td>42.477744</td>\n", "      <td>10.645537</td>\n", "      <td>3.426733</td>\n", "      <td>25.599702</td>\n", "      <td>2.322937</td>\n", "      <td>122.323683</td>\n", "      <td>3.055791</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17516</th>\n", "      <td>58.908322</td>\n", "      <td>42.657677</td>\n", "      <td>10.598397</td>\n", "      <td>3.370981</td>\n", "      <td>25.819725</td>\n", "      <td>2.312240</td>\n", "      <td>120.287580</td>\n", "      <td>3.010667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17517</th>\n", "      <td>59.006263</td>\n", "      <td>42.836211</td>\n", "      <td>10.545836</td>\n", "      <td>3.281494</td>\n", "      <td>26.037294</td>\n", "      <td>2.298849</td>\n", "      <td>118.331870</td>\n", "      <td>2.968728</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17518</th>\n", "      <td>59.081172</td>\n", "      <td>42.984552</td>\n", "      <td>10.497945</td>\n", "      <td>3.231871</td>\n", "      <td>26.253455</td>\n", "      <td>2.292898</td>\n", "      <td>116.428207</td>\n", "      <td>2.931535</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17519</th>\n", "      <td>59.140646</td>\n", "      <td>43.112254</td>\n", "      <td>10.453448</td>\n", "      <td>3.220678</td>\n", "      <td>26.492812</td>\n", "      <td>2.291138</td>\n", "      <td>114.545510</td>\n", "      <td>2.896878</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>17520 rows × 8 columns</p>\n", "</div>"], "text/plain": ["             nox        no2         no  ...        ws          wd  air_temp\n", "0      18.634584  16.685778   1.270981  ...  4.594288  258.053368  5.187445\n", "1      19.176644  17.122874   1.339436  ...  4.602090  257.695571  5.219898\n", "2      19.723134  17.548214   1.418449  ...  4.613062  257.370167  5.255796\n", "3      20.133219  17.844811   1.492463  ...  4.630636  257.105888  5.300848\n", "4      19.865833  17.747791   1.381354  ...  4.661511  256.906359  5.350748\n", "...          ...        ...        ...  ...       ...         ...       ...\n", "17515  58.800669  42.477744  10.645537  ...  2.322937  122.323683  3.055791\n", "17516  58.908322  42.657677  10.598397  ...  2.312240  120.287580  3.010667\n", "17517  59.006263  42.836211  10.545836  ...  2.298849  118.331870  2.968728\n", "17518  59.081172  42.984552  10.497945  ...  2.292898  116.428207  2.931535\n", "17519  59.140646  43.112254  10.453448  ...  2.291138  114.545510  2.896878\n", "\n", "[17520 rows x 8 columns]"]}, "metadata": {"tags": []}, "execution_count": 7}]}, {"cell_type": "markdown", "metadata": {"id": "ageyQmUhU0ko", "colab_type": "text"}, "source": ["先将每个监测站的columns更名。加上后缀为监测站的首字母"]}, {"cell_type": "code", "metadata": {"id": "9zdzb4LqRkp_", "colab_type": "code", "colab": {}}, "source": ["col_<PERSON>lebone=['nox_M','no2_M','no_M','o3_M','pm2.5_M','ws_M','wd_M','air_temp_M']\n", "col_Bloomsbury=['nox_B','no2_B','no_B','o3_B','pm2.5_B','ws_B','wd_B','air_temp_B']\n", "col_Eltham=['nox_E','no2_E','no_E','o3_E','pm2.5_E','ws_E','wd_E','air_temp_E']\n", "col_Harlington=['nox_H','no2_H','no_H','o3_H','pm2.5_H','ws_H','wd_H','air_temp_H']\n", "col_N_Kensington=['nox_N','no2_N','no_N','o3_N','pm2.5_N','ws_N','wd_N','air_temp_N']\n", "\n", "Marylebone_Road.columns=col_Marylebone\n", "Bloomsbury.columns=col_Bloomsbury\n", "Eltham.columns=col_Eltham\n", "Harlington.columns=col_Harlington\n", "N_Kensington.columns=col_N_Kensington"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "UVo7UDMpU8dW", "colab_type": "text"}, "source": ["接下来将所有监测站的数据拼接起来"]}, {"cell_type": "code", "metadata": {"id": "oc3G44CBS4Nh", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1598557499467, "user_tz": -60, "elapsed": 12, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "66230d36-2e37-478d-8f63-97adbbe0062a"}, "source": ["dataset=Bloomsbury.join(Marylebone_Road)\n", "dataset=dataset.join(Eltham)\n", "dataset=dataset.join(Harlington)\n", "dataset=dataset.join(N_Kensington)\n", "dataset.head()"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>nox_B</th>\n", "      <th>no2_B</th>\n", "      <th>no_B</th>\n", "      <th>o3_B</th>\n", "      <th>pm2.5_B</th>\n", "      <th>ws_B</th>\n", "      <th>wd_B</th>\n", "      <th>air_temp_B</th>\n", "      <th>nox_H</th>\n", "      <th>no2_H</th>\n", "      <th>no_H</th>\n", "      <th>o3_H</th>\n", "      <th>pm2.5_H</th>\n", "      <th>ws_H</th>\n", "      <th>wd_H</th>\n", "      <th>air_temp_H</th>\n", "      <th>nox_E</th>\n", "      <th>no2_E</th>\n", "      <th>no_E</th>\n", "      <th>o3_E</th>\n", "      <th>pm2.5_E</th>\n", "      <th>ws_E</th>\n", "      <th>wd_E</th>\n", "      <th>air_temp_E</th>\n", "      <th>nox_N</th>\n", "      <th>no2_N</th>\n", "      <th>no_N</th>\n", "      <th>o3_N</th>\n", "      <th>pm2.5_N</th>\n", "      <th>ws_N</th>\n", "      <th>wd_N</th>\n", "      <th>air_temp_N</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>38.719371</td>\n", "      <td>27.599582</td>\n", "      <td>7.252141</td>\n", "      <td>47.360318</td>\n", "      <td>7.497625</td>\n", "      <td>4.598855</td>\n", "      <td>257.279906</td>\n", "      <td>5.378717</td>\n", "      <td>25.652376</td>\n", "      <td>17.162654</td>\n", "      <td>5.536853</td>\n", "      <td>58.319162</td>\n", "      <td>4.669857</td>\n", "      <td>6.536290</td>\n", "      <td>254.682024</td>\n", "      <td>4.977652</td>\n", "      <td>10.387007</td>\n", "      <td>7.380368</td>\n", "      <td>1.960881</td>\n", "      <td>59.104229</td>\n", "      <td>10.931926</td>\n", "      <td>4.508552</td>\n", "      <td>256.806923</td>\n", "      <td>5.348268</td>\n", "      <td>18.634584</td>\n", "      <td>16.685778</td>\n", "      <td>1.270981</td>\n", "      <td>60.070209</td>\n", "      <td>6.872474</td>\n", "      <td>4.594288</td>\n", "      <td>258.053368</td>\n", "      <td>5.187445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>38.976582</td>\n", "      <td>27.836512</td>\n", "      <td>7.265368</td>\n", "      <td>47.042127</td>\n", "      <td>7.449653</td>\n", "      <td>4.603798</td>\n", "      <td>257.009139</td>\n", "      <td>5.412134</td>\n", "      <td>26.603729</td>\n", "      <td>17.836684</td>\n", "      <td>5.717718</td>\n", "      <td>57.659575</td>\n", "      <td>4.672704</td>\n", "      <td>6.554847</td>\n", "      <td>254.312810</td>\n", "      <td>5.012679</td>\n", "      <td>10.823717</td>\n", "      <td>7.652696</td>\n", "      <td>2.068089</td>\n", "      <td>58.843670</td>\n", "      <td>10.720061</td>\n", "      <td>4.498159</td>\n", "      <td>256.500375</td>\n", "      <td>5.379024</td>\n", "      <td>19.176644</td>\n", "      <td>17.122874</td>\n", "      <td>1.339436</td>\n", "      <td>59.637749</td>\n", "      <td>6.795094</td>\n", "      <td>4.602090</td>\n", "      <td>257.695571</td>\n", "      <td>5.219898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>39.251382</td>\n", "      <td>28.072885</td>\n", "      <td>7.290429</td>\n", "      <td>46.715825</td>\n", "      <td>7.416401</td>\n", "      <td>4.621557</td>\n", "      <td>256.762603</td>\n", "      <td>5.453970</td>\n", "      <td>27.500740</td>\n", "      <td>18.461178</td>\n", "      <td>5.895449</td>\n", "      <td>57.038377</td>\n", "      <td>4.669182</td>\n", "      <td>6.579388</td>\n", "      <td>254.014423</td>\n", "      <td>5.051040</td>\n", "      <td>11.238689</td>\n", "      <td>7.897627</td>\n", "      <td>2.178986</td>\n", "      <td>58.579590</td>\n", "      <td>10.543322</td>\n", "      <td>4.510796</td>\n", "      <td>256.218836</td>\n", "      <td>5.420729</td>\n", "      <td>19.723134</td>\n", "      <td>17.548214</td>\n", "      <td>1.418449</td>\n", "      <td>59.211174</td>\n", "      <td>6.717488</td>\n", "      <td>4.613062</td>\n", "      <td>257.370167</td>\n", "      <td>5.255796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>37.985254</td>\n", "      <td>27.997451</td>\n", "      <td>6.513879</td>\n", "      <td>46.400863</td>\n", "      <td>7.358787</td>\n", "      <td>4.636919</td>\n", "      <td>256.538550</td>\n", "      <td>5.502388</td>\n", "      <td>28.379700</td>\n", "      <td>19.068324</td>\n", "      <td>6.072721</td>\n", "      <td>56.435795</td>\n", "      <td>4.654887</td>\n", "      <td>6.636912</td>\n", "      <td>253.861244</td>\n", "      <td>5.099631</td>\n", "      <td>11.546266</td>\n", "      <td>8.070745</td>\n", "      <td>2.266678</td>\n", "      <td>58.360156</td>\n", "      <td>10.361331</td>\n", "      <td>4.524491</td>\n", "      <td>255.957051</td>\n", "      <td>5.467779</td>\n", "      <td>20.133219</td>\n", "      <td>17.844811</td>\n", "      <td>1.492463</td>\n", "      <td>58.913020</td>\n", "      <td>6.621239</td>\n", "      <td>4.630636</td>\n", "      <td>257.105888</td>\n", "      <td>5.300848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>38.973919</td>\n", "      <td>28.512513</td>\n", "      <td>6.822754</td>\n", "      <td>46.033610</td>\n", "      <td>7.302818</td>\n", "      <td>4.658491</td>\n", "      <td>256.342472</td>\n", "      <td>5.554477</td>\n", "      <td>29.266844</td>\n", "      <td>19.683219</td>\n", "      <td>6.250279</td>\n", "      <td>55.827704</td>\n", "      <td>4.636542</td>\n", "      <td>6.716429</td>\n", "      <td>253.903225</td>\n", "      <td>5.156423</td>\n", "      <td>11.794165</td>\n", "      <td>8.191950</td>\n", "      <td>2.349305</td>\n", "      <td>58.183813</td>\n", "      <td>10.154809</td>\n", "      <td>4.533285</td>\n", "      <td>255.699470</td>\n", "      <td>5.517365</td>\n", "      <td>19.865833</td>\n", "      <td>17.747791</td>\n", "      <td>1.381354</td>\n", "      <td>58.671554</td>\n", "      <td>6.515320</td>\n", "      <td>4.661511</td>\n", "      <td>256.906359</td>\n", "      <td>5.350748</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       nox_B      no2_B      no_B  ...      ws_N        wd_N  air_temp_N\n", "0  38.719371  27.599582  7.252141  ...  4.594288  258.053368    5.187445\n", "1  38.976582  27.836512  7.265368  ...  4.602090  257.695571    5.219898\n", "2  39.251382  28.072885  7.290429  ...  4.613062  257.370167    5.255796\n", "3  37.985254  27.997451  6.513879  ...  4.630636  257.105888    5.300848\n", "4  38.973919  28.512513  6.822754  ...  4.661511  256.906359    5.350748\n", "\n", "[5 rows x 32 columns]"]}, "metadata": {"tags": []}, "execution_count": 9}]}, {"cell_type": "markdown", "metadata": {"id": "qsw13S-QuS9t", "colab_type": "text"}, "source": ["# 多站点多变量进行预测"]}, {"cell_type": "code", "metadata": {"id": "3zTUeXsCuZ3V", "colab_type": "code", "colab": {}}, "source": ["var_origin=dataset.values"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "hw0hFQeWup8R", "colab_type": "text"}, "source": ["数据进行归一化操作"]}, {"cell_type": "code", "metadata": {"id": "T9bUOj3purhd", "colab_type": "code", "colab": {}}, "source": ["from sklearn.preprocessing import MinMaxScaler\n", "scaler = MinMaxScaler(feature_range=(0, 1))\n", "scaled = scaler.fit_transform(var_origin)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "k0vvmm5puuTJ", "colab_type": "text"}, "source": ["将数据转为cuda类型"]}, {"cell_type": "code", "metadata": {"id": "5b-DE_7huwo4", "colab_type": "code", "colab": {}}, "source": ["import torch\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "var= torch.FloatTensor(scaled).to(device)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "vAm8BF6Nu3eI", "colab_type": "text"}, "source": ["划分训练集，验证集和测试集"]}, {"cell_type": "code", "metadata": {"id": "S5Z9FaVUu7ji", "colab_type": "code", "colab": {}}, "source": ["def splitData(var,per_val,per_test):\n", "    num_val=int(len(var)*per_val)\n", "    num_test=int(len(var)*per_test)\n", "    train_size=int(len(var)-num_val-num_test)\n", "    train_data=var[0:train_size]\n", "    val_data=var[train_size:train_size+num_val]\n", "    test_data=var[train_size+num_val:train_size+num_val+num_test]\n", "    return train_data,val_data,test_data"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "hAbQ9M0pu-6W", "colab_type": "text"}, "source": ["我们的验证集合测试集都取96小时"]}, {"cell_type": "code", "metadata": {"id": "12s54oyKu_qb", "colab_type": "code", "colab": {}}, "source": ["train_data,val_data,test_data=splitData(var,0.1,0.1)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "B9MMKedCvBcQ", "colab_type": "text"}, "source": ["查看长度"]}, {"cell_type": "code", "metadata": {"id": "3CL_EC11vDhf", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1598557516506, "user_tz": -60, "elapsed": 14855, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "20f2296f-242f-475c-863e-200ed584ad95"}, "source": ["print('The length of train data, validation data and test data are:',len(train_data),',',len(val_data),',',len(test_data))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The length of train data, validation data and test data are: 14016 , 1752 , 1752\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "xM8DYu0RvGSt", "colab_type": "text"}, "source": ["\n", "取一定大小的窗口进行滑动，每个窗口的label值是窗口下一个预测的第一个空气污染物的值"]}, {"cell_type": "code", "metadata": {"id": "Neov5unqwMkx", "colab_type": "code", "colab": {}}, "source": ["train_window = 240\n", "def create_train_sequence(input_data, tw):\n", "    inout_seq = []\n", "    L = len(input_data)\n", "    for i in range(L-tw):\n", "        train_seq = input_data[i:i+tw]\n", "        train_label = input_data[i+tw:i+tw+1]\n", "        inout_seq.append((train_seq ,train_label))\n", "    return inout_seq"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "Nq2VNCQZwQNb", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1598557516507, "user_tz": -60, "elapsed": 14845, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "859046cf-e6b2-417f-bd1a-21140d0da58f"}, "source": ["train_inout_seq = create_train_sequence(train_data, train_window)\n", "print('The total number of train windows is',len(train_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of train windows is 13776\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "SRKEVERCwOQs", "colab_type": "text"}, "source": ["注意，与上面创建train_data的sequence不同，验证集数据只是label。其数据部分还是需要借助于train集中的数据，大小为一个窗口。而这一个窗口的数据并不会在训练过程中被使用"]}, {"cell_type": "code", "metadata": {"id": "pYIJhKWkwWSW", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1598557516508, "user_tz": -60, "elapsed": 14841, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "20d6b3bb-a286-4559-fca3-b06b35faf766"}, "source": ["def create_val_sequence(train_data,val_data, tw):\n", "    temp=torch.cat((train_data,val_data))   #先将训练集和测试集合并\n", "    inout_seq = []\n", "    L = len(val_data)\n", "    for i in range(L):\n", "        val_seq = temp[-(train_window+L)+i:-L+i]\n", "        val_label = test_data[i:i+1]\n", "        inout_seq.append((val_seq ,val_label))\n", "\n", "    return inout_seq\n", "\n", "val_inout_seq = create_val_sequence(train_data, val_data,train_window)\n", "print('The total number of validation windows is',len(val_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of validation windows is 1752\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "5Ite_y8OWk4Y", "colab_type": "text"}, "source": [" 此时的label的shape是[1,40]。注意，真正的label只有这40个值中的前五个"]}, {"cell_type": "code", "metadata": {"id": "WodbuYEG6Inl", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1598557516508, "user_tz": -60, "elapsed": 14836, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "fc5247c3-2850-466f-fa9e-5f32e41daa36"}, "source": ["val_inout_seq[0][1].shape"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["torch.<PERSON><PERSON>([1, 32])"]}, "metadata": {"tags": []}, "execution_count": 20}]}, {"cell_type": "code", "metadata": {"id": "9Rr2_qTvTKkm", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1598557516509, "user_tz": -60, "elapsed": 14830, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "a371c131-3c93-4e8f-c3cd-223ad9204f2e"}, "source": ["def create_test_sequence(train_data,val_data,test_data, tw):\n", "    temp=torch.cat((train_data,val_data))   #先将训练集和测试集合并\n", "    temp=torch.cat((temp,test_data))\n", "    inout_seq = []\n", "    L = len(test_data)\n", "    for i in range(L):\n", "        test_seq = temp[-(train_window+L)+i:-L+i]\n", "        test_label = test_data[i:i+1]\n", "        inout_seq.append((test_seq ,test_label))\n", "\n", "    return inout_seq\n", "\n", "test_inout_seq = create_test_sequence(train_data, val_data, test_data,train_window)\n", "print('The total number of validation windows is',len(val_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of validation windows is 1752\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "C-1_1zW9wdih", "colab_type": "text"}, "source": ["# 定义LSTM"]}, {"cell_type": "markdown", "metadata": {"id": "sFYIJDyqwhH5", "colab_type": "text"}, "source": ["ConvLSTM的代码借鉴的是这位老哥的https://github.com/ndrplz/ConvLSTM_pytorch "]}, {"cell_type": "code", "metadata": {"id": "7PsCR6V2DgZu", "colab_type": "code", "colab": {}}, "source": ["import torch.nn as nn\n", "import torch\n", "\n", "class ConvLSTMCell(nn.Module):\n", "\n", "    def __init__(self, input_dim, hidden_dim, kernel_size, bias):\n", "        \"\"\"\n", "        Initialize ConvLSTM cell.\n", "        Parameters\n", "        ----------\n", "        input_dim: int\n", "            Number of channels of input tensor.\n", "        hidden_dim: int\n", "            Number of channels of hidden state.\n", "        kernel_size: (int, int)\n", "            Size of the convolutional kernel.\n", "        bias: bool\n", "            Whether or not to add the bias.\n", "        \"\"\"\n", "\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.input_dim = input_dim\n", "        self.hidden_dim = hidden_dim\n", "\n", "        self.kernel_size = kernel_size\n", "        self.padding = kernel_size[0] // 2, kernel_size[1] // 2\n", "        self.bias = bias\n", "\n", "        self.conv = nn.Conv2d(in_channels=self.input_dim + self.hidden_dim,\n", "                              out_channels=4 * self.hidden_dim,\n", "                              kernel_size=self.kernel_size,\n", "                              padding=self.padding,\n", "                              bias=self.bias).to(device)\n", "\n", "    def forward(self, input_tensor, cur_state):\n", "        h_cur, c_cur = cur_state\n", "\n", "        combined = torch.cat([input_tensor, h_cur], dim=1)  # concatenate along channel axis\n", "\n", "        combined_conv = self.conv(combined)\n", "        cc_i, cc_f, cc_o, cc_g = torch.split(combined_conv, self.hidden_dim, dim=1)\n", "        i = torch.sigmoid(cc_i)\n", "        f = torch.sigmoid(cc_f)\n", "        o = torch.sigmoid(cc_o)\n", "        g = torch.tanh(cc_g)\n", "\n", "        c_next = f * c_cur + i * g\n", "        h_next = o * torch.tanh(c_next)\n", "\n", "        return h_next.to(device), c_next.to(device)\n", "\n", "    def init_hidden(self, batch_size, image_size):\n", "        height, width = image_size\n", "        return (torch.zeros(batch_size, self.hidden_dim, height, width, device=self.conv.weight.device),\n", "                torch.zeros(batch_size, self.hidden_dim, height, width, device=self.conv.weight.device))"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "J1nBtcRFDsBG", "colab_type": "code", "colab": {}}, "source": ["class ConvLSTM(nn.Module):\n", "\n", "    \"\"\"\n", "    Parameters:\n", "        input_dim: Number of channels in input\n", "        hidden_dim: Number of hidden channels\n", "        kernel_size: Size of kernel in convolutions\n", "        num_layers: Number of LSTM layers stacked on each other\n", "        batch_first: Whether or not dimension 0 is the batch or not\n", "        bias: Bias or no bias in Convolution\n", "        return_all_layers: Return the list of computations for all layers\n", "        Note: Will do same padding.\n", "    Input:\n", "        A tensor of size B, T, C, H, W or T, B, C, H, W\n", "    Output:\n", "        A tuple of two lists of length num_layers (or length 1 if return_all_layers is False).\n", "            0 - layer_output_list is the list of lists of length T of each output\n", "            1 - last_state_list is the list of last states\n", "                    each element of the list is a tuple (h, c) for hidden state and memory\n", "    Example:\n", "        >> x = torch.rand((32, 10, 64, 128, 128))\n", "        >> convlstm = ConvLSTM(64, 16, 3, 1, True, True, False)\n", "        >> _, last_states = convlstm(x)\n", "        >> h = last_states[0][0]  # 0 for layer index, 0 for h index\n", "    \"\"\"\n", "\n", "    def __init__(self, input_dim, hidden_dim, kernel_size, num_layers,\n", "                 batch_first=False, bias=True, return_all_layers=False):\n", "        super(<PERSON>v<PERSON><PERSON>, self).__init__()\n", "\n", "        self._check_kernel_size_consistency(kernel_size)\n", "\n", "        # Make sure that both `kernel_size` and `hidden_dim` are lists having len == num_layers\n", "        kernel_size = self._extend_for_multilayer(kernel_size, num_layers)\n", "        hidden_dim = self._extend_for_multilayer(hidden_dim, num_layers)\n", "        if not len(kernel_size) == len(hidden_dim) == num_layers:\n", "            raise ValueError('Inconsistent list length.')\n", "\n", "        self.input_dim = input_dim\n", "        self.hidden_dim = hidden_dim\n", "        self.kernel_size = kernel_size\n", "        self.num_layers = num_layers\n", "        self.batch_first = batch_first\n", "        self.bias = bias\n", "        self.return_all_layers = return_all_layers\n", "\n", "        cell_list = []\n", "        for i in range(0, self.num_layers):\n", "            cur_input_dim = self.input_dim if i == 0 else self.hidden_dim[i - 1]\n", "\n", "            cell_list.append(ConvLSTMCell(input_dim=cur_input_dim,\n", "                                          hidden_dim=self.hidden_dim[i],\n", "                                          kernel_size=self.kernel_size[i],\n", "                                          bias=self.bias))\n", "\n", "        self.cell_list = nn.ModuleList(cell_list)\n", "\n", "    def forward(self, input_tensor, hidden_state=None):\n", "        \"\"\"\n", "        Parameters\n", "        ----------\n", "        input_tensor: todo\n", "            5-D Tensor either of shape (t, b, c, h, w) or (b, t, c, h, w)\n", "        hidden_state: todo\n", "            None. todo implement stateful\n", "        Returns\n", "        -------\n", "        last_state_list, layer_output\n", "        \"\"\"\n", "        if not self.batch_first:\n", "            # (t, b, c, h, w) -> (b, t, c, h, w)\n", "            input_tensor = input_tensor.permute(1, 0, 2, 3, 4)\n", "\n", "        b, _, _, h, w = input_tensor.size()\n", "\n", "        # Implement stateful ConvLSTM\n", "        if hidden_state is not None:\n", "            raise NotImplementedError()\n", "        else:\n", "            # Since the init is done in forward. Can send image size here\n", "            hidden_state = self._init_hidden(batch_size=b,\n", "                                             image_size=(h, w))\n", "\n", "        layer_output_list = []\n", "        last_state_list = []\n", "\n", "        seq_len = input_tensor.size(1)\n", "        cur_layer_input = input_tensor\n", "\n", "        for layer_idx in range(self.num_layers):\n", "\n", "            h, c = hidden_state[layer_idx]\n", "            output_inner = []\n", "            for t in range(seq_len):\n", "                h, c = self.cell_list[layer_idx](input_tensor=cur_layer_input[:, t, :, :, :],\n", "                                                 cur_state=[h, c])\n", "                output_inner.append(h)\n", "\n", "            layer_output = torch.stack(output_inner, dim=1)\n", "            cur_layer_input = layer_output\n", "\n", "            layer_output_list.append(layer_output)\n", "            last_state_list.append([h, c])\n", "\n", "        if not self.return_all_layers:\n", "            layer_output_list = layer_output_list[-1:]\n", "            last_state_list = last_state_list[-1:]\n", "\n", "        # return layer_output_list, last_state_list\n", "        pre=np.array(layer_output_list)[0]\n", "\n", "        return pre[0,0,-1,-1,0]\n", "\n", "    def _init_hidden(self, batch_size, image_size):\n", "        init_states = []\n", "        for i in range(self.num_layers):\n", "            init_states.append(self.cell_list[i].init_hidden(batch_size, image_size))\n", "        return init_states\n", "\n", "    @staticmethod\n", "    def _check_kernel_size_consistency(kernel_size):\n", "        if not (isinstance(kernel_size, tuple) or\n", "                (isinstance(kernel_size, list) and all([isinstance(elem, tuple) for elem in kernel_size]))):\n", "            raise ValueError('`kernel_size` must be tuple or list of tuples')\n", "\n", "    @staticmethod\n", "    def _extend_for_multilayer(param, num_layers):\n", "        if not isinstance(param, list):\n", "            param = [param] * num_layers\n", "        return param"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "zF00gWiSwgqg", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1598557516770, "user_tz": -60, "elapsed": 15072, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "8370944d-3d2d-4489-ccaf-f8c601ca101e"}, "source": ["train_data.shape"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([14016, 32])"]}, "metadata": {"tags": []}, "execution_count": 24}]}, {"cell_type": "code", "metadata": {"id": "cQTl7q1Tqlvb", "colab_type": "code", "colab": {}}, "source": ["from torch import nn\n", "import torch.nn.functional as F\n", "import torch.nn.init as init\n", "import math\n", "class LSTM(nn.Module):\n", "    def __init__(self,input_size=40,hidden_layer_size=50,output_size=1,num_layers=2):\n", "        super().__init__()\n", "        self.hidden_layer_size=hidden_layer_size\n", "        self.lstm=nn.LSTM(input_size,hidden_layer_size,num_layers)\n", "        self.linear1=nn.Linear(hidden_layer_size,hidden_layer_size)\n", "        self.linear2=nn.Linear(hidden_layer_size,output_size)\n", "        self.hidden_cell=(torch.zeros(num_layers,1,self.hidden_layer_size),torch.zeros(num_layers,1,self.hidden_layer_size))\n", "        init_rnn(self.lstm,'xavier')\n", "        \n", "    def forward(self,input_seq):\n", "        lstm_out, self.hidden_cell = self.lstm(input_seq.reshape(len(input_seq),1,40), self.hidden_cell)\n", "        out=self.linear1(lstm_out.view(len(input_seq), -1))\n", "        out=torch.tanh(out)\n", "        predictions = self.linear2(out)\n", "        return predictions[-1]\n", "\n", "#设定初始化\n", "def init_rnn(x, type='uniform'):\n", "    for layer in x._all_weights:\n", "        for w in layer:\n", "            if 'weight' in w:\n", "                if type == 'xavier':\n", "                    init.xavier_normal_(getattr(x, w))\n", "                elif type == 'uniform':\n", "                    stdv = 1.0 / math.sqrt(x.hidden_size)\n", "                    init.uniform_(getattr(x, w), -stdv, stdv)\n", "                elif type == 'normal':\n", "                    stdv = 1.0 / math.sqrt(x.hidden_size)\n", "                    init.normal_(getattr(x, w), .0, stdv)\n", "                else:\n", "                    raise ValueError"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "Y4wFgdDb3yRu", "colab_type": "text"}, "source": ["# 初始化模型"]}, {"cell_type": "markdown", "metadata": {"id": "khFmQ2awIfht", "colab_type": "text"}, "source": ["与之前一样，分别多nox,no2,no,o3和pm2.5进行预测"]}, {"cell_type": "code", "metadata": {"id": "MNLpUCgxTa9e", "colab_type": "code", "colab": {}}, "source": ["import copy\n", "\n", "epochs=5\n", "\n", "\n", "#为了实现多变量预测多个单变量，我这里用了五个LSTM模型\n", "model_nox = ConvLSTM(input_dim=1,\n", "                 hidden_dim=[32,32],\n", "                 kernel_size=(5, 5),\n", "                 num_layers=2)\n", "model_no2 = ConvLSTM(input_dim=1,\n", "                 hidden_dim=[32,32],\n", "                 kernel_size=(5, 5),\n", "                 num_layers=2)\n", "\n", "model_no = ConvLSTM(input_dim=1,\n", "                 hidden_dim=[32,32],\n", "                 kernel_size=(5, 5),\n", "                 num_layers=2)\n", "\n", "model_o3 = ConvLSTM(input_dim=1,\n", "                 hidden_dim=[32,32],\n", "                 kernel_size=(5, 5),\n", "                 num_layers=2)\n", "\n", "model_pm25 = ConvLSTM(input_dim=1,\n", "                 hidden_dim=[32,32],\n", "                 kernel_size=(5, 5),\n", "                 num_layers=2)\n", "\n", "\n", "\n", "loss_function=nn.MSELoss()\n", "optimizer_nox = torch.optim.SGD(model_nox.parameters(), lr=0.03,momentum=0.2, weight_decay=6e-4)\n", "optimizer_no2 = torch.optim.SGD(model_no2.parameters(), lr=0.02,momentum=0.2, weight_decay=6e-4)\n", "optimizer_no = torch.optim.SGD(model_no.parameters(), lr=0.02,momentum=0.15, weight_decay=6e-4)\n", "optimizer_o3 = torch.optim.SGD(model_o3.parameters(), lr=0.02,momentum=0.2, weight_decay=6e-4)\n", "optimizer_pm25 = torch.optim.SGD(model_pm25.parameters(), lr=0.04,momentum=0.4, weight_decay=6e-4)\n", "\n", "\n", "attr_dic={\n", "    'nox':model_nox,\n", "    'no2':model_no2,\n", "    'no':model_no,\n", "    'o3':model_o3,\n", "    'pm2.5':model_pm25\n", "}\n", "\n", "index_dic={\n", "    'nox':0,\n", "    'no2':1,\n", "    'no':2,\n", "    'o3':3,\n", "    'pm2.5':4\n", "    \n", "}\n", "\n", "optimizer_dic={\n", "    'nox':optimizer_nox,\n", "    'no2':optimizer_no2,\n", "    'no':optimizer_no,\n", "    'o3':optimizer_o3,\n", "    'pm2.5':optimizer_pm25\n", "}\n", "\n", "\n", "#下面的dic其实没啥用，留作备用\n", "loss_train_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "loss_val_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "value_train_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "value_val_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "ojNa4lnVWUSO", "colab_type": "text"}, "source": ["# 训练和测试函数"]}, {"cell_type": "code", "metadata": {"id": "yNE6Evl4i_40", "colab_type": "code", "colab": {}}, "source": ["def train_model(attr,model):\n", "  model.train()\n", "  print('训练',attr,'模型')\n", "  for i in range(epochs):\n", "    #train\n", "    add=0\n", "    for seq,label in train_inout_seq:   \n", "        optimizer_dic[attr].zero_grad()\n", "        seq=seq.to(device)\n", "        label=label.to(device)\n", "        seq=seq.unsqueeze(0).unsqueeze(0).unsqueeze(0)\n", "        y_pred = model(seq)\n", "\n", "        if(i==epochs-1):  #对最后一次epoch的值进行记录\n", "          value_train_dic[attr].append(y_pred)\n", "        single_loss = loss_function(y_pred, label[0,index_dic[attr]])   #这里只预测label[attr]的数值，即某个单一的空气污染物\n", "        add+=single_loss\n", "        single_loss .backward()\n", "        optimizer_dic[attr].step()\n", "    loss_train=add/len(train_inout_seq)\n", "    loss_train_dic[attr].append(loss_train)\n", "\n", "\n", "    #val\n", "    add=0 \n", "    t=0\n", "\n", "    val_inputs=train_data[-train_window:]\n", "    fut_pred = len(val_data)\n", "\n", "    for seq,label in val_inout_seq:\n", "      with torch.no_grad():\n", "        seq = val_inputs[-train_window:].to(device)\n", "        label=label.to(device)\n", "        seq=seq.unsqueeze(0).unsqueeze(0).unsqueeze(0).to(device)\n", "        y_pred=model(seq)\n", "        single_loss=loss_function(y_pred,label[0,index_dic[attr]])  \n", "\n", "        add+=single_loss\n", "\n", "        if(i==epochs):  #对最后一次epoch的值进行记录\n", "          value_val_dic[attr].append(y_pred)\n", "          \n", "        temp=copy.deepcopy(val_data[t])\n", "        temp[index_dic[attr]]=y_pred\n", "        temp=temp.view(1,-1)\n", "        \n", "        val_inputs=torch.cat((val_inputs,temp),0)\n", "        t+=0\n", "\n", "    loss_val=add/len(val_inout_seq)\n", "    loss_val_dic[attr].append(loss_val)\n", "\n", "    print(f'epoch: {i:3}  train_loss:{loss_train:10.8f} val_loss:{loss_val:10.8f}')\n", "  print('----------------------')"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "bTXI3MxItlVd", "colab_type": "code", "colab": {}}, "source": ["from sklearn.metrics import mean_squared_error\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "def test_model(attr,model):\n", "  temp=torch.cat((train_data,val_data))\n", "  test_inputs=temp[-train_window:,:]\n", "\n", "  fut_pred = len(test_data)\n", "  test_list=[]\n", "  test_results=copy.deepcopy(test_data)\n", "\n", "  model.eval()\n", "\n", "  for i in range(fut_pred):\n", "      seq = test_inputs[-train_window:].to(device)\n", "      seq=seq.unsqueeze(0).unsqueeze(0).unsqueeze(0)\n", "      with torch.no_grad():\n", "          # model.hidden = (torch.zeros(1, 1, model.hidden_layer_size),\n", "          #                 torch.zeros(1, 1, model.hidden_layer_size))\n", "          y_pred=model(seq)\n", "\n", "          temp=copy.deepcopy(test_data[i]).view(1,-1)\n", "          # temp[index_dic[attr]]=y_pred\n", "          # temp=temp.view(1,-1)\n", "          test_inputs=torch.cat((test_inputs,temp),0)\n", "          test_results[i]=y_pred\n", "\n", "\n", "\n", "\n", "  actual_predictions = scaler.inverse_transform(np.array(test_results.cpu()))\n", "\n", "\n", "\n", "\n", "  x = np.arange(len(train_data)+len(val_data), len(dataset), 1)\n", "  plt.figure(figsize=(8, 6))\n", "  plt.grid(True)\n", "\n", "  plt.plot(dataset.loc[len(dataset)-len(test_data):,attr+'_B'].values,color=\"red\",label='real value')\n", "  plt.plot(actual_predictions[:,index_dic[attr]],label='prediction')\n", "\n", "  plt.title('hours vs '+attr)\n", "  plt.ylabel(attr)\n", "  plt.xlabel('hour')\n", "\n", "  plt.legend(loc='upper right',fontsize=15)\n", "\n", "  y_true=dataset.loc[len(dataset)-len(test_data):,attr+'_B'].values\n", "  y_pred=actual_predictions[:,index_dic[attr]]\n", "  print('mse: ',mean_squared_error(y_true, y_pred))\n", "  print('mae: ',mean_absolute_error(y_true, y_pred))"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "vmwklNZ1ugFG", "colab_type": "code", "colab": {}}, "source": ["def predict_future(attr,model):\n", "  temp=torch.cat((train_data,val_data))\n", "  test_inputs=temp[-train_window:,:]\n", "\n", "  fut_pred = 96\n", "  test_list=[]\n", "  test_results=copy.deepcopy(test_data)\n", "\n", "  model.eval()\n", "\n", "  for i in range(fut_pred):\n", "      seq = test_inputs[-train_window:].to(device)\n", "      seq=seq.unsqueeze(0).unsqueeze(0).unsqueeze(0)\n", "      with torch.no_grad():\n", "          y_pred=model(seq)\n", "          temp=copy.deepcopy(test_data[i])\n", "          temp[index_dic[attr]]=y_pred\n", "          temp=temp.view(1,-1)\n", "          \n", "        #这一步可能比较模糊。首先我们要明确一点的是，y_pred是一个常数。但是我们的val的是nx8维的数。也就是一个\n", "        #数据就有8维。我们对val_inputs的更新实际上是应该更新8维的数。可因为我们是多变量预测单变量，我们每次只是预测一个值\n", "        #所以，解决方法就是，我先把未来的验证集里的8个数添加进去。然后用得出来的值取覆盖相应污染物的值\n", "          test_inputs=torch.cat((test_inputs,temp),0)\n", "          test_results[i]=y_pred\n", "\n", "\n", "\n", "\n", "  actual_predictions = scaler.inverse_transform(np.array(test_results.cpu()))\n", "\n", "\n", "\n", "\n", "\n", "  plt.figure(figsize=(8, 6))\n", "  plt.grid(True)\n", "\n", "  plt.plot(dataset.loc[len(dataset)-len(test_data):len(dataset)-len(test_data)+fut_pred,attr+'_B'].values,color=\"red\",label='real value')\n", "  plt.plot(actual_predictions[:fut_pred,index_dic[attr]],label='prediction')\n", "\n", "  plt.title('hours vs '+attr)\n", "  plt.ylabel(attr)\n", "  plt.xlabel('hour')\n", "\n", "  plt.legend(loc='upper right',fontsize=15)\n", "\n", "  y_true=dataset.loc[len(dataset)-len(test_data):len(dataset)-len(test_data)+fut_pred-1,attr+'_B'].values\n", "  y_pred=actual_predictions[:fut_pred,index_dic[attr]]\n", "\n", "  print('mse: ',mean_squared_error(y_true, y_pred))\n", "  print('mae: ',mean_absolute_error(y_true, y_pred))\n", "\n", "  y_pred=pd.DataFrame(y_pred)\n", "\n", "  y_pred.to_csv('/content/drive/My Drive/air_inference/result24/convlstm'+attr+'.csv',index=False)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "AEzeCfiTWZFP", "colab_type": "text"}, "source": ["# 训练"]}, {"cell_type": "code", "metadata": {"id": "dcEij6uAMKg5", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 170}, "executionInfo": {"status": "ok", "timestamp": 1598547920404, "user_tz": -60, "elapsed": 490853, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "355145f2-e333-492f-9b98-f2d79e0bbac5"}, "source": ["%%time\n", "train_model('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 nox 模型\n", "epoch:   0  train_loss:0.00711064 val_loss:0.05473236\n", "epoch:   1  train_loss:0.00104175 val_loss:0.05616401\n", "epoch:   2  train_loss:0.00094619 val_loss:0.05655846\n", "epoch:   3  train_loss:0.00093307 val_loss:0.05677297\n", "epoch:   4  train_loss:0.00092688 val_loss:0.05690625\n", "----------------------\n", "CPU times: user 5min 17s, sys: 2min 29s, total: 7min 47s\n", "Wall time: 7min 51s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "RRsvxtbiuDaF", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598547922978, "user_tz": -60, "elapsed": 493418, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "bc275e80-e752-46fe-f849-24b3d6038a45"}, "source": ["test_model('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  76.31633469727993\n", "mae:  7.128202202793883\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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******************************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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "elwU6f9supSh", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598547925032, "user_tz": -60, "elapsed": 495466, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "a2ebdd2f-5cd5-49cc-afa9-ba3e33208961"}, "source": ["predict_future('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  385.7930875042884\n", "mae:  17.75337888236946\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "gvn89ILdjgYX", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 170}, "executionInfo": {"status": "ok", "timestamp": 1598548883823, "user_tz": -60, "elapsed": 460315, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "e456f3ff-ea61-4f4b-a13c-724c7152616d"}, "source": ["%%time\n", "train_model('no2',model_no2)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 no2 模型\n", "epoch:   0  train_loss:0.01160373 val_loss:0.06451733\n", "epoch:   1  train_loss:0.00163263 val_loss:0.07736301\n", "epoch:   2  train_loss:0.00126573 val_loss:0.07762288\n", "epoch:   3  train_loss:0.00123732 val_loss:0.07761996\n", "epoch:   4  train_loss:0.00122068 val_loss:0.07758631\n", "----------------------\n", "CPU times: user 5min 11s, sys: 2min 24s, total: 7min 35s\n", "Wall time: 7min 39s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "uiobF6CHvCYW", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598548886453, "user_tz": -60, "elapsed": 460796, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "e6feeffc-dbda-46a7-d9e3-a7b113751c22"}, "source": ["test_model('no2',model_no2)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  9.3703194314121\n", "mae:  2.4995662114624673\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "xBxzon8gusbY", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598548888299, "user_tz": -60, "elapsed": 461243, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "d1195077-8ac4-47f4-a440-5402963b5eb1"}, "source": ["predict_future('no2',model_no2)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  27.333477018531596\n", "mae:  3.873051344707322\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "5yEx1bQSLxJu", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 170}, "executionInfo": {"status": "ok", "timestamp": 1598549978707, "user_tz": -60, "elapsed": 485088, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "ac3a1fad-93db-4819-e50a-40fd6ec1df04"}, "source": ["%%time\n", "train_model('no',model_no)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 no 模型\n", "epoch:   0  train_loss:0.01091678 val_loss:0.02220598\n", "epoch:   1  train_loss:0.00274489 val_loss:0.03615083\n", "epoch:   2  train_loss:0.00182557 val_loss:0.03683767\n", "epoch:   3  train_loss:0.00163978 val_loss:0.03682545\n", "epoch:   4  train_loss:0.00152269 val_loss:0.03671976\n", "----------------------\n", "CPU times: user 5min 22s, sys: 2min 21s, total: 7min 44s\n", "Wall time: 7min 48s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "7xIGo5JwP5eQ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598549981184, "user_tz": -60, "elapsed": 487555, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "83dae943-3a01-4a7e-a478-5e8bd7140abf"}, "source": ["test_model('no',model_no)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  7.093799530837947\n", "mae:  2.037917517853848\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "t9rw2Y4Buu6o", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598549983234, "user_tz": -60, "elapsed": 489464, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "e7b6e646-1fa4-4a75-ca02-1e20b736c1c2"}, "source": ["predict_future('no',model_no)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  52.071769250718496\n", "mae:  6.131988759030087\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAfQAAAGDCAYAAADd8eLzAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAgAElEQVR4nOzdd3hURdvH8e8kpAKBBEiA0HsvBlAUFVBAFLEhRcX6CCjyqCgKioCKXewoKgqoYCiiKNiVqM+rCAGRXpQeAiG0kJCElHn/OAFBWgK7Odnk97muvcye3TPn3nH13pkzxVhrEREREd/m53YAIiIicvaU0EVERIoBJXQREZFiQAldRESkGFBCFxERKQaU0EVERIoBJXSRIsgYs8kYc6nbcYiI71BCFxERKQaU0EVKMGNMKbdjEBHPUEIXKbpaGWOWGWP2G2OmG2OCD79gjLnTGPOXMWaPMeZzY0zVvOO1jDH26ERtjIkzxvwn7+9bjTH/Z4x52RizGxhjjKlnjPkp7zrJxpjpJwrGGPOVMeaefx370xhzrXG8bIxJMsakGGOWG2OanaScOGPMk3lxHDDGfGuMqXjU6z2NMSuNMfvy3tv4rGpRpIRQQhcpunoDlwG1gRbArQDGmM7AM3mvVwE2A7EFKPdcYAMQBTwFPAl8C4QD1YDXT3Lex0C/w0+MMU2AmsA8oCtwEdAAKJcX2+5TxHADcBsQCQQCD+aV2SDvOvcBlYAvgS+MMYEF+HwiJZISukjR9Zq1dru1dg/wBdAq7/iNwPvW2iXW2kxgBNDeGFMrn+Vut9a+bq3NttamA1k4ibmqtTbDWvu/k5z3KU6vQc2j4pidF0MWUBZoBBhr7WprbeIpYphkrV2Xd/0ZR322PsA8a+131tos4EUgBDg/n59NpMRSQhcpunYc9fdBoEze31VxWuUAWGtTcVrD0fksd+u/nj8EGGBhXlf37Sc6yVp7AKc13jfvUD9gat5rPwJvAOOBJGPMO8aYsFPEkN/PlpsXb34/m0iJpYQu4nu247SoATDGlAYqAAlAWt7h0KPeX/lf5x+zxaK1doe19k5rbVVgIPCmMabeSa79MdDPGNMeCAbmH1XOa9baGKAJTtf7sIJ+MI7/bAaojvPZROQUlNBFfM/HwG3GmFbGmCDgaeB3a+0ma+0unOR3kzHGP6+1XfdUhRljrjfGVMt7uhcn4eee5O1f4iTcJ4DpeS1ojDFtjTHnGmMCcH5UZJyijFOZAVxhjLkkr6wHgEzg1zMoS6REUUIX8THW2u+Bx4BPgESchN33qLfcidM63g005fTJsC3wuzEmFfgcuNdau+Ek184EZgOXAtOOeikMeBfnB8HmvGu/UKAP5pS/FrgJZ2BeMnAlcKW19lBByxIpaYy19vTvEhERkSJNLXQREZFiQAldRESkGFBCFxERKQaU0EVERIoBJXQREZFiwCd2WqpYsaKtVauWx8pLS0ujdOnSHitPVKeepvr0PNWpZ6k+Pe/oOl28eHGytbZSQc73iYReq1Yt4uPjPVZeXFwcHTt29Fh5ojr1NNWn56lOPUv16XlH16kxZvOp3308dbmLiIgUA0roIiIixYASuoiISDGghC4iIlIMKKGLiIgUAz4xyl1EpLhJSUkhKSmJrKwst0M5I+XKlWP16tVuh+GTAgICiIyMJCwszKPlKqGLiBSylJQUdu7cSXR0NCEhIRhj3A6pwA4cOEDZsmXdDsPnWGtJT08nISEBwKNJXV3uIiKFLCkpiejoaEJDQ30ymcuZM8YQGhpKdHQ0SUlJHi1bCV1EpJBlZWUREhLidhjiopCQEI/fblFCFxFxgVrmJZs3/v0roYuIiBQDGhQnIiKekZPjPLKznYcx4O/vPEqVAj8/55h4hRK6iIjkj7WQmQkHDxK4fz8kJ8OhQ84jKwtyc099vp8fhIQ4j+BgCA2FMmWc4/8SFxdHp06dWL58Oc2aNfPSB3Js2rSJ2rVr88UXX9CjRw+vXsublNBFROTkMjNhzx7Yvx8OHjyStAMBAgMhIMBJzIGBTiv86Ie1/7TYc3KcxJ+eDvv2OcfAabGXLQthYc4jJESt+DOkhC4iIsfKzYW9e2H3bkhJcY6VLg0VKzrJOzSU1Oxsyh41hzonJ4ecnBwCAwPzd42sLOcHQkqK89i2zTkeHAwVKjjJXwpEg+JERMSRkwOJibBsGWzcCBkZULUqNG8OjRtDjRpHkvqgu+6iTZs2fPbZZzRt2pTg4GB+//13AObMmUObNm0IDg6mcuXKPPTQQ8dM0VqzZg19+/enerNmhDZsSNPevXklLo7c6tWdFn9CgnN9cHoGrD0u1LS0NEqXLs348eOPe61t27bcdNNNACQmJnL77bdTp04dQkJCaNCgASNHjuTQaX4wGGN44403jjk2ZswYKlaseMyxLVu20LdvXyIiIggNDaVbt26sXbv29HXtBWqhi4iUdNnZsGsX7Nzp/B0WBpUrO13hp+j+3rRpEw899BCjRo2icuXK1K5dmxkzZtCvXz8GDhzI008/zd9//82IESPIzc3lxRdfBCAhIYGGDRty4403UrZsWZYuXcro0aNJz8pixIgRTjf/pk3ORbZuheXLISrK+THh7w9A6dKl6dGjBzNmzGDw4MFHYtqwYQPx8fGMHj0agOTkZCIiInjppZcIDw9n3bp1jBkzhl27dvH222+fVbXt2bOHDh06UKFCBSZMmEBoaCjPPvssl156KevWrSv0tQaU0EVEioL77oOlSwv3mrm5Ttd37dowdCiUK+e0yEuXztfpu3fv5vvvv6dVq1aAs6zpsGHDuPnmm3nzzTePvC8oKIjBgwczYsQIKlSowCWXXMIll1xy5JwOHTpw8OBB3n33XSehBwU5yRucXoGgICexb98OlSo5PzZKlaJv37706tWL7du3U7VqVQCmT59OeHg43bp1A6B58+ZHfkgAXHDBBZQuXZrbb7+d119/Pf+3CE7g5ZdfJi0tjaVLlxIREXGk/Fq1avH+++8f80OjMKjLXUSkpMnJcQanpaU596qDgpwu9fr1853MAaKjo48kc4B169axZcsWevfuTXZ29pFH586dycjIYMWKFQBkZGQwevRo6tWrR1BQEAEBATz66KNs3LiR7MOD5Q4LC4OGDZ34wsJgxw6nxZ6YSPeuXSlTpgwzZ8488vbp06dzzTXXEBAQADg/GF555RWaNGlCSEgIAQEB3HjjjWRmZrJly5azqET4/vvv6dKlC2FhYUc+a9myZYmJiSE+Pv6syj4TaqGLiBQFr7zi/WukpzuDz/bvd7quK1aEyEgnoZ+BqKioY54nJycDcPnll5/w/Vu3bgXg4YcfZuLEiYwePZpzzjmH8uXLM2fOHMaOHUtGRgZlypQ5/uTSpaFuXWcgXUICJCQQnJTEVd26MX36dO69917Wrl3Ln3/+yQsvvHDktFdeeYVhw4bx8MMPc/HFFxMeHs6iRYsYPHgwGRkZZ/S5j/68CxYsYPr06ce9drgHojApoYuIFHdZWU539a5dTiKPjnYSed796DP17+VLD3c7v/POO7Ru3fq499euXRuAmTNnMmTIEB566KEjr82bNy9/Fw0NdXoSUlMhIYE+55/PlUOHsmXlSqZ/8gmVKlWic+fOR94+c+ZMevXqxVNPPXXk2KpVq057maCgoOMGzu3du/eY5xEREfTs2ZPHHnvsuPPd2IlOCV1EpLjKyYGkJGfkurVOEq9SxRlJ7gUNGzYkOjqaTZs2ceedd570fenp6QQd1SuQk5NDbGxswS5Wpgw0aEDX8HDKjx7NjHfeYfqXX9LrmmvwP+qHyr+vBTB16tTTFl+tWrVj9nvPzc3lhx9+OOY9l1xyCTNmzKBp06ZFYrMdJXQRkeLGWmcOeUKC0zovXx6qVXPmeHuRn58f48aNo3///qSkpNC9e3cCAwPZsGEDn332GbNmzSI0NJQuXbowfvx46tWrR0REBOPHjyczM7PgFzSGgMhIru3Vi5emTydx507eHDbM6Y2oXBn8/OjSpQuvvfYa5557LnXr1mXq1Kn89ddfpy36mmuuYfz48bRu3Zo6deowceJEUg7Pyc8zdOhQPvroIzp37syQIUOIjo5m586d/PTTT3To0IF+/foV/DOdBSV0EZHiJCXFGRGenu7cd65Tx5l+Vkj69OlDWFgYTz/9NO+//z7+/v7UqVOHHj16HBlR/vrrrzNo0CAGDx5MSEgIt9xyC9dccw0DBgw4o2v27deP995/n6pVq3Jhp05OQt+zB2rWZNSoUezatYuRI0cCcO211/Laa69x5ZVXnrLM0aNHk5SUxMiRIwkMDOSee+6hadOmx8x7r1ixIgsWLODRRx/l/vvvZ9++fVSpUoUOHTrQokWLM/osZ8PYE0zYL2ratGljPTliMC4ujo4dO3qsPFGdeprq0/OKUp2uXr2axo0be7bQQ4ecRL53rzPILToawsO9tozqgQMHXLlPnC/798OWLc589goVnN4JL91mOBv//h4c/R01xiy21rYpSHlqoYuI+DJrnfvkCQnO31WrHuluLrHKlYMmTZyxAzt3OmvHR0c7c9iL8TrxSugiIr4qLc1ZUS093UlihxdhEWcEf7VqTgt9yxbnkZzs1NGJpsUVA0roIiK+JjfXaX0mJjpdyXXrOgPfinHr84yFhECDBs6tiK1bYc0aJ8lXrVrsfvwooYuI+JLUVKdVnpHhLAxTrZqzVamcnDEQEeH0Yhzuht+zx+mC9+I0vsKmb4GIiC+w1ln2NCHB2Xu8fn0nQUn+He6Gj4x0RsInJTnd8FFRzsPHfxh5PXpjjD8QDyRYa3sYY2oDsUAFYDHQ31qrjW9FRE4mK8vZTjQlxWlp1qx51qu8lWiBgVCrlpPEt293Wu1JSU6i9+HEXhjDIO8FVh/1/DngZWttPWAvcEchxCAi4ptSU2H1ajhwwBnQVbu2krmnhIQ44w+aNHHm6icmOhu/JCQ428j6GK8mdGNMNeAKYGLecwN0BmblvWUKcLU3YxAR8UmHp6OtXes8b9TIaUFq4JvnhYZCvXpOYg8LcxL7ihXOvfbcXLejyzdv9yu8AjwEHF59oAKwz1p7+KfPNiDayzGIiPiW3Nx/plmVK+e0yn20G9inhIY6Lfa0NKeVvnWrk9Sjo51bHUX8x5TXviHGmB5AkrV2sTGm4xmcPwAYAM4WfXFxcR6LLTU11aPlierU01SfnleU6rRcuXIcOHDghK+ZrCxCtm/HPyODzIgIDlWs6MwzL2JycnJO+hm85e2332bYsGFH1lT/5ZdfuOKKK1iwYAFNmjTJVxmTJk2iUqVK9OjR45jjzZo146qrrvpnV7YqVfAPCyNo1y78N24ke8cOMipXxuYtX+sJGRkZx3wnz/Y76s2ffBcAPY0xlwPBQBjwKlDeGFMqr5VeDUg40cnW2neAd8BZ+tWTSzYWpSUgiwvVqWepPj2vKNXp6tWrT7xsamqq0yrMyYE6dQiKiKCozpR2Y+nX4LzNZQ5ft0OHDvz222+0aNEi37udffDBBzRr1uy4jVM+++wzKlSocOxnKlvWGSSXnEypbdsos2mT01qPivJIaz04OPiYbWbP9jvqtXvo1toR1tpq1tpaQF/gR2vtjcB8oFfe224B5ngrBhERn3B4Stratc6SrY0aOV28xUy6h3sawsLCOO+88zyydWnr1q2pUaPG8S8Y48xXb9rUuf2xbZszSLEI9pq4sdjvw8BQY8xfOPfU33MhBhGRoiE7G/7+20kU5cpB48bOvdwibtCgQbRp04bPPvuMRo0aERwcTIcOHVi1atWR9xhjeOmll7jvvvuoVKkSzZs3B5yu5oceeojq1asTFBREy5Yt+fLLL48pPzMzk3vuuYfy5csTERHB/fffT1ZW1jHviYuLwxjDihUrjhzLycnhmWeeoUGDBgQFBVGtWjVuvfVWADp27MjixYuZMmUKxhiMMUyePBmAWrVq8eCDDx5T/owZM2jevDlBQUFUr1uXR99/n+waNZyNcFatYvKrr2KMYfny5XTp0oXSpUvTqFEjZs+e7alqLpBCSejW2jhrbY+8vzdYa9tZa+tZa6+31p7BJrgiIsVAWprT2tu/H6pXdwZk+dDgt82bNzN06FAee+wxpk2bxv79++nWrRsZGRlH3vPCCy+QmJjIhx9+yGuvvQZAr169mDx5Mo888ghffPEFbdu2pWfPnixduvTIecOHD2fixIk89thjTJ06lc2bNzNu3LjTxjRw4EBGjx5N7969mTt3LuPGjePgwYMAvPnmmzRq1IjLL7+c3377jd9++40rrrjihOV8++239OnTh3POOYc5c+YwZMgQXhw3jntGjfqntb5nDwA39OtHz549+fTTT6lfvz59+/Zl27ZtZ1yvZ8p3vjkiIsXJzp1OqzwgABo25PH5m1m1fYMroTSpGsboK5sW+Lzk5GTmzJnD+eefD0BMTAx169Zl8uTJDBo0CIAqVaowffr0I+f88MMPzJs3j7i4OC6++GIAunbtyrp163jqqaeYOXMmu3fvZsKECTz++OM88MADAHTr1u20A9/WrFnDe++9x6uvvsp///vfI8f79OnjfM4mTShdujSVKlXivPPOO2VZo0aNomPHjkyZMgWAyy67DIARI0YwcuRIqtWt6yy9C9x/3XXcfsMNEBFBTEwMUVFRzJ0790gdFJYSvL+eiIgLdu925pdv3frPNp8+uvtXZGTkkWQOULNmTWJiYli4cOGRY5dffvkx53z//fdUrlyZCy64gOzs7COPSy65hPj4eACWL19ORkYGV1111ZHz/Pz8jnl+IvPnzwc40sV+pnJycliyZAnXX3/9Mcf79OlDbm4uv/32m3NvPW8AXddOnZx/p0CFChWIjIxUC11EpFj7+We44QaYOPG4hWLOpIXstsjIyBMeS0xMPPI8KirqmNeTk5PZsWMHASfYEMU/bwW8HTt2nLD8E13vaLt376Z06dKEhYXl7wOcRHJyMllZWcfFfvj5nryu9sPKx8RAcPCRf5eBgYHH3HYoLEroIiLetmgRjB0Ln3/urEhWpYoz9cnHJeW1Sv99rGnTf36cmH9N74qIiCA6OprPPvvspOVWrlz5SFkRR432P9H1jlahQgXS0tJISUk5q6ResWJFAgICjrvezp07AY6JCXASeRHYsU1d7iIi3vK//8Fll0G7dvDLL/D447BkibM5SDGQlJTEr7/+euT5li1bWLJkCe3atTvpOZdccgk7duygTJkytGnT5rgHQPPmzQkODmbOnH9mNefm5h7z/EQ6d+4MOHPNTyY/rWd/f39iYmKYOXPmMcdnzJiBn58f7du3P+X5blELXUTE09asgQcfhHnznG71556Du+46cs+1uKhYsSI33XQTY8eOJSQkhNGjRxMZGXnKe9hdunShW7dudOnShYcffpimTZuSkpLC0qVLycjI4JlnnqFChQoMGDCA0aNHU6pUKZo2bcq7775LamrqKeNp2LAhAwYM4IEHHiApKYmLLrqIffv2MWvWLGJjYwFo1KgR33zzDd988w0VKlSgdu3aVKhQ4biyHn/8cbp168Ztt91G3759Wb58OY899hh33nkn1apVO6t68xYldBERT9mzx2mFv/mms5PXs8/CkCE+Ma/8TNSsWZNHHnmE4cOHs3nzZtq0acO0adOOrOh2IsYYZs+ezdNPP80rr7zCli1biIiIoFWrVgwZMuTI+55//nmysrJ44okn8PPz46abbmLo0KFHRr2fzJtvvknNmjWZOHEizz77LJGRkXTt2vXI6yNHjmTLli307t2blJQUJk2adMIfIF27diU2NpaxY8cydepUIiMjeeCBB3j88ccLXlGFxVpb5B8xMTHWk+bPn+/R8kR16mmqT8/zep1+/LG14eHW+vlZO3CgtTt3nvStq1at8m4sheCGG26wnv5/c0nz7+/B0d9RIN4WMFfqHrqIyNmw1mmV9+vnrPK2dClMmOB0tYsUInW5i4icqYwMuOMOmDYNbrkF3n4bgorqdipS3Cmhi4iciV274Oqr4ddf4emnYfjwIr9ftidNmDCh0Hdbk1NTQhcRKaikJLj4Yti0CWbMgH+tKCbiBiV0EZGC2LcPunWDzZvhm2/goovcjkgEUEIXEcm/1FS4/HJYuRK++OKskrm19rhV1KTkcAaye5ZGuYuI5EdGhnPPfOFCiI11WulnKCAggPT0dA8GJ74mPT39hOvZnw0ldBGR08nOhj594IcfYNIkuPbasyouMjKShIQEDh486JWWmhRd1loOHjxIQkLCaTebKSh1uYuInEpuLvznP87GKuPHQ//+Z13k4Y1Dtm/fTlZW1lmX54aMjIxTrggnJxcQEEBUVNRZ7wr3b0roIiInYy089BBMmQJPPAF33+2xosPCwjz+P/TCFBcXR+vWrd0OQ46iLncRkZN5/nkYNw7uuQdGjnQ7GpFTUkIXETmR995zFovp1w9efbVELRojvkkJXUTk377+GgYMcEayT54MfvpfpRR9+paKiBxtzRpnRHuLFjBrFgQGuh2RSL4ooYuIHLZ3L/TsCcHBMGcOlCnjdkQi+aZR7iIi8M9c802bYP58qFHD7YhECkQJXUQE4MEH4bvvnMFwF1zgdjQiBaYudxGRyZOdkez33Qe33+52NCJnRAldREq2pUvhrrugc2d44QW3oxE5Y0roIlJy7dsH110HFSrAxx9DKd2FFN/ltW+vMSYY+BkIyrvOLGvtaGPMZOBiYH/eW2+11i71VhwiIieUmwu33AJbtsDPP4OHN8oQKWze/DmaCXS21qYaYwKA/xljvsp7bZi1dpYXry0icmovvOBsuPLqq9C+vdvRiJw1ryV06+wJmJr3NCDvoX0CRcR98+fDI48409SGDHE7GhGPMN7ci9cY4w8sBuoB4621D+d1ubfHacH/AAy31mae4NwBwACAqKiomNjYWI/FlZqaShktGOFRqlPPUn163uE6LZWSQts77iA7NJQlEyaQExLidmg+Sd9Rzzu6Tjt16rTYWtumIOd7NaEfuYgx5YFPgSHAbmAHEAi8A/xtrX3iVOe3adPGxsfHeyyeuLg4Onbs6LHyRHXqaapPz4uLi6PjxRdD797OKnC//w7a/vOM6TvqeUfXqTGmwAm9UEa5W2v3AfOBy6y1idaRCUwC2hVGDCIifPihsz77k08qmUux47WEboyplNcyxxgTAnQB1hhjquQdM8DVwApvxSAiclhwYqKzr/mFFzqrwokUM94c5V4FmJJ3H90PmGGtnWuM+dEYUwkwwFJgkBdjEBGBnBwaP/20s6f5Bx+Av7/bEYl4nDdHuS8DjuvTstZ29tY1RURO6PnnKbdihZPMa9VyOxoRr9BKcSJSvK1cCaNHk3TxxXDTTW5HI+I1SugiUnzl5MB//gNhYay/7z6ny12kmNLCxSJSfI0fDwsWwIcfklW+vNvRiHiVWugiUjxt3uysBnfZZXDjjW5HI+J1SugiUvxYCwMHOn9PmKCudikR1OUuIsXP1KnwzTfOxis1a7odjUihUAtdRIqX3bvhvvvgvPNg8GC3oxEpNEroIlK8jB4Ne/fCO+9oARkpUZTQRaT4WLnSuWc+aBA0b+52NCKFSgldRIoHa+H++6FsWXj8cbejESl0GhQnIsXD3Lnw3XfwyitQsaLb0YgUOrXQRcT3HToEDzwAjRrB3Xe7HY2IK9RCFxHf9/rrsH49fPklBAS4HY2IK9RCFxHflpQETzwBl18O3bu7HY2Ia9RCFxHf9uSTkJYG48a5HYkUQGpmNht3pbFt70ES9qWzbW86ifvT2Z+exYGMbA5kZJOamY21llL+fpTyM5TyN5QNCiAyLIiossFEhQVRpXwIDaLK0rByWcoEleyUVrI/vYj4tg0b4O23nR3VGjVyOxo5gdxcy4bkVJZt28/qxBTW7Uzlr6RUEvalH/O+0oH+VC0fQvnQAKLCgqkXWYqywaXwM4asHEt2Ti7ZuZYDGVnsTMlk1fYUklMzybX/lFEtPIRGlcNoUyucdrUjaB5djgD/ktMRrYQuIr7rscegVCkYNcrtSCTP/vQsFm/ew8KNe1m6dS8rElJIzcwGILCUH/UqlaFNrXBuiKpB3UplqBYeQrXwEMqFBGAKuOZ+dk4u2/dlsHbnAdbuSGHtzlRWbt/P96t3AhAS4M85NcvToV4lOjeKpEFUmQJfw5cooYuIb1q6FKZNg+HDoWpVt6MpsTKycvhtw25+XreL3zfsYfWOFKyFAH9DkyphXNM6mhbVytGiWnnqRZbB389zCbWUvx81KoRSo0IoXZpEHTm+60Am8Zv28PvGPSzYsJvnvl7Dc1+vIbp8CB0bVuKSxpGcX7ciwQHFayVBJXQR8U2PPALh4fDww25HUuIk7Evn+81ZTJ60kN/+3k1mdi5BpfyIqRnOvZfUp13tCFpXDyck0J2EWalsEN2bV6F78yoA7NifQdzaJOavTeKzPxKY+vsWQgP9ubhBJbo2jaJzwyjKhfr+7AgldBHxPT/9BF99Bc8/D+XLux1NifBXUirfrNzBNyt3sGzbfgBqVUijX7sadGxYifPqVCiyLd7K5YLp264GfdvVIDM7hwUb9vDtyh18t2onX63Ygb+foU3NcC5tHMUljSOpU6mM2yGfESV0EfEt1jqt8uhouOcet6Mp1tbvPMC85Yl8uTyRdTtTAWhVvTwPX9aI8LTN9L2ik8sRFlxQKadlfnGDSjx5VTOWbtvH96t28uOaJJ76cjVPfbma2hVLc3GDSlxYvyLn1alAaR8ZPe8bUYqIHPbZZ/D77/DuuxAS4nY0xc5fSQeYu+yfJG4MtK0VwZgrm9CtWWWqlHPqPC5uq8uRnj0/P8M5NcI5p0Y4D13WiK17DjJ/bRI/rkkidtEWJv+6iQB/5z0X1KtI+7oVaFmtPIGliubIeSV0EfEdmZkwbBg0bgy33up2NMXG37tSmbcskXnLElm788CRJP54z6Z0b1aZyLBgt0MsFNUjQrm5fS1ubl+LzOwcFm/ay8/rk/ll/S5e/n4dL33njJxvWzuC686J5qpW0W6HfAwldBHxHS+/DH//Dd9840xXkzOWuD+dL/7czud/bmdFQoqTxGuWvCR+MkGl/Dm/XsW3jZcAACAASURBVEXOr1eR4d0bse/gIRZs2MNvfyfz24bd/JWU6naIx9F/ESLiG7Zvh7FjoWdP6NrV7Wh8UkpGFl8tT2T2kgQWbtqDtdCyWjlGXtGYHi2qUrlcyU7ip1I+NJDLmlXmsmaVAWfBnKJGCV1EfMPw4ZCVBS+95HYkPiU7J5df1ifzyZJtfLdqJ5nZudSpVJr7L21Az5ZVqVWxtNsh+iQ/D86n9xQldBEp+hYsgA8/hBEjoG5dt6PxCZt3pzEjfisz47eRdCCT8NAA+ratzrXnVKNFtXLFesW0kkoJXUSKttxc+O9/ndXgHnnE7WiKtMzsHL5ZuZPYhVv49e/d+Bno2DCS3m2q07lRZJEdnS2e4bWEbowJBn4GgvKuM8taO9oYUxuIBSoAi4H+1tpD3opDRHzclCmwaJHTQi/jmwt+eNuW3QeZtnALM+O3sjvtENUjQniwawN6xVTXffESxJst9Eygs7U21RgTAPzPGPMVMBR42Voba4yZANwBvOXFOETEV+3b59w7b98ebrzR7WiKlJxcy/w1SXywYDM/r9uFv5/h0saR3HhuTTrUq1gk7/GKd3ktoVtrLXB4XH9A3sMCnYEb8o5PAcaghC4iJzJqFCQnO8u86p4vALtTM5kev5WpC7aQsC+dqLAg7ru0Pn3b1lBrvIQzTt71UuHG+ON0q9cDxgMvAAustfXyXq8OfGWtbXaCcwcAAwCioqJiYmNjPRZXamoqZdR151GqU89SfUKZ9euJGTSI7T17sv7ee8+6PF+v0w37cvh+SzYLE7PJttA4wo/ONQJoHelPKRda475en0XR0XXaqVOnxdbaNgU536sJ/chFjCkPfAo8BkzOT0I/Wps2bWx8fLzH4omLi6Njx44eK09Up55W4uszNxc6dIC//oK1a51d1c6SL9ZpZnYOc/9M5IPfNvHntv2UDvTn2nOqcXP7mtSPKutqbL5Yn0Xd0XVqjClwQi+UUe7W2n3GmPlAe6C8MaaUtTYbqAYkFEYMIuJDpkyB336DSZM8ksx9za4DmUz9fTMfLdhMcuoh6lYqzRNXNeWa1tGUDfb9bT7FO7w5yr0SkJWXzEOALsBzwHygF85I91uAOd6KQUR80N698NBDcP75cPPNbkdTqNbtPMA7P2/g86XbOZSTS+dGkdx2QS061KuoeeNyWt5soVcBpuTdR/cDZlhr5xpjVgGxxpixwB/Ae16MQUR8zciRsGcPjB8PfiVj3vTqxBRe+2E9X63YQUiAP33aVufWC2pR10f35RZ3eHOU+zKg9QmObwDaeeu6IuLDFi+Gt95y9jlv1crtaLxu1fYUXvl+Hd+u2knZoFIM6VyP2y+oTXjpQLdDEx+kleJEpGjIzYW774bISHjySbej8ap9Bw/xwjdrmbZwC2WDSnHfpfW57fzalAvV/XE5c0roIlI0TJwICxc6K8KVK+d2NF6Rk2uZvmgrL3yzhpSMbG49vxb3XdqAciFK5HL2lNBFxH27djkrwl18cbFdEW5jchr3xf7Bn9v20662s+944yphboclxYgSuoi4b/hwOHAA3nyzWK4IN2dpAo/MXk5AKT9e7duKni2ratS6eJwSuoi469df4f33YdgwaNLE7Wg8Kv1QDmM+X8n0+K20rRXOq31bU7V8iNthSTGlhC4i7snOdgbCVavmrNtejGzencadH8SzPimVezrV475L61PKv2RMwxN3KKGLiHvGj4c//4RZs4rV1qh/bt3H7ZMXkWstH9zejgvrV3I7JCkBlNBFxB0JCc4iMt27w7XXuh2Nx/y4ZieDp/5BxbKBTLmtHXW0OIwUEiV0EXHH/fc7Xe5vvFFsBsLFLtzCo5+toEmVMN6/tS2Vyga5HZKUIEroIlL4vv4aZs6EsWOhTh23o/GIt+L+5rmv13Bxg0q8eeM5lA7S/16lcOkbJyKFKz0dBg+Ghg3hwQfdjsYjDifzq1pV5cXrWxKgwW/iAiV0ESlcTz8NGzbAjz9CkO93SU/46Z9k/lLvVvj7FY/bB+J79DNSRArP2rXw3HNw003QqZPb0Zy1d37+m2e/WsOVLasy7vqWSubiKiV0ESk8990HoaHw4otuR3LWJv6ygae/XEOPFlV4uXdLzTEX16nLXUQKx5dfOoPhXnoJoqLcjuaszIzfyth5q7m8eWVe6dNKyVyKBH0LRcT7srJg6FBo0MAZEOfDvl+1k+Gzl3Nh/Yq80qe1krkUGWqhi4j3vfmmc//8iy8gMNDtaM7Y7xt2M3jaEppFl2PCTTEEllIyl6JD30YR8a7kZBgzBrp2hSuucDuaM7Zqewr/+SCeauEhTLq1reaZS5GjhC4i3jV6tLM16ksv+eyKcAn70rll0kLKBJXigzvOJaK07/YySPGlhC4i3rNiBUyYAIMGQdOmbkdzRlIysrht0kIysnKYcns7orX9qRRRSugi4j3DhkFYGDz+uNuRnJGsnFzu/mgJG3alMeGmGBpElXU7JJGT0k0gEfGOH390pqm98AJUqOB2NAVmreXRT5fzv7+SeaFXCy6oV9HtkEROSS10EfE8a+Hhh6F6dbjnHrejOSNvxv3NjPhtDOlcj+vbVHc7HJHTUgtdRDxv1iyIj4dJkyA42O1oCmzeskRe+GYtV7WqytAuDdwORyRf1EIXEc/KyoJHH3UGwfXv73Y0BbYiYT8PzFzKOTXK89x1LTA+OjJfSh610EXEs957D9avdxaR8fd3O5oCSTqQwZ0fxBMRGsjb/dsQHOBb8UvJpoQuIp6TluaMaO/QwecWkcnIymHgh4vZdzCLmYPaU6ms72/tKiWL17rcjTHVjTHzjTGrjDErjTH35h0fY4xJMMYszXtc7q0YRKSQvfIK7NjhbJHqQ13V1loe+XQ5f2zZx0u9W9IsupzbIYkUmDdb6NnAA9baJcaYssBiY8x3ea+9bK31/f0TReQfu3fD88/DVVfB+ee7HU2BTPxlI7OXJHD/pQ3o3ryK2+GInBGvJXRrbSKQmPf3AWPMaiDaW9cTEZe9+KKzxOvYsW5HUiC/rN/FM1+tpnuzyvz3knpuhyNyxgpllLsxphbQGvg979A9xphlxpj3jTHhhRGDiHjRzp3w2mvQrx80a+Z2NPm2ZfdB7pn2B/Ujy/Li9S01ol18mrHWevcCxpQBfgKestbONsZEAcmABZ4Eqlhrbz/BeQOAAQBRUVExsbGxHospNTWVMmXKeKw8UZ16mq/VZ93x46k2ezYLJ08mvXrRXITl33WakW0ZuyCdvZmW0e1DiAzVLN6C8LXvqC84uk47deq02FrbpkAFWGu99gACgG+AoSd5vRaw4nTlxMTEWE+aP3++R8sT1amn+VR9bt1qbVCQtbfd5nYkp3R0nebm5tq7Poq3tYfPtT+tTXIvKB/mU99RH3F0nQLxtoA515uj3A3wHrDaWvvSUcePHnFyDbDCWzGISCF46inIzYVRo9yOJN/ejPubL5fvYHj3RlzUoJLb4Yh4hDdHuV8A9AeWG2OW5h17BOhnjGmF0+W+CRjoxRhExJs2boSJE+HOO6FWLbejyZf5a5J48du19GxZlTsvrON2OCIe481R7v8DTjTC5EtvXVNECtkTTzirwT36qNuR5MvG5DT+G/sHjSuHaVlXKXY0CkREzszatfDBB3D33RBd9GekpmdbBnwQTyk/w9v9YwgJ1LKuUrxo6VcROTOjRkFICAwf7nYkp5Wba3l3WSYbknP58PZ2VI8IdTskEY9TC11ECu6PP2DGDLj/foiMdDua03pj/l8sScrh0csbc369im6HI+IVSugiUnAjR0J4ODzwgNuRnNbXK3bw0nfrOL9qKW67oJbb4Yh4jbrcRaRg/vc/+PJLePZZKF/e7WhOaXViCkNnLKVV9fLc2uiQBsFJsaYWuojkn7XwyCNQuTLcc4/b0ZxScmom/5kST1hwAO/0jyHQX8lcijcldBHJv2+/hV9+cbrcS5d2O5qTOpSdy10fLSY5NZN3bo4hMizY7ZBEvE5d7iKSP4db57VqOQvJFFHWWh77bAWLNu3l9X6taVGtaN8WEPGUfCV0Y0w5YAxwYd6hn4AnrLX7vRSXiBQ1s2fDkiUweTIEBrodzUlN+GkD0+O3ck+nelzZsqrb4YgUmvx2ub8PpAC98x4pwCRvBSUiRUxODjz2GDRqBDfe6HY0JzVnaQLPfb2GK1tWZWiXBm6HI1Ko8tvlXtdae91Rzx8/an12ESnupk6F1ath5kwoVTTv1P2+YTfDZi6jXe0IXry+BX5+GgQnJUt+W+jpxpgOh58YYy4A0r0TkogUKYcOwejRcM45cO21bkdzQn8lHeDOD+KpHhHCO/1jCCqlZV2l5MnvT+27gCl599IB9gK3eCckESlSJk6ETZvgrbfAr+hNjEk6kMGtkxYRWMqfybe1o3xo0b2/L+JN+U3oq4HngbpAeWA/cDWwzEtxiUhRcPAgjB0LF14I3bq5Hc1x9h08RP+JC9mTdojYAedpjXYp0fKb0OcA+4AlQIL3whGRImX8eEhMhOnToYitspaamc0t7y9k4+40Jt3aVtPTpMTLb0KvZq29zKuRiEjRsn+/s7zrZZc5LfQiJP1QDndMXsSK7Sm8fVMMF2jDFZF8D4r71RjT3KuRiEjRMm4c7NnjdLkXIYeyc7lr6mIWbtrDS71bcmmTKLdDEikS8ttC7wDcaozZCGQCBrDW2hZei0xE3LN9u5PQe/eGmBi3ozniUHYuQz5eQtzaXTxzbXOuahXtdkgiRUZ+E3p3r0YhIkXLqFGQlQXPPON2JEccys5l8LQlfLdqJ6OvbEK/djXcDkmkSMlXQrfWbvZ2ICJSRCxbBu+/D/ffD3XquB0NAJnZOdz90RJ+WJPEE1c15eb2tdwOSaTIKZpLPomIe4YNc/Y5f/RRtyMBICMrh7s+Wsz8tbt46ppm3HhuTbdDEimSlNBF5B/ffONskfrSSxAR4XY0HDyUzcAPF/PL+mSeuba5utlFTkEJXUQcOTnw4INQty4MHux2NOxPz+KOyYtYsmUvL/RqwfVtqrsdkkiRpoQuIo7Jk2HFCmcDFpe3R01OzeTm9xayPukAb9xwDpc3r+JqPCK+QAldRCA5GR55BNq3h+uuO/37vWj7vnRumvg72/enM/GWtlzcoJKr8Yj4CiV0EYF774W9e2HCBFeXeP17Vyo3v7eQlPQsPrzjXNrWcv8+voivUEIXKek+/xymTYMxY6CFe2tF/bl1H7dNXoSfgY8HnEez6HKnP0lEjlBCFynJ9u6FQYOcRD5ihGth/LJ+FwM/XEyFMoF8ePu51KpY2rVYRHyV1zY3NsZUN8bMN8asMsasNMbcm3c8whjznTFmfd4/w70Vg4icxv33Q1ISTJrk2kC4L/7czu2TF1EjIpRPBp2vZC5yhryW0IFs4AFrbRPgPGCwMaYJMBz4wVpbH/gh77mIFLavvoIpU2D4cDjnHFdCmPx/G/lv7B+0rh7O9IHtiQwLdiUOkeLAa13u1tpEIDHv7wPGmNVANHAV0DHvbVOAOOBhb8UhIiewfj385z/QtCk89lihXz431/Lc12t4++cNdG0SxWv9WhMc4F/ocYgUJ4VyD90YUwtoDfwOROUle4AdgPY+FClM//d/cNVVzt8ffQRBQYV6+UPZuQyb9Sdzlm6n/3k1GdOzKf5+7o2sFykujLXWuxcwpgzwE/CUtXa2MWaftbb8Ua/vtdYedx/dGDMAGAAQFRUVExsb67GYUlNTKVOmjMfKE9Wpp3mrPiv9+CONn32WjKgolj/7LOnRhbv9aHq25fU/Mli1O5de9QO4ok4AppCmyek76lmqT887uk47deq02FrbpkAFWGu99gACgG+AoUcdWwtUyfu7CrD2dOXExMRYT5o/f75HyxPVqad5vD5zc6199llrwdoOHaxNTvZs+fmwdU+a7frST7buiHl2VvzWQr++vqOepfr0vKPrFIi3Bcy53hzlboD3gNXW2peOeulz4Ja8v28B5ngrBhEBDh6Em292Br/17QvffQcVKhRqCH9u3cfV439l+/50Jt/WjutiqhXq9UVKAm/eQ78A6A8sN8YszTv2CPAsMMMYcwewGejtxRhESrYNG+Daa509zp94wtkS1c+bk1uO99XyRO6fsZSKZYL4+M5zqR9VtlCvL1JSeHOU+/+Ak90cu8Rb1xWRPF99BTfeCNbCvHnQvXuhXt5ay4SfNvD8N2toVb08797choplCncAnkhJopXiRIqDKVNgzhw4cABSU51/rloFzZvD7NnOlqiFKC0zm4dmLWPe8kR6tKjCi9e31LQ0ES9TQhfxdRMmwF13Qe3aULkylC0LVavClVc6c8xDQws1nM270xj44WLW7TzA8O6NGHhRnUIbyS5Skimhi/iyjz6Cu++GHj2clnhAgKvhxK1N4r8f/4Exhsm3teMibX0qUmiU0EV81aefwq23QqdOMHOmq8k8KyeXcd+uY8JPf9Ooclne6d+GGhUKt2dApKRTQhfxRd9+60xBa9vWuXce7N4a6Fv3HOS/sX/wx5Z99GtXnVE9mhISqPvlIoVNCV3E16xd60xFa9wYvvwSXFyta96yRIbPXgYW3rihNT1aVHUtFpGSTgldxJekp8P110NIiDMVLdyd3Yf3p2fx+Ocrmf1HAi2rl+eNfq2pHqEudhE3KaGL+JJ774Xly5055oW8DvthP6/bxUOzlrErNZP/XlKfIZ3rEeBfuIvViMjxlNBFfMW0afDuuzBiBFx2WaFfPi0zm2e+Ws1HC7ZQt1Jp3u5/Pi2rlz/9iSJSKJTQRXzBunUwcCB06OAs4VrIflyzk5GfriAxJYP/dKjNg90aaqEYkSJGCV2kqMvIgN69nX3LP/4YShXef7a7DmTy+BcrmbsskfqRZZg1qD0xNSMK7foikn9K6CJF3Zgx8OefMHcuVCucXcpycy0z4rfy9JerycjKZWiXBgy6uC6BpXSvXKSoUkIXKcoWLYIXXoA77oArriiUS67ansLIz5azZMs+2tWO4OlrmlMv0r2pcSKSP0roIkVVZibcdhtUqQLjxnn9cgcysnj5u/VM/nUj4aGBjLu+JdeeE6112EV8hBK6SFE1diysXOnMNy9XzmuXsdbyzcodjP58JUkHMrmhXQ2GdWtI+dBAr11TRDxPCV2kCCqzfj088wzcfDNcfrnXrpOwL53Rc1bw/eokGlcJY8JNMbSu4c5iNSJydpTQRYqaQ4do9NxzUKkSvPyyVy6Rk2uZ9H8beem7dVgLj17emNsuqEUpLRAj4rOU0EWKmuHDKfP33/DZZxDh+SliW/cc5IEZf7Jw0x46N4rkiauaUi1cy7aK+DoldJGi5O234eWX2XbNNVS76iqPFm2t5ZMlCYz5fCWABr2JFDNK6CJFxfffw+DB0L07fw8ejCdnnO87eIjhnyzn65U7aFcrgnG9W2ozFZFiRgldpChYswZ69XK2RI2NxS5Z4rGil27dx+CpS0g6kMGI7o34z4V18PdTq1ykuFFCF3FbcjL06OEs7Tp3LoSFeaRYay1Tft3EU1+uJrJsMLMGaTMVkeJMCV3ETenpcPXVsG0bxMVBzZoeKfZARhbDP1nOvOWJXNIoknG9W2peuUgxp4Qu4pacHLjpJvj1V5g+Hc47zyPFbkxO484P4tmYnMaI7o2488I6+KmLXaTYU0IXcYO1cP/9MHu2M9f8+us9UuxP63YxZNoS/P0MH97RjvPrVvRIuSJS9Cmhi7hh3Dh4/XUYOhTuu++si7PWMvGXjTzz1WoaRJXl3ZvbaBS7SAmjhC5S2GJjYdgw6NPH2UntLGVk5TBi9nI+/SOB7s0q8+L1LSkdpP+0RUoa/VcvUpiWLIFbb4WLLoIpU8Dv7JZaTdyfzsAPF7Ns237uv7QBQzrX0/1ykRLKaws3G2PeN8YkGWNWHHVsjDEmwRizNO/hvV0nRIqaPXvguusgMhI++cSZpnYW4jft4crX/4+/k1J5p38M915aX8lcpATzZgt9MvAG8MG/jr9srX3Ri9cVKXpyc6F/f0hIgF9+gYpnN1jt44VbGDVnBdHlQ5h257k0iCrroUBFxFd5LaFba382xtTyVvkiPuWpp+DLL+HNN+Hcc8+4mEPZuYz5YiXTft/ChfUr8ka/cygXGuDBQEXEVxlrrfcKdxL6XGtts7znY4BbgRQgHnjAWrv3JOcOAAYAREVFxcTGxnosrtTUVMqUKeOx8kR1eirhixbR4uGH2XnppawZMQLysRnKiepzX0YubyzN5K99uVxeO4BeDQLw08Yq+abvqGepPj3v6Drt1KnTYmttm4KcX9gJPQpIBizwJFDFWnv76cpp06aNjY+P91hccXFxdOzY0WPlier0pLZvhxYtoGpVWLAAQvM3lezf9blky17u+mgxKenZPN+rBVe2rOqlgIsvfUc9S/XpeUfXqTGmwAm9UEe5W2t3Hv7bGPMuMLcwry9SqKyF225zlnedNSvfyfzfPl64hdFzVhJVLojZd59P4yqeWetdRIqXQk3oxpgq1trEvKfXACtO9X4Rn/bmm/Dtt/DWW9CgQYFPz8zOYfSclcQu2sqF9SvyWt/WhJfWeuwicmJeS+jGmI+BjkBFY8w2YDTQ0RjTCqfLfRMw0FvXF3HVmjXO4jHdu8PAgn/N92Tk0vvtBfy5dR93d6zLA10bastTETklb45y73eCw+9563oiRUZWljNFLTQU3nsvX4Pgjvb7ht2M+TWdHA4x4aZzuKxZFS8FKiLFiVaKE/G0sWMhPt5ZPKZK/pPx4f3Lx85bTcVgw0eDLqBepOaXi0j+KKGLeNL//ufMOb/lFrj22nyflpGVw6OfruCTJdu4tHEk11ZNVTIXkQLx2tKvIiVOQgL06gW1a8Orr+b7tO370un99m98smQb915Sn3f6tyE0QPfLRaRg1EIX8YSMDKdFnpYGP/wA5crl67RFm/Zw10eLycjK5Z3+MXRtWtnLgYpIcaWELnK2rIW77oKFC+HTT6Fp03ydNu33LYz+fAXVwkOJHRCjLnYROStK6CJn6403YPJkGD0arr76tG8/lJ3LE3NX8tGCLVzcoBKv9WtNuRCtxy4iZ0cJXeRsxMXB/fdDz54watRp356cmsndU5ewcOMeBl5ch4e6NdL8chHxCCV0kTOVkAB9+kD9+vDhh+B36jGmKxL2M+CDeHanHeLVvq24qlV0IQUqIiWBErrImTh0CK6/Hg4edFrpYadeX/3zP7fz0Kw/CQ8NZNag82leLX+D5kRE8ksJXeRMPPAA/PYbzJgBjRuf9G05uZYXv13LW3F/07ZWOG/eGEOlskGFGKiIlBRK6CIFNXWqMxBu6FCnlX4S+9OzuDf2D+LW7uKGc2sw5sqmBJbS0g8i4h1K6CIFsWwZ3HknXHQRPPvsSd/2V1IqAz6IZ8ueg4y9uhk3nVezEIMUkZJICV0kv3bvdhaPKV8epk+HgBNPNfth9U7ui11KYCk/pt15Hu1qRxRyoCJSEimhi+THoUNw3XWwbRvMnw+Vj1/RzVrLhJ828Pw3a2haNYy3+7chunyIC8GKSEmkhC5yOtbCPffATz/BRx9B+/bHvSUjK4dHZi9n9h8J9GhRhRd6tSQk0N+FYEWkpFJCFzmdV1+Fd9+FRx6BG2887uWkAxkM/HAxf2zZx9AuDRjSuR6mgHugi4icLSV0kVP56itnito118CTTx738srt+7lzSjx7D2bx1o3n0L15/vc/FxHxJCV0kZNZsQL69oUWLU64EtwPq3cy5OM/KBcSwMxB7WkWrcViRMQ9SugiJ7JzJ/ToAaVLw+efO/88yqT/28iTc1fRpGoY793SlqiwYJcCFRFxKKGL/Ft6urPZyq5d8PPPUL36kZeyc3J5cu4qpvy2mS5Noni1bytCA/WfkYi4T/8nEjlabi7ccgssWgSzZ0NMzJGX0jKzGfLxH/y4Jok7L6zN8O6NtVOaiBQZSugiRxs5EmbOhBdfPGZv810HMrl98iJWbt/Pk1c3o79WfhORIkYJXeSwefPgmWdgwABnnfY8f+9K5dZJC0k+cIh3b27DJY2jXAxSROTElNBFwNkG9Z57oEkTeP11yJtHvnjzHu6YEo+/McQOOI+W1cu7HKiIyIkpoYsAPP00bNrkrAYXGAjA1ysSuTd2KVXLhzD5trbUrFD61GWIiLhICV1kzRp4/nm4+WZnFzWcaWlPzF1Fq+rlee+WtkSUDnQ5SBGRU1NCl5LNWhg82Jln/sIL5OZanvlqNe/+spGuTaJ4tW9rrckuIj5BCV1Kto8/hh9/hLfeIiO8Ag/G/sHcZYnc0r4mo65sqmlpIuIz/E7/ljNjjHnfGJNkjFlx1LEIY8x3xpj1ef8M99b1RU5r/35nNHvbtiT16U+fdxYwd1kiI7o3YkxPJXMR8S1eS+jAZOCyfx0bDvxgra0P/JD3XMQdTzwBSUn8+dRrXPnWb6zfeYAJN8Uw8OK62i1NRHyO1xK6tfZnYM+/Dl8FTMn7ewpwNSJu2LQJ3niDOQMfo/dPeynl58cnd53PZc0qux2ZiMgZMdZa7xVuTC1grrW2Wd7zfdba8nl/G2Dv4ecnOHcAMAAgKioqJjY21mNxpaamUqZMGY+VJ75Xp3WeeY7JuTV4P6YnDcP9GNw6mLDAotMq97X69AWqU89SfXre0XXaqVOnxdbaNgU537VBcdZaa4w56a8Ja+07wDsAbdq0sR07dvTYtePi4vBkeeJbdbrxl3jurnIZKyrX45b2NXn0iiYElvLm3aeC86X69BWqU89SfXre2dZpYSf0ncaYKtbaRGNMFSCpkK9fbOXmWnanHSItM5vQIH/KBJUiJMBf94L/ZfaSbTz2+VYCykfxzrWN6NqurtshiYh4RGEn9M+BW4Bn8/45p5Cv79OstWzbm87aHQdYsyOFNTsOsHVvOkkpGew6kEl27rEdHn4GyoUE0Cy6HK2rl6d1jXBaDo8o+wAAFdJJREFUVS9PeAlcJCU5NZPHv1jFF39up13CWl5pFUJVJXMRKUa8ltCNMR8DHYGKxphtwGicRD7DGHMHsBno7a3rFwd70g6xdOte/tiyj6VbnceBjOwjr1ePCKFWhdLUj6xIVFgQUWHBlA4sxcFD2aQdyiEtM5vk1Ez+3LqfN+b/xeF8H1MznB4tqnBF8ypEhgW79OkKh7WWmYu38dS81aQfymbo3z8yeNEn+L+/xu3QREQ8ymsJ/f/bu/PwqKt7j+Pvb0ggrIGwhCUoWwiEsi+lilQUUETB606tLWpFr9d9pfS2VVuhKG0p6oNFpOIGVUBFUZEKihsgiOwgOwQCGIIIIRJCzv3jDDX1siRkkl9m5vN6njzOTCa/3zeHY75zdufckBN86/yyumekyz1cwKItOXy8PptPNmSzdtcBwLe00xvW4uIOjflRk1q0aViL9IY1qVGl+P98uYcLWLFjP4s25/D2iiwefnM1j7y1mp7N6/JfnZswsEMjqpfgepFgc3YuI2as4LNNe+nerA6jEjNpNfIvMHkyJEb3BxkRiT3R9Rc8Au3Lzee91buYtWIXn23M5shRR+X4OLo3q8P9F6TT9cw6tG+SVOpkW71KPD1b1KVni7rccX4a63cf4M3lWby1bCcPTF/OI2+t5pKOjbmme1M6pCZF9Nh7Tm4+T8xdz4sLtpKYUIlRl7Xn6o4NiWv3C+jQAa69NugQRUTCTgk9AIfyC5i1PIuZy3by6ca9HC10NE2uyvVnN6d3Wn26NatDYkLZ7h+ellKTe/rV5O6+aSzeuo+pi7bz2tJMpizaRttGtbiqWyqXdmoSUePteflHmfTJZp7+YCO5+QVc3b0pd/drTYOaiTBhAmzcCG++CZW0N7uIRB8l9HK0csd+pizaxswvd3LgcAFn1q3GsN4tGNi+Ee0a1wqkVWxmdG+WTPdmyfx+UAZvfLmTVxdv5+E3VzPq7bX0y0jhiq6p9EqrR0KlirW065jcwwVMWbSNCfM3sefAYfq2TeHBC9NJS6np3/Ddd35XuJ49YeDAYIMVESkjSuhlrOBoIe+u2sUz8zexLHM/VeLjGNi+EVd3b0qP5skVqmu7VmIC1/U8k+t6nsmarG95dXEmry3NZNaKLGolxtMvoyEXtW9Ir7R6VIkPvpWbk5vPc59uYfKnW9ifd4QfN0/myZ91oUfz5P984/jxsGMHvPACVKDyFhEJJyX0MpKXf5RpS7bzzEeb2ZZziOb1qvPwoHZc2qkJSdUSgg7vlNo2qsXvLslg+IA2zP/qa95emcWc1buY/kUmNarEc3aruvy0dQN6t65Hap1q5RZXYaFj0ZYcpi3JZNbyLPKOHKV/Rgq3nNuSLmcc56yfAwdg5Ejo2xf69Cm3OEVEypsSepgdyi/ghc+2MmH+Jvbm5tOpaW1GXNSWfhkpEXl6V+X4OPpmpNA3I4X8gkI+3ZjN7FW7+HDd18xetRuAlvWrc2bVw3xbZyfdm9WhUVLVsMZQWOj4as8B3lmxixlLM9mek0eNKvEM7tSYG3s1/75r/XjGjoXsbHj00bDGJCJS0Sihh8l3R47y4oKtPP3hRrIP5nNOWj1uPy+N7s3qVKhu9dKoHB/HuekNODe9Ac45Nuw5yIdffc389dl8ujGXuVOWApBapyodU2vTskEN0hrUoFWDGjSvV71YE/2cc3xz6AiZ+/L4cvs+Ptu0lwWbcsjJzccMzm5Zj3v7pXNBu4ZUrXyK6+XkwJgxMHgw9OgRjiIQEamwlNBLqbDQMe2LTMbMXseeA4c5u1Vdnu7bmm7Nkk/9wxHMzEhLqUlaSk1+dU4L3p87jwatu/D5lhw+35LDyp37eWdlFkU3r6tRJZ66NSpTt3pl6lTzs+ePOsfRQv+158Bhdn6Tx6H8o//+mcZJiZybXp+zWtajV6t6NEwqwfrxxx7zXe5/+EO4fm0RkQpLCb0U1mR9y29fX8nirfvockZtxg3pTM8WdcNzcedg/XpYswbWroV162DzZt/avP32Crf0qlKc0T41ifapSdzQqzngey02Z+eyYc9BtuUcYu/BfLIPHmZv7mGy9n+Hmf+5ODMqxRkt61end1p9mtSpSpPaibRpWIsz61Y7vR6OnTth3DgYMgTatw/zbysiUvEooZ+Gg4cL+Oucr3ju0y3USoznscs7cEXXVOJKO0a+bx/861/w7rswe7afmX1Mw4ZQty7cfTdMmwaTJkHr1qW7XxlLTKhE20a1aNuoVvnf/OGHoaBArXMRiRlK6CX00fqvGT59BTv353FN9zN44IL00998xTlYvRreest/ffopFBZC7dp+Vnb//tCxI6SnQ1KSf/+LL8Idd/jXR470jytYaz1w69bBs8/CrbdCixZBRyMiUi6U0IvpwHdHGPn2WqYs2kaL+tWZdstZdD3zOMukiiMzE556CqZOhS1b/GudO8OIETBggJ/AFX+cfxozuO46n+xvvhnuuQeWLoXnnz/t3ysq/eY3ULUq/O//Bh2JiEi5UUIvho/XZ/Pg9OVk7c/j5t4tuLtf69PbmnXhQr+M6tVXfWt7wAD49a/hoosgNbX412nUCN54Ax54wM/ivvtu/4FAYMECmD7dd7k3aBB0NCIi5UYJ/SRyDxcw8u01vLQw1Cr/77OOv3nJyRQWwsyZ8Pjjvku9Vi246y647TZo1uz0gzPzLdGJE+F3v/N7lMc65+DBB30iv+eeoKMRESlXSugnsGDTXu6ftozMfXncdE5z7u2fXrJW+Xff+a1Gx4yBr77yyXvsWLjhBqh5ko1QSqJ2bbjvPt+1vHAh/PjH4blupHrnHZg/H558EmrUCDoaEZFyVTFP2wjQ/kNHeGjmKq6ZsIBKZrx680/4zcCM4ifzI0fg6aeheXMYNswnlqlT/RK0O+8MXzI/5o47oF4930qPZUeP+uGLli3hppuCjkZEpNyphR5yuOAoL3y2lSfmbuDb744w9KxmPHBhOtUqF7OInIPXX4fhw32LvFcvPyP9vPPK9kCQmjV9N/P998PHH/v7xqInnoDly+Gf/4TKkXPkq4hIuMR8Qi84WsisFVk8Pnsdmfvy6N26PsMvbENG4xKsnV64EO69Fz75BNq29RPWLrmk/E72uvVW+POffdf7vHmxd6LYhg1+hcDAgXDllUFHIyISiJhM6M45vtz+Da8v3cFby3eSfTCfjEa1eOHG9pyTVr/4F9qxw7fIX3zRb/wyYQJcf/3xl5yVpWrVfEK74w6YOxfOP7987x+kwkK48UbfKv/732Pvw4yISEjMJfQZX2Qy+qM8ds/+hMrxcZzfpgGXdm5Cv7Ypxd/pLS/PT3b705/82O2IEX78NsiJWDfd5Pcu/+1vy76bvyIZP95PhJs0CZo0CToaEZHAxFxCLzjqSE407h3QngvbN6RWYgnPJp81yy8527IFLr/cL0dr3rxMYi2RxESfzG++2cd48cVBR1T2Nm/28wcuuACGDg06GhGRQMXcLPerujflwR5Vuap705Il8+3b4bLLfKJMTIT33/d7qleEZH7M9ddDq1a+x6CwMOhoypZzvlciLg6eeSZ2eiRERE4g5hJ6ieXn+1Z427b+0JSRI2HZMt+tXdEkJMAf/wgrVsCUKUFHU7aeesp/qBozBpo2DToaEZHAKaGfiHN+97V27fwWq336wKpVfqy8Ii+LuvJK6NTJd7/n5wcdTdlYvNjvBHfxxVpzLiISooR+PKtW+XHZQYP8jPV33vHJvSJ1r59IXByMGuXHlydODDqa8Nu3z39oadQIJk9WV7uISIgSelF5eX78uWNH+Pxz+Nvf/GYlF14YdGQlc8EF0Ls3PPII5OYGHU34OOfnCWRm+g1kkpODjkhEpMJQQj/mgw98Ih81yh9Run69X9edUMJZ8BWBmf89du+GceOCjiZ8xo71m/Y89hj07Bl0NCIiFUogCd3MtpjZCjP70swWBxHDv33zjR+H7dPHrymfMwf+8Q+/P3okO+ssv1vd6NGQlRV0NKW3YIGfy3Dppf60OhER+Q9BttD7OOc6Oee6BRbBjBl+9vqkSX4v9BUroG/fwMIJu9GjoaDAzwU4dCjoaE7fwYNw7bX+zPhJkzRuLiJyHDHZ5V45O9uvKb/8cr9l66JFvhu3WrWgQwuvtm3h5ZdhyRI/jBCpa9Pvv99P8nv+eahTwvPoRURiRFAJ3QHvmdkSMxtWrnd+6SV6DB3qZ66PHu2Tedeu5RpCuRo0yB/cMmOGn/AXaWbP9sfR3nMPnHNO0NGIiFRY5pwr/5uaNXHO7TCzBsAc4Hbn3PwfvGcYMAwgJSWl69SpU8Ny7wbvv0+DN95g4wMPkJeaGpZrVnjOkTZ2LE1mzmTtffexa+DAsN/i4MGD1AjzXvbxBw7Q/YYbKKhenSUTJlBYkdf/h1lZlGesU5mGl8oz/IqWaZ8+fZaUeEjaORfoF/AQcN/J3tO1a1cXNoWFbt7cueG7XqTIz3euf3/n4uOdmzUr7JefN29e2K/prr3Wx7t4cfivXcGVSXnGOJVpeKk8w69omQKLXQnzabl3uZtZdTOreewx0B9YWY4BxOakqoQEeOUV6NDBzxR/9dWgIzq56dPhpZf8Ge/RPCQiIhImQYyhpwAfm9kyYBEwyzn3bgBxxJ6kJH9eeo8ecM01fnleRbR7N9xyi0/kkTjuLyISgHI/PtU5twnoWN73lZCkJD/R7LLL4IYb4Ntv4c47g47qe875ZH7ggJ/VHokb+4iIBCAml63FvOrVYeZMn9TvusvP9q8oXnwRXn8dHn0UMjKCjkZEJGIooceqKlX8fug/+xkMH+6PIw1aZibcfjucfbZ2gxMRKaFy73KXCiQ+Hp57zh/gctttvjv+5z8PJhbn4MYb4cgRH1OlSsHEISISodRCj3UJCTB1Kpx3Hgwd6g8/CcKECfDee/D449CqVTAxiIhEMCV0gcREP27drRtcdZU/oKY8LV4M997r99G/5ZbyvbeISJRQQhevZk14+21IT4f+/f1hKJs2lf1916zx583Xrw+TJ0OcqqSIyOnQX0/5XnIyfPSRnyT32mvQpo2fpLZ7d9ncb+tW6NfPj+XPmQONG5fNfUREYoASuvynpCQYNQo2bPDr1MeP92Pa48b58+LDZc8en8wPHvRj5xo3FxEpFSV0Ob7Gjf0pZ6tX+2Vkd94JZ50Fy5eX/trbtvlu9sxMmDXLb0crIiKlooQuJ9e6tT9q9uWX/ZnkXbr4LvlDh0p+rZwcf7Z569Z+7Hz6dP9hQURESk0JXU7NDIYMgbVr/dK20aP9Lm5vvnnynztyxI+/r17tf6ZFC382+5AhsG4dDBhQLuGLiMQCJXQpvuRkmDgRPvwQatSAQYNg8GDYupW4/Hx/8MuIEf7wlzp1oHJlaNgQ2rXzrfpevWDZMn8ozBlnBP3biIhEFe0UJyXXuzcsXQpjx8JDD0GbNpxdWAj5+X6Ht549/Y5z9etDvXr+Ky0NOncOOnIRkailhC6nJyHBj4dffTWMHMnOvXtpOnSoT/Y1awYdnYhIzFFCl9I54wx4+mk2fvABTc89N+hoRERilsbQRUREooASuoiISBRQQhcREYkCSugiIiJRQAldREQkCiihi4iIRAEldBERkSighC4iIhIFlNBFRESigBK6iIhIFFBCFxERiQJK6CIiIlFACV1ERCQKmHMu6BhOycy+BraG8ZL1gOwwXk9UpuGm8gw/lWl4qTzDr2iZnumcq1+SH46IhB5uZrbYOdct6Diiico0vFSe4acyDS+VZ/iVtkzV5S4iIhIFlNBFRESiQKwm9AlBBxCFVKbhpfIMP5VpeKk8w69UZRqTY+giIiLRJlZb6CIiIlEl5hK6mV1oZuvMbIOZDQ86nkhjZk3NbJ6ZrTazVWZ2Z+j1ZDObY2brQ/+tE3SskcbMKpnZUjN7K/S8uZktDNXVf5pZ5aBjjBRmVtvMppnZWjNbY2Y/UR0tHTO7O/T//Eozm2JmiaqjJWNmk8xsj5mtLPLaceuleeNCZbvczLqc6voxldDNrBLwFDAAyACGmFlGsFFFnALgXudcBtAT+J9QGQ4H3nfOpQHvh55LydwJrCnyfDTwV+dcK2AfcGMgUUWmvwHvOufaAB3x5ao6eprMrAlwB9DNOfcjoBJwDaqjJfUccOEPXjtRvRwApIW+hgHjT3XxmEroQA9gg3Nuk3MuH5gKDA44pojinMtyzn0RenwA/4eyCb4cJ4feNhm4NJgII5OZpQIDgYmh5wacB0wLvUVlWkxmlgT0Bp4FcM7lO+e+QXW0tOKBqmYWD1QDslAdLRHn3Hwg5wcvn6heDgaed94CoLaZNTrZ9WMtoTcBthd5nhl6TU6DmTUDOgMLgRTnXFboW7uAlIDCilRjgQeAwtDzusA3zrmC0HPV1eJrDnwN/CM0hDHRzKqjOnranHM7gDHANnwi3w8sQXU0HE5UL0ucr2ItoUuYmFkNYDpwl3Pu26Lfc37phJZPFJOZXQzscc4tCTqWKBEPdAHGO+c6A7n8oHtddbRkQuO6g/EflhoD1fn/XcdSSqWtl7GW0HcATYs8Tw29JiVgZgn4ZP6Sc25G6OXdx7qDQv/dE1R8EehsYJCZbcEPA52HHwOuHereBNXVksgEMp1zC0PPp+ETvOro6esLbHbOfe2cOwLMwNdb1dHSO1G9LHG+irWE/jmQFpqZWRk/qWNmwDFFlNDY7rPAGufcX4p8aybwy9DjXwJvlHdskco592vnXKpzrhm+Ts51zl0LzAOuCL1NZVpMzrldwHYzSw+9dD6wGtXR0tgG9DSzaqG/AcfKVHW09E5UL2cCvwjNdu8J7C/SNX9cMbexjJldhB+vrARMcs49GnBIEcXMegEfASv4frx3BH4c/RXgDPzJeFc55344+UNOwczOBe5zzl1sZi3wLfZkYCnwc+fc4SDjixRm1gk/wbAysAm4Ht+AUR09TWb2MHA1fqXLUuBX+DFd1dFiMrMpwLn4U9V2A78HXuc49TL0welJ/NDGIeB659zik14/1hK6iIhINIq1LncREZGopIQuIiISBZTQRUREooASuoiISBRQQhcREYkCSugiMcjMmhU98UlEIp8SuoiERZEdw0QkAEroIrGrkpk9Ezrj+j0zq2pmncxsQej85deKnM38gZl1Cz2uF9qmFjMbamYzzWwu/uhHEQmIErpI7EoDnnLOtQO+AS4HngcedM51wO8G+PtiXKcLcIVz7qdlFqmInJISukjs2uyc+zL0eAnQEqjtnPsw9Npk/LnipzJHW6iKBE8JXSR2Fd1z+yhQ+yTvLeD7vxeJP/hebjiDEpHTo4QuIsfsB/aZ2Tmh59cBx1rrW4CuocdXICIVjmalikhRvwSeNrNqfH9KGcAY4BUzGwbMCio4ETkxnbYmIiISBdTlLiIiEgWU0EVERKKAErqIiEgUUEIXERGJAkroIiIiUUAJXUREJAoooYuIiEQBJXQREZEo8H+kDQfcYEV69wAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "x2tnzs_tQBu1", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 170}, "executionInfo": {"status": "ok", "timestamp": 1598557979044, "user_tz": -60, "elapsed": 460558, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "44cffe55-b84f-4c18-e1a9-55a5e68c1915"}, "source": ["%%time\n", "train_model('o3',model_o3)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 o3 模型\n", "epoch:   0  train_loss:0.01786627 val_loss:0.07447874\n", "epoch:   1  train_loss:0.00711041 val_loss:0.09543302\n", "epoch:   2  train_loss:0.00298155 val_loss:0.09223332\n", "epoch:   3  train_loss:0.00224356 val_loss:0.08845887\n", "epoch:   4  train_loss:0.00202443 val_loss:0.08666097\n", "----------------------\n", "CPU times: user 5min 12s, sys: 2min 23s, total: 7min 35s\n", "Wall time: 7min 39s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "t8NwDrCSjHal", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598557981764, "user_tz": -60, "elapsed": 462255, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "fd7df319-bc74-4938-a902-bcd904eda809"}, "source": ["test_model('o3',model_o3)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  8.300758433239272\n", "mae:  2.3802632042808813\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "00r_S4UFuxPo", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598557985229, "user_tz": -60, "elapsed": 3445, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "ac8d3144-8419-43a6-a0c8-96649f62fc7b"}, "source": ["predict_future('o3',model_o3)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  1004.6188748231581\n", "mae:  29.607159102434622\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "4gC2bva0UT_m", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 170}, "executionInfo": {"status": "ok", "timestamp": 1598557301230, "user_tz": -60, "elapsed": 464855, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "27a1666e-c87f-4b68-d71d-362f1c7185cd"}, "source": ["%%time\n", "train_model('pm2.5',model_pm25)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 pm2.5 模型\n", "epoch:   0  train_loss:0.00967855 val_loss:0.02008323\n", "epoch:   1  train_loss:0.00191295 val_loss:0.02124865\n", "epoch:   2  train_loss:0.00165555 val_loss:0.02178136\n", "epoch:   3  train_loss:0.00162844 val_loss:0.02200780\n", "epoch:   4  train_loss:0.00163234 val_loss:0.02211982\n", "----------------------\n", "CPU times: user 5min 7s, sys: 2min 33s, total: 7min 40s\n", "Wall time: 7min 44s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "lf7IELqHYaEJ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598557303949, "user_tz": -60, "elapsed": 460591, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "95299ded-4d96-4dc9-facc-70f3811eb822"}, "source": ["test_model('pm2.5',model_pm25)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  11.545323349453927\n", "mae:  3.005253711069992\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "g1e0ezuEuzX0", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598557305838, "user_tz": -60, "elapsed": 461465, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "74586032-d46b-4724-a960-129b976831fa"}, "source": ["predict_future('pm2.5',model_pm25)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  47.92610676681903\n", "mae:  6.638429032577004\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}]}