{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "ARIMA.ipynb", "provenance": [{"file_id": "1gVa0nk0qu6p_eCfB-GZO0iuNyyDzS2SY", "timestamp": 1596641101049}, {"file_id": "1bE9BR1VLYohcN5Ko1Xfqnulh5XF0Unyu", "timestamp": 1596632496528}, {"file_id": "1IxQcZpOXeCS9CtXRmPjnFz7k5vwDTcqP", "timestamp": 1595516852382}], "collapsed_sections": [], "toc_visible": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}}, "cells": [{"cell_type": "code", "metadata": {"id": "ukOLd5z9o2NQ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 71}, "executionInfo": {"status": "ok", "timestamp": 1598707951312, "user_tz": -60, "elapsed": 662, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "015823a0-f8d1-4f38-94ea-7ffbb83746d3"}, "source": ["  !nvidia-smi"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["NVIDIA-SMI has failed because it couldn't communicate with the NVIDIA driver. Make sure that the latest NVIDIA driver is installed and running.\n", "\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "AIdpFAYUpDRR", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 71}, "executionInfo": {"status": "ok", "timestamp": 1598874182986, "user_tz": -60, "elapsed": 1444, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "515cd3d6-f09e-499a-f3d7-313fae88920c"}, "source": ["import pandas as pd\n", "from matplotlib import pyplot as plt\n", "import numpy as np\n", "from sklearn.metrics import mean_squared_error\n", "from sklearn.metrics import mean_absolute_error\n", "from statsmodels.tsa.arima_model import ARIMA"], "execution_count": 1, "outputs": [{"output_type": "stream", "text": ["/usr/local/lib/python3.6/dist-packages/statsmodels/tools/_testing.py:19: FutureWarning: pandas.util.testing is deprecated. Use the functions in the public API at pandas.testing instead.\n", "  import pandas.util.testing as tm\n"], "name": "stderr"}]}, {"cell_type": "markdown", "metadata": {"id": "GdwmDyu4pG2O", "colab_type": "text"}, "source": ["# 导入数据"]}, {"cell_type": "code", "metadata": {"id": "Iib6DHLKDN84", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 122}, "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": -60, "elapsed": 17017, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "e1e6e444-f082-42af-8667-2a3160f17465"}, "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"], "execution_count": 2, "outputs": [{"output_type": "stream", "text": ["Go to this URL in a browser: https://accounts.google.com/o/oauth2/auth?client_id=************-6bn6qk8qdgf4n4g3pfee6491hc0brc4i.apps.googleusercontent.com&redirect_uri=urn%3aietf%3awg%3aoauth%3a2.0%3aoob&scope=email%20https%3a%2f%2fwww.googleapis.com%2fauth%2fdocs.test%20https%3a%2f%2fwww.googleapis.com%2fauth%2fdrive%20https%3a%2f%2fwww.googleapis.com%2fauth%2fdrive.photos.readonly%20https%3a%2f%2fwww.googleapis.com%2fauth%2fpeopleapi.readonly&response_type=code\n", "\n", "Enter your authorization code:\n", "··········\n", "Mounted at /content/drive\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "rylZ0333POOT", "colab_type": "text"}, "source": ["这次我们只使用1个监测站的数据，即Bloomsbury，进行单边量自回归。"]}, {"cell_type": "code", "metadata": {"id": "E9MpBLKwpIlm", "colab_type": "code", "colab": {}, "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": -60, "elapsed": 1891, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}}, "source": ["Bloomsbury=pd.read_csv('/content/drive/My Drive/air_inference/data/Bloomsbury_clean.csv')"], "execution_count": 3, "outputs": []}, {"cell_type": "code", "metadata": {"id": "pQ3uen_qQCza", "colab_type": "code", "colab": {}, "executionInfo": {"status": "ok", "timestamp": 1598874200470, "user_tz": -60, "elapsed": 1885, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}}, "source": ["Bloomsbury=Bloomsbury[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']]"], "execution_count": 4, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "vAm8BF6Nu3eI", "colab_type": "text"}, "source": ["划分训练集,测试集"]}, {"cell_type": "code", "metadata": {"id": "S5Z9FaVUu7ji", "colab_type": "code", "colab": {}, "executionInfo": {"status": "ok", "timestamp": 1598874200471, "user_tz": -60, "elapsed": 1880, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}}, "source": ["def splitData(var,per_test):\n", "    num_test=int(len(var)*per_test)\n", "    train_size=int(len(var)-num_test)\n", "    train_data=var[0:train_size]\n", "    test_data=var[train_size:train_size+num_test]\n", "    return train_data,test_data"], "execution_count": 5, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "Rq4B1nL-Iysy", "colab_type": "text"}, "source": ["下面的字典用于存放不同污染物的index"]}, {"cell_type": "code", "metadata": {"id": "hZrKeA1o7Lr7", "colab_type": "code", "colab": {}, "executionInfo": {"status": "ok", "timestamp": 1598874200472, "user_tz": -60, "elapsed": 1470, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}}, "source": ["index_dic={\n", "    'nox':0,\n", "    'no2':1,\n", "    'no':2,\n", "    'o3':3,\n", "    'pm2.5':4    \n", "}"], "execution_count": 6, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "C-1_1zW9wdih", "colab_type": "text"}, "source": ["# 对单站点单变量进行预测"]}, {"cell_type": "code", "metadata": {"id": "blYd5mMpJV2m", "colab_type": "code", "colab": {}, "executionInfo": {"status": "ok", "timestamp": 1598874203021, "user_tz": -60, "elapsed": 537, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}}, "source": ["def predict_arima_sin(attr):\n", "  #数据集\n", "  dataset=Bloomsbury[attr]\n", "\n", "  #划分数据集\n", "  train_data,test_data=splitData(dataset,0.1)\n", "\n", "  model=ARIMA(train_data,order=(5,1,1)).fit()        \n", "  y_pred=model.forecast(len(test_data))  \n", "\n", "\n", "  print('mse:', mean_squared_error(test_data, y_pred[0]))\n", "  print('mae:', mean_absolute_error(test_data, y_pred[0]))\n", "\n", "  #预测未来\n", "  fut_pred=96\n", "  y_pred=model.forecast(fut_pred+1)\n", "  y_pred=pd.DataFrame(y_pred[0])\n", "  y_pred.to_csv(\"/content/drive/My Drive/air_inference/result24/arima_\"+attr+\".csv\",header = None, index = None)"], "execution_count": 7, "outputs": []}, {"cell_type": "code", "metadata": {"id": "UgmfsHzdRgky", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 85}, "executionInfo": {"status": "ok", "timestamp": 1598874218422, "user_tz": -60, "elapsed": 14153, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "aaf47385-43be-49bc-b630-69633429a167"}, "source": ["%%time\n", "predict_arima_sin('nox')"], "execution_count": 8, "outputs": [{"output_type": "stream", "text": ["mse: 1103.7526806258559\n", "mae: 25.32323559695387\n", "CPU times: user 15.2 s, sys: 9.73 s, total: 24.9 s\n", "Wall time: 13.3 s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "9GX5K4ljYbOz", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 85}, "executionInfo": {"status": "ok", "timestamp": 1598874230076, "user_tz": -60, "elapsed": 24964, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "7d12ddf1-ca69-4053-844e-8a3882c0c498"}, "source": ["%%time\n", "predict_arima_sin('no2')"], "execution_count": 9, "outputs": [{"output_type": "stream", "text": ["mse: 124.5136144287543\n", "mae: 9.009209045612717\n", "CPU times: user 14.1 s, sys: 8.64 s, total: 22.7 s\n", "Wall time: 11.8 s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "IJaEIBwsYc7N", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 85}, "executionInfo": {"status": "ok", "timestamp": 1598874242265, "user_tz": -60, "elapsed": 35746, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "53efaf1f-3c8a-423a-c38d-9356a786d61e"}, "source": ["%%time\n", "predict_arima_sin('no')"], "execution_count": 10, "outputs": [{"output_type": "stream", "text": ["mse: 224.10201116919112\n", "mae: 11.227594901804112\n", "CPU times: user 14.5 s, sys: 9.09 s, total: 23.5 s\n", "Wall time: 12.2 s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "BVwnI5I3YeNC", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 85}, "executionInfo": {"status": "ok", "timestamp": 1598874254335, "user_tz": -60, "elapsed": 46599, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "28116f46-6cb0-4526-ee08-fc1fee3f7cbe"}, "source": ["%%time\n", "predict_arima_sin('o3')"], "execution_count": 11, "outputs": [{"output_type": "stream", "text": ["mse: 153.10804438906428\n", "mae: 9.97472590039405\n", "CPU times: user 14.5 s, sys: 8.9 s, total: 23.4 s\n", "Wall time: 12.1 s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "C2uT5bvIYhsJ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 85}, "executionInfo": {"status": "ok", "timestamp": 1598874268005, "user_tz": -60, "elapsed": 59074, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "07879216182861586278"}}, "outputId": "16114b02-ae39-4c00-b2a1-3fbfad8e6ed0"}, "source": ["%%time\n", "predict_arima_sin('pm2.5')"], "execution_count": 12, "outputs": [{"output_type": "stream", "text": ["mse: 33.25185023551172\n", "mae: 4.264388160147856\n", "CPU times: user 16.1 s, sys: 10.2 s, total: 26.3 s\n", "Wall time: 13.6 s\n"], "name": "stdout"}]}]}