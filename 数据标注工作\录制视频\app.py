import streamlit as st
import cv2
import numpy as np
import tempfile
import os
import time
from PIL import Image
from model import EnvironmentViolationDetector

# 设置页面配置
st.set_page_config(
    page_title="环境违规行为检测系统",
    page_icon="🔍",
    layout="wide"
)

# 应用标题
st.title("环境违规行为检测系统")
st.markdown("实时识别工地扬尘、裸土未覆盖、土方作业未降尘、夜间违规施工等违规行为")

# 侧边栏配置
st.sidebar.header("配置参数")

# 模型选择
model_path = st.sidebar.selectbox(
    "选择模型",
    ["YOLOv8n (预训练)", "YOLOv8s (预训练)", "自定义模型"]
)

# 如果选择自定义模型，显示文件上传器
custom_model_path = None
if model_path == "自定义模型":
    uploaded_model = st.sidebar.file_uploader("上传模型文件", type=['pt'])
    if uploaded_model:
        # 保存上传的模型文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pt') as tmp_file:
            tmp_file.write(uploaded_model.getvalue())
            custom_model_path = tmp_file.name

# 获取实际模型路径
if model_path == "YOLOv8n (预训练)":
    actual_model_path = "yolov8n.pt"
elif model_path == "YOLOv8s (预训练)":
    actual_model_path = "yolov8s.pt"
else:
    actual_model_path = custom_model_path

# 置信度阈值
confidence = st.sidebar.slider("置信度阈值", 0.0, 1.0, 0.5, 0.05)

# 检测类别选择
st.sidebar.subheader("选择要检测的违规行为")
detect_bare_soil = st.sidebar.checkbox("裸土未覆盖", value=True)
detect_dust = st.sidebar.checkbox("工地扬尘", value=True)
detect_no_dust_reduction = st.sidebar.checkbox("土方作业未降尘", value=True)
detect_night_construction = st.sidebar.checkbox("夜间违规施工", value=True)
detect_outdoor_bbq = st.sidebar.checkbox("露天烧烤", value=True)
detect_garbage_burning = st.sidebar.checkbox("垃圾焚烧", value=True)
detect_uncovered_truck = st.sidebar.checkbox("渣土车未覆盖", value=True)

# 创建要检测的类别列表（只检测用户选择的类型）
selected_violation_types = []
if detect_bare_soil:
    selected_violation_types.append(0)  # 裸土未覆盖
if detect_dust:
    selected_violation_types.append(1)  # 工地扬尘
if detect_no_dust_reduction:
    selected_violation_types.append(2)  # 土方作业未降尘
if detect_night_construction:
    selected_violation_types.append(3)  # 夜间违规施工
if detect_outdoor_bbq:
    selected_violation_types.append(4)  # 露天烧烤
if detect_garbage_burning:
    selected_violation_types.append(5)  # 垃圾焚烧
if detect_uncovered_truck:
    selected_violation_types.append(6)  # 渣土车未覆盖

# 如果没有选择任何类型，则不进行检测
if not selected_violation_types:
    st.sidebar.warning("请至少选择一种违规类型进行检测")

# 初始化检测器
@st.cache_resource
def load_detector(model_path, confidence):
    return EnvironmentViolationDetector(model_path=model_path, confidence=confidence)

# 主界面
tab1, tab2, tab3 = st.tabs(["图片检测", "视频检测", "实时检测"])

with tab1:
    st.header("图片检测")
    uploaded_file = st.file_uploader("上传图片", type=['jpg', 'jpeg', 'png'])
    
    if uploaded_file is not None:
        # 转换上传的图片为OpenCV格式
        file_bytes = np.asarray(bytearray(uploaded_file.read()), dtype=np.uint8)
        image = cv2.imdecode(file_bytes, cv2.IMREAD_COLOR)
        
        # BGR转RGB用于显示
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 显示原图
        st.subheader("原始图片")
        st.image(image_rgb, caption="上传的图片", use_column_width=True)
        
        # 添加检测按钮
        if st.button("开始检测"):
            try:
                with st.spinner("正在检测中..."):
                    # 加载模型
                    detector = load_detector(actual_model_path, confidence)
                    
                    # 计时
                    start_time = time.time()
                    
                    # 进行检测，传入选择的违规类型
                    processed_image, detections = detector.detect(image, selected_violation_types)
                    
                    # 计算处理时间
                    process_time = time.time() - start_time
                    
                    # 转换为RGB用于Streamlit显示
                    processed_image_rgb = cv2.cvtColor(processed_image, cv2.COLOR_BGR2RGB)
                    
                    # 显示处理后的图片
                    st.subheader("检测结果")
                    st.image(processed_image_rgb, caption="检测结果", use_column_width=True)
                    
                    # 显示检测信息
                    st.subheader("检测详情")
                    st.write(f"处理时间: {process_time:.2f} 秒")
                    
                    if detections:
                        # 创建表格显示检测结果
                        results_df = {
                            "违规类型": [],
                            "置信度": [],
                            "位置": []
                        }
                        
                        for det in detections:
                            results_df["违规类型"].append(det["class"])
                            results_df["置信度"].append(f"{det['confidence']:.2f}")
                            results_df["位置"].append(f"[{det['bbox'][0]}, {det['bbox'][1]}, {det['bbox'][2]}, {det['bbox'][3]}]")
                        
                        st.dataframe(results_df)
                    else:
                        st.write("未检测到任何违规行为")
            
            except Exception as e:
                st.error(f"检测过程中出错: {str(e)}")

with tab2:
    st.header("视频检测")
    uploaded_video = st.file_uploader("上传视频", type=['mp4', 'avi', 'mov'])
    
    if uploaded_video is not None:
        # 保存上传的视频到临时文件
        temp_video_path = tempfile.NamedTemporaryFile(delete=False, suffix='.mp4').name
        with open(temp_video_path, "wb") as f:
            f.write(uploaded_video.getbuffer())
        
        # 显示原始视频
        st.subheader("原始视频")
        st.video(temp_video_path)
        
        # 添加检测按钮
        if st.button("开始视频检测", key="video_detect"):
            try:
                with st.spinner("正在处理视频..."):
                    # 加载模型
                    detector = load_detector(actual_model_path, confidence)
                    
                    # 打开视频
                    cap = cv2.VideoCapture(temp_video_path)
                    if not cap.isOpened():
                        st.error("无法打开视频文件")
                    else:
                        # 获取视频属性
                        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        fps = cap.get(cv2.CAP_PROP_FPS)
                        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                        
                        # 创建临时输出视频文件
                        output_video_path = tempfile.NamedTemporaryFile(delete=False, suffix='.mp4').name
                        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                        out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
                        
                        # 处理进度条
                        progress_bar = st.progress(0)
                        status_text = st.empty()
                        
                        # 统计检测到的违规行为
                        violation_counts = {}
                        
                        # 处理视频帧
                        frame_count = 0
                        
                        while True:
                            ret, frame = cap.read()
                            if not ret:
                                break
                                
                            # 每隔几帧进行一次检测以加快处理速度
                            if frame_count % 5 == 0:  # 每5帧处理一次
                                # 进行检测，传入选择的违规类型
                                processed_frame, detections = detector.detect(frame, selected_violation_types)
                                
                                # 统计违规行为
                                for det in detections:
                                    if det["class"] not in violation_counts:
                                        violation_counts[det["class"]] = 0
                                    violation_counts[det["class"]] += 1
                                
                                # 将检测结果写入输出视频
                                out.write(processed_frame)
                            else:
                                # 将原始帧写入输出视频
                                out.write(frame)
                            
                            # 更新进度
                            frame_count += 1
                            if frame_count % 10 == 0:
                                progress = min(frame_count / total_frames, 1.0)
                                progress_bar.progress(progress)
                                status_text.text(f"处理进度: {int(progress*100)}% ({frame_count}/{total_frames})")
                        
                        cap.release()
                        out.release()
                        
                        # 完成进度条
                        progress_bar.progress(1.0)
                        status_text.text("视频处理完成!")
                        
                        # 显示处理后的视频
                        st.subheader("检测结果视频")
                        st.video(output_video_path)
                        
                        # 显示统计信息
                        st.subheader("违规行为统计")
                        if violation_counts:
                            stats_data = {
                                "违规类型": list(violation_counts.keys()),
                                "检测次数": list(violation_counts.values())
                            }
                            st.dataframe(stats_data)
                            
                            # 绘制统计图表
                            st.bar_chart(violation_counts)
                        else:
                            st.write("未检测到任何违规行为")
                
                # 清理临时文件
                os.unlink(temp_video_path)
                os.unlink(output_video_path)
                
            except Exception as e:
                st.error(f"视频处理过程中出错: {str(e)}")

with tab3:
    st.header("实时摄像头检测")
    st.warning("注意: 实时检测需要访问您的摄像头")
    
    # 摄像头选择
    camera_id = st.selectbox("选择摄像头", ["0", "1", "2", "3"], index=0)
    
    if st.button("开始实时检测", key="camera_detect"):
        # 加载模型
        detector = load_detector(actual_model_path, confidence)
        
        # 打开摄像头
        cap = cv2.VideoCapture(int(camera_id))
        if not cap.isOpened():
            st.error(f"无法打开摄像头 {camera_id}")
        else:
            # 创建实时显示的占位符
            live_feed = st.empty()
            stats_display = st.empty()
            
            # 用于计算FPS
            frame_count = 0
            start_time = time.time()
            fps = 0
            
            # 用于存储检测结果的缓冲区
            detection_buffer = []
            
            # 退出按钮
            stop_button_pressed = st.button("停止检测")
            
            try:
                while not stop_button_pressed:
                    # 读取一帧
                    ret, frame = cap.read()
                    if not ret:
                        st.error("无法从摄像头读取画面")
                        break
                    
                    # 处理帧，传入选择的违规类型
                    processed_frame, detections = detector.detect(frame, selected_violation_types)
                    
                    # 计算FPS
                    frame_count += 1
                    elapsed_time = time.time() - start_time
                    if elapsed_time >= 1.0:
                        fps = frame_count / elapsed_time
                        frame_count = 0
                        start_time = time.time()
                    
                    # 显示FPS
                    cv2.putText(processed_frame, f"FPS: {fps:.2f}", (10, 30), 
                                cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                    
                    # 将检测结果添加到缓冲区
                    if detections:
                        detection_buffer.extend(detections)
                        # 保持缓冲区大小适中
                        if len(detection_buffer) > 50:
                            detection_buffer = detection_buffer[-50:]
                    
                    # 转换为RGB以供Streamlit显示
                    processed_frame_rgb = cv2.cvtColor(processed_frame, cv2.COLOR_BGR2RGB)
                    
                    # 更新显示
                    live_feed.image(processed_frame_rgb, caption="实时检测", use_column_width=True)
                    
                    # 统计和显示检测结果
                    if detection_buffer:
                        violation_counts = {}
                        for det in detection_buffer:
                            if det["class"] not in violation_counts:
                                violation_counts[det["class"]] = 0
                            violation_counts[det["class"]] += 1
                        
                        # 显示统计信息
                        stats_text = "检测到的违规行为:\n"
                        for vio_type, count in violation_counts.items():
                            stats_text += f"- {vio_type}: {count}次\n"
                        
                        stats_display.text(stats_text)
                    
                    # 检查是否点击了停止按钮
                    if stop_button_pressed:
                        break
                    
                    # 添加小延迟以减轻CPU负担
                    time.sleep(0.01)
            
            except Exception as e:
                st.error(f"实时检测过程中出错: {str(e)}")
            finally:
                # 释放资源
                cap.release()
                st.success("已停止实时检测")

# 页脚
st.markdown("---")
st.markdown("### 环境违规行为检测系统说明")
st.markdown("""
本系统使用基于YOLOv8的深度学习模型，可以检测以下环境违规行为:
- 裸土未覆盖：识别工地、建筑区域内未覆盖的裸土区域
- 工地扬尘：检测建筑工地产生的扬尘现象
- 土方作业未降尘：识别土方工程作业过程中未采取降尘措施的情况
- 夜间违规施工：检测夜间非法施工行为
- 露天烧烤：识别城市区域内的露天烧烤行为
- 垃圾焚烧：检测非法焚烧垃圾的行为
- 渣土车未覆盖：识别运输途中未覆盖的渣土车辆

系统提供三种检测模式:
1. 图片检测: 上传图片进行单次检测
2. 视频检测: 上传视频文件进行批量检测
3. 实时检测: 使用摄像头进行实时违规行为监控
""")

# 添加模型训练说明
with st.expander("模型训练说明"):
    st.markdown("""
    ### 如何训练自定义模型
    
    1. 准备数据集:
       - 收集包含目标环境违规行为的图像
       - 使用标注工具(如Labelimg, CVAT)标注图像中的违规行为
       - 按照YOLOv8格式组织数据集
    
    2. 创建数据集配置文件(data.yaml):
    ```yaml
    path: /path/to/dataset
    train: train/images
    val: val/images
    test: test/images
    
    nc: 7  # 类别数量
    names: ['裸土未覆盖', '工地扬尘', '土方作业未降尘', '夜间违规施工', '露天烧烤', '垃圾焚烧', '渣土车未覆盖']
    ```
    
    3. 执行训练:
    ```bash
    python train.py --data data.yaml --epochs 100 --batch-size 16 --img-size 640
    ```
    
    4. 上传训练好的模型:
       - 在侧边栏选择"自定义模型"
       - 上传训练好的.pt文件
    """)

# 系统状态信息
st.sidebar.markdown("---")
st.sidebar.subheader("系统信息")
st.sidebar.info(f"模型: {model_path}\n置信度阈值: {confidence}")
st.sidebar.text("© 2023 环境违规行为检测系统") 