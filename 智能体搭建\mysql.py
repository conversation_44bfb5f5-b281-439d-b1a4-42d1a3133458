import pymysql
from faker import Faker
from datetime import datetime, timedelta
import random

# ---------- 1. 连接 ----------
conn = pymysql.connect(
    host='*************',
    port=3306,
    user='root',
    passwd='lianwei123',
    db='lianwei_agent',
    charset='utf8mb4'
)
cur = conn.cursor()

fake = Faker('zh_CN')

# ---------- 2. 工具函数 ----------
def random_decimal(low, high, precision=2):
    return round(random.uniform(low, high), precision)

def gen_hourly(base_time):
    """返回一条小时级随机数据"""
    return {
        'plant_code': 'DEMO_PLANT',
        'unit_no': random.randint(1, 3),
        'data_time': base_time,
        'furnace_temp_avg': random_decimal(840, 880),
        'pm_hourly': random_decimal(5, 35),
        'nox_hourly': random_decimal(120, 300),
        'so2_hourly': random_decimal(10, 90),
        'hcl_hourly': random_decimal(5, 45),
        'co_hourly': random_decimal(5, 70),
        'o2_hourly': random_decimal(7, 12),
        'flow_hourly': random_decimal(60000, 80000),
        'status_mark': random.choice(['normal', 'startup', 'shutdown', 'fault', 'accident', 'warming', 'cooling', 'offline']),
        'activated_carbon': random_decimal(20, 60),
        'lime': random_decimal(200, 400),
        'ammonia': random_decimal(80, 200),
        'slag_hourly': random_decimal(0.8, 1.5),
        'flyash_hourly': random_decimal(0.2, 0.5),
        'leachate_hourly': random_decimal(0.5, 1.2),
        'is_valid': 1,
        'mark_reason': None
    }

def insert_many(sql, data_list):
    cur.executemany(sql, data_list)
    conn.commit()

# ---------- 3. 生成小时级 7*3*24 ----------
hour_rows = []
base = datetime(2024, 7, 10, 0)
for day in range(7):
    for hour in range(24):
        t = base + timedelta(days=day, hours=hour)
        row = gen_hourly(t)
        hour_rows.append(tuple(row.values()))

sql_hour = """
INSERT INTO incinerator_hourly
(plant_code, unit_no, data_time, furnace_temp_avg, pm_hourly, nox_hourly, so2_hourly,
 hcl_hourly, co_hourly, o2_hourly, flow_hourly, status_mark,
 activated_carbon, lime, ammonia, slag_hourly, flyash_hourly, leachate_hourly,
 is_valid, mark_reason)
VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)
"""
insert_many(sql_hour, hour_rows)

# ---------- 4. 生成日汇总 7*3 ----------
daily_rows = []
for day in range(7):
    date = (base + timedelta(days=day)).date()
    for u in (1, 2, 3):
        daily_rows.append((
            'DEMO_PLANT', u, date,
            random.randint(1200, 1440),            # run_time_min
            random_decimal(5, 35),                 # pm_daily
            random_decimal(120, 300),              # nox_daily
            random_decimal(10, 90),                # so2_daily
            random_decimal(5, 45),                 # hcl_daily
            random_decimal(5, 70),                 # co_daily
            random_decimal(95, 99),                # ≥850℃占比
            random.randint(0, 6),                  # <850℃次数
            random_decimal(400, 1000),             # 活性炭
            random_decimal(4000, 8000),            # 石灰
            random_decimal(1500, 4000),            # 氨水
            random_decimal(18, 30),                # 炉渣
            random_decimal(4, 10),                 # 飞灰
            random_decimal(10, 25),                # 渗滤液
            random.randint(0, 2),                  # startup_cnt
            random.randint(0, 2),                  # shutdown_cnt
            random.randint(0, 2),                  # fault_cnt
            random.randint(0, 1),                  # accident_cnt
            random.randint(0, 1),                  # warming_cnt
            random.randint(0, 1),                  # cooling_cnt
            random.randint(0, 1),                  # offline_cnt
            random.choice([0, 1]),                 # exceed_flag
            random.choice([0, 1])                  # temp_violation
        ))

sql_daily = """
INSERT INTO incinerator_daily
(plant_code, unit_no, data_date, run_time_min,
 pm_daily, nox_daily, so2_daily, hcl_daily, co_daily,
 temp_ge850_rate, temp_lt850_cnt,
 activated_carbon_daily, lime_daily, ammonia_daily,
 slag_daily, flyash_daily, leachate_daily,
 startup_cnt, shutdown_cnt, fault_cnt, accident_cnt,
 warming_cnt, cooling_cnt, offline_cnt,
 exceed_flag, temp_violation)
VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)
"""
insert_many(sql_daily, daily_rows)

# ---------- 5. 插入符合 GB 18485 的标准限值 ----------
limit_rows = []
# GB 18485-2014 表 4 及 5.4 条：24h 均值 / 小时均值（mg/m³，11%O₂，干基）
gb18485 = {
    'PM' :  (20, 30),
    'NOx': (250, 300),
    'SO2': (80, 100),
    'HCl': (50, 60),
    'CO' : (80, 100)
}
# 炉温 850 ℃ 法规要求（仅作记录，程序里用数字 850 判断）
# 如需存库，可扩展字段 furnace_temp_lim = 850

for unit in (1, 2, 3):
    for pol, (daily, hourly) in gb18485.items():
        limit_rows.append((
            'DEMO_PLANT',    # region / plant_code
            unit,            # unit_no
            pol,             # pollutant
            daily,           # 24 h 均值限值
            hourly           # 1 h 均值限值
        ))

sql_limit = """
INSERT INTO standard_limits(region, unit_no, pollutant, daily_lim, hourly_lim)
VALUES (%s,%s,%s,%s,%s)
"""
insert_many(sql_limit, limit_rows)

# ---------- 6. 清理 ----------
cur.close()
conn.close()
print('✅ 虚拟数据已插入完成！')