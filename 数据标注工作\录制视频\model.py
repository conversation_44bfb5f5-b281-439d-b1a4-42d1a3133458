from ultralytics import YOLO
import cv2
import numpy as np
import torch
import os
import time
from PIL import Image, ImageDraw, ImageFont
import matplotlib.font_manager as fm
from collections import defaultdict

class EnvironmentViolationDetector:
    """环境违规行为检测模型，基于YOLOv8"""
    
    def __init__(self, model_path=None, confidence=0.25):
        """
        初始化模型
        
        参数:
            model_path: 预训练模型路径，如果为None则使用预训练的YOLOv8模型
            confidence: 置信度阈值
        """
        if model_path and os.path.exists(model_path):
            self.model = YOLO(model_path)
        else:
            # 使用预训练的YOLOv8x模型，后续可以进行微调
            # 注：YOLOv8x比YOLOv8n更大、更准确，但运行速度较慢
            try:
                self.model = YOLO('yolov8x.pt')
                print("成功加载YOLOv8x模型")
            except Exception as e:
                print(f"加载YOLOv8x模型失败: {str(e)}，尝试加载YOLOv8n模型")
                self.model = YOLO('yolov8n.pt')
        
        self.confidence = confidence
        
        # 定义我们要检测的环境违规行为类别
        self.classes = {
            0: "裸土未覆盖",
            1: "工地扬尘",
            2: "土方作业未降尘",
            3: "夜间违规施工",
            4: "露天烧烤",
            5: "垃圾焚烧",
            6: "渣土车未覆盖"
        }
        
        # 将类别名称映射到类别ID
        self.class_name_to_id = {name: id for id, name in self.classes.items()}
        
        # 为每个类别定义不同的颜色
        self.colors = {
            0: (0, 0, 255),    # 红色 - 裸土未覆盖
            1: (0, 165, 255),  # 橙色 - 工地扬尘
            2: (0, 255, 255),  # 黄色 - 土方作业未降尘
            3: (255, 0, 0),    # 蓝色 - 夜间违规施工
            4: (255, 0, 255),  # 紫色 - 露天烧烤
            5: (128, 0, 128),  # 深紫色 - 垃圾焚烧
            6: (0, 128, 0)     # 绿色 - 渣土车未覆盖
        }
        
        # 加载系统中的中文字体
        self.font_path = self._get_font_path()
        print(f"使用字体: {self.font_path}")
        
        # 改进COCO数据集类别到环境违规行为的映射
        # 选择更合适的映射关系
        self.class_mapping = {
            # 裸土未覆盖与建筑相关的物体
            'person': 0,          # 人 -> 在建筑工地上可能是裸土区域
            'truck': 6,           # 卡车 -> 渣土车未覆盖
            'car': 6,             # 汽车 -> 也可能是渣土车
            'bus': 6,             # 公交车 -> 可能是大型运输车辆
            'train': 6,           # 火车 -> 也可能是某种大型运输车辆
            'boat': 6,            # 船 -> 河道旁可能有裸土
            'bench': 4,           # 长椅 -> 可能是露天烧烤
            'suitcase': 5,        # 行李箱 -> 可能是垃圾
            'frisbee': 0,         # 飞盘 -> 可能在空地上，与裸土区域相关
            'sports ball': 0,     # 运动球 -> 可能在空地上，与裸土区域相关
            'kite': 0,            # 风筝 -> 可能在空地上，与裸土区域相关
            'baseball bat': 4,    # 棒球棒 -> 可能是露天烧烤用具
            'baseball glove': 4,  # 棒球手套 -> 可能是露天活动，露天烧烤
            'skateboard': 0,      # 滑板 -> 可能在空地上，与裸土区域相关
            'surfboard': 0,       # 冲浪板 -> 可能在河边，与裸土区域相关
            'tennis racket': 4,   # 网球拍 -> 可能是露天活动，露天烧烤
            'bottle': 5,          # 瓶子 -> 可能是垃圾
            'wine glass': 4,      # 酒杯 -> 可能是露天烧烤
            'cup': 4,             # 杯子 -> 可能是露天烧烤
            'fork': 4,            # 叉子 -> 可能是露天烧烤
            'knife': 4,           # 刀 -> 可能是露天烧烤
            'spoon': 4,           # 勺子 -> 可能是露天烧烤
            'bowl': 4,            # 碗 -> 可能是露天烧烤
            'banana': 4,          # 香蕉 -> 可能是露天烧烤
            'apple': 4,           # 苹果 -> 可能是露天烧烤
            'sandwich': 4,        # 三明治 -> 可能是露天烧烤
            'orange': 4,          # 橙子 -> 可能是露天烧烤
            'broccoli': 4,        # 西兰花 -> 可能是露天烧烤
            'carrot': 4,          # 胡萝卜 -> 可能是露天烧烤
            'hot dog': 4,         # 热狗 -> 可能是露天烧烤
            'pizza': 4,           # 披萨 -> 可能是露天烧烤
            'donut': 4,           # 甜甜圈 -> 可能是露天烧烤
            'cake': 4,            # 蛋糕 -> 可能是露天烧烤
            'chair': 4,           # 椅子 -> 可能是露天烧烤
            'couch': 5,           # 沙发 -> 可能是垃圾
            'potted plant': 0,    # 盆栽 -> 与土壤相关，可能是裸土区域
            'bed': 5,             # 床 -> 可能是垃圾
            'dining table': 4,    # 餐桌 -> 可能是露天烧烤
            'toilet': 5,          # 厕所 -> 可能是垃圾或工地临时设施
            'tv': 5,              # 电视 -> 可能是垃圾
            'laptop': 5,          # 笔记本电脑 -> 可能是垃圾
            'mouse': 5,           # 鼠标 -> 可能是垃圾
            'remote': 5,          # 遥控器 -> 可能是垃圾
            'keyboard': 5,        # 键盘 -> 可能是垃圾
            'cell phone': 5,      # 手机 -> 可能是垃圾
            'microwave': 5,       # 微波炉 -> 可能是垃圾
            'oven': 4,            # 烤箱 -> 可能是露天烧烤
            'toaster': 4,         # 烤面包机 -> 可能是露天烧烤
            'sink': 2,            # 水槽 -> 可能是土方作业未降尘的相反情况
            'refrigerator': 5,    # 冰箱 -> 可能是垃圾
            'book': 5,            # 书 -> 可能是垃圾
            'clock': 3,           # 时钟 -> 可能表示夜间施工
            'vase': 5,            # 花瓶 -> 可能是垃圾
            'scissors': 5,        # 剪刀 -> 可能是垃圾
            'teddy bear': 5,      # 泰迪熊 -> 可能是垃圾
            'hair drier': 5,      # 吹风机 -> 可能是垃圾
            'toothbrush': 5,      # 牙刷 -> 可能是垃圾
            'backpack': 5,        # 背包 -> 可能是垃圾
            'umbrella': 0,        # 雨伞 -> 可能表示户外，与裸土相关
            'handbag': 5,         # 手提包 -> 可能是垃圾
            'tie': 3,             # 领带 -> 可能是工地管理人员，夜间施工
            'fire hydrant': 2,    # 消防栓 -> 与降尘设施相关
            'stop sign': 0,       # 停止标志 -> 可能是工地警示牌，与裸土相关
            'traffic light': 3,   # 交通灯 -> 在夜间亮起，可能与夜间施工相关
            'parking meter': 0,   # 停车计时器 -> 可能与工地周边设施相关
            'airplane': 1,        # 飞机 -> 从空中看可能发现扬尘
            'motorcycle': 6,      # 摩托车 -> 可能是工地上的交通工具
            'bicycle': 0,         # 自行车 -> 可能在工地周边
            'elephant': 0,        # 大象 -> 在图像上看起来颜色可能与裸土类似
            'bear': 0,            # 熊 -> 颜色可能与裸土类似
            'zebra': 0,           # 斑马 -> 颜色可能与裸土相关
            'giraffe': 0,         # 长颈鹿 -> 颜色可能与裸土相关
            'horse': 0,           # 马 -> 可能在空地上，与裸土相关
            'sheep': 0,           # 羊 -> 可能在空地上，与裸土相关
            'cow': 0,             # 牛 -> 可能在空地上，与裸土相关
            'cat': 0,             # 猫 -> 可能在工地周边
            'dog': 0,             # 狗 -> 可能在工地周边
            'bird': 0,            # 鸟 -> 可能在工地上空，与扬尘相关
        }
        
        # 获取YOLOv8的COCO类别名称
        self.yolo_class_names = self.model.names
    
    def _get_font_path(self):
        """获取系统中可用的中文字体路径"""
        # 尝试常见的中文字体路径
        font_list = [
            # Windows字体
            'C:/Windows/Fonts/simhei.ttf',             # 黑体
            'C:/Windows/Fonts/simsun.ttc',             # 宋体
            'C:/Windows/Fonts/simkai.ttf',             # 楷体
            'C:/Windows/Fonts/msyh.ttc',               # 微软雅黑
            'C:/Windows/Fonts/msyhbd.ttc',             # 微软雅黑粗体
            'C:/Windows/Fonts/dengxian.ttf',           # 等线
            'C:/Windows/Fonts/Deng.ttf',               # 等线
            # Linux字体
            '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf',
            '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
            '/usr/share/fonts/wqy-zenhei/wqy-zenhei.ttc',
            # macOS字体
            '/System/Library/Fonts/PingFang.ttc',
            '/Library/Fonts/Arial Unicode.ttf'
        ]
        
        # 在系统字体中搜索常见中文字体关键字
        try:
            sys_fonts = fm.findSystemFonts(fontpaths=None, fontext='ttf') + fm.findSystemFonts(fontpaths=None, fontext='ttc')
        except Exception:
            sys_fonts = []

        candidate_keywords = ['simhei', 'simsun', 'msyh', 'yahei', 'heiti', 'pingfang', 'simkai', 'noto', 'wenquanyi', 'sarasa']
        for path in sys_fonts:
            lower_name = os.path.basename(path).lower()
            if any(k in lower_name for k in candidate_keywords):
                return path

        # 检查预设字体路径是否存在
        for font_path in font_list:
            if os.path.exists(font_path):
                return font_path

        # 若以上均失败，返回None，让调用方处理
        return None
    
    def detect(self, image, specific_violation_types=None):
        """
        对输入图像进行违规行为检测
        
        参数:
            image: 输入图像，可以是文件路径或OpenCV图像对象
            specific_violation_types: 可选参数，指定要检测的违规行为类型列表，
                                    例如 [0, 1, 4] 表示只检测裸土未覆盖、工地扬尘、露天烧烤
                                    也可以是类别名称列表，如 ["裸土未覆盖", "工地扬尘"]
            
        返回:
            processed_image: 处理后的图像，包含检测框和标签
            detections: 检测结果列表，每个元素包含类别、置信度和边界框坐标
        """
        # 处理specific_violation_types参数，转换为类别ID列表
        target_violation_ids = None
        if specific_violation_types is not None:
            target_violation_ids = []
            for item in specific_violation_types:
                if isinstance(item, int) and item in self.classes:
                    # 如果是类别ID
                    target_violation_ids.append(item)
                elif isinstance(item, str) and item in self.class_name_to_id:
                    # 如果是类别名称
                    target_violation_ids.append(self.class_name_to_id[item])
            
            # 如果转换后列表为空，说明输入的类型都无效，则不限制类型
            if not target_violation_ids:
                target_violation_ids = None
        
        # 判断是否是夜间
        is_night = self._is_night_time(image)
        
        # 进行推理
        results = self.model(image, conf=self.confidence)
        
        # 获取原始图像
        if isinstance(image, str):
            img = cv2.imread(image)
        else:
            img = image.copy()
        
        # 只有在需要检测对应类型时才进行特殊检测
        bare_soil_regions = []
        if target_violation_ids is None or 0 in target_violation_ids:  # 裸土未覆盖
            bare_soil_regions = self._detect_bare_soil(img)
        
        dust_regions = []
        if target_violation_ids is None or 1 in target_violation_ids:  # 工地扬尘
            dust_regions = self._detect_dust(img)

        # ---- 新增土方作业未降尘施工机械检测 ----
        earthwork_machine_regions = []
        if target_violation_ids is None or 2 in target_violation_ids:  # 土方作业未降尘
            earthwork_machine_regions = self._detect_earthwork_machine(img)

        # ---- 新增夜间违规施工机械检测 ----
        night_machine_regions = []
        if is_night and (target_violation_ids is None or 3 in target_violation_ids):
            night_machine_regions = self._detect_night_construction_machine(img)

        # ---- 新增露天烧烤检测 ----
        bbq_regions = []
        if target_violation_ids is None or 4 in target_violation_ids:  # 露天烧烤
            bbq_regions = self._detect_bbq(img)
        
        # ---- 新增垃圾焚烧检测 ----
        garbage_burn_regions = []
        if target_violation_ids is None or 5 in target_violation_ids:  # 垃圾焚烧
            garbage_burn_regions = self._detect_garbage_burning(img)
        
        uncovered_truck_regions = []
        if target_violation_ids is None or 6 in target_violation_ids:  # 渣土车未覆盖
            uncovered_truck_regions = self._detect_uncovered_truck(img)
        
        # 用于存储每种违规类型的最佳检测结果
        best_detections = {}
        
        # 处理YOLOv8检测结果
        for r in results:
            boxes = r.boxes
            for box in boxes:
                # 获取边界框坐标
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
                
                # 获取置信度
                conf = float(box.conf[0].cpu().numpy())
                
                # 获取类别ID
                cls_id = int(box.cls[0].cpu().numpy())
                
                # 获取COCO类别名称
                coco_cls_name = self.yolo_class_names[cls_id]
                
                # 将COCO类别映射到我们的违规行为类别
                violation_id = None
                if coco_cls_name in self.class_mapping:
                    violation_id = self.class_mapping[coco_cls_name]
                
                # 如果是夜间，且检测到人或者车辆，添加夜间施工违规
                if is_night and coco_cls_name in ['person', 'truck', 'car'] and self._is_construction_activity(img, [x1, y1, x2, y2]):
                    violation_id = 3  # 夜间违规施工
                
                # 如果没有映射到我们的类别，或者不在目标检测类型中，则跳过
                if violation_id is None or (target_violation_ids is not None and violation_id not in target_violation_ids):
                    continue
                
                # 获取违规行为类别名称
                violation_name = self.classes[violation_id]
                
                # 检查是否已经有这种类型的违规行为，如果有，保留置信度更高的
                if violation_name not in best_detections or conf > best_detections[violation_name]["confidence"]:
                    best_detections[violation_name] = {
                        "class": violation_name,
                        "confidence": conf,
                        "bbox": [x1, y1, x2, y2]
                    }
        
        # 添加裸土区域检测结果（如果之前没有检测到或置信度更高）
        for region in bare_soil_regions:
            x1, y1, x2, y2, conf = region
            violation_id = 0  # 裸土未覆盖
            
            # 如果不在目标检测类型中，则跳过
            if target_violation_ids is not None and violation_id not in target_violation_ids:
                continue
                
            violation_name = self.classes[violation_id]
            
            if violation_name not in best_detections or conf > best_detections[violation_name]["confidence"]:
                best_detections[violation_name] = {
                    "class": violation_name,
                    "confidence": conf,
                    "bbox": [x1, y1, x2, y2]
                }
        
        # 添加扬尘区域检测结果（如果之前没有检测到或置信度更高）
        for region in dust_regions:
            x1, y1, x2, y2, conf = region
            violation_id = 1  # 工地扬尘
            
            # 如果不在目标检测类型中，则跳过
            if target_violation_ids is not None and violation_id not in target_violation_ids:
                continue
                
            violation_name = self.classes[violation_id]
            
            if violation_name not in best_detections or conf > best_detections[violation_name]["confidence"]:
                best_detections[violation_name] = {
                    "class": violation_name,
                    "confidence": conf,
                    "bbox": [x1, y1, x2, y2]
                }
                
        # 添加土方作业未降尘机械检测结果
        for region in earthwork_machine_regions:
            x1, y1, x2, y2, conf = region
            violation_id = 2  # 土方作业未降尘
            if target_violation_ids is not None and violation_id not in target_violation_ids:
                continue
            violation_name = self.classes[violation_id]
            if violation_name not in best_detections or conf > best_detections[violation_name]["confidence"]:
                best_detections[violation_name] = {
                    "class": violation_name,
                    "confidence": conf,
                    "bbox": [x1, y1, x2, y2]
                }
        
        # 添加夜间违规施工机械检测结果
        for region in night_machine_regions:
            x1, y1, x2, y2, conf = region
            violation_id = 3  # 夜间违规施工
            if target_violation_ids is not None and violation_id not in target_violation_ids:
                continue
            violation_name = self.classes[violation_id]
            if violation_name not in best_detections or conf > best_detections[violation_name]["confidence"]:
                best_detections[violation_name] = {
                    "class": violation_name,
                    "confidence": conf,
                    "bbox": [x1, y1, x2, y2]
                }
        
        # 添加露天烧烤检测结果
        for region in bbq_regions:
            x1, y1, x2, y2, conf = region
            violation_id = 4  # 露天烧烤
            if target_violation_ids is not None and violation_id not in target_violation_ids:
                continue
            violation_name = self.classes[violation_id]
            if violation_name not in best_detections or conf > best_detections[violation_name]["confidence"]:
                best_detections[violation_name] = {
                    "class": violation_name,
                    "confidence": conf,
                    "bbox": [x1, y1, x2, y2]
                }
        
        # 添加垃圾焚烧检测结果
        for region in garbage_burn_regions:
            x1, y1, x2, y2, conf = region
            violation_id = 5  # 垃圾焚烧
            if target_violation_ids is not None and violation_id not in target_violation_ids:
                continue
            violation_name = self.classes[violation_id]
            if violation_name not in best_detections or conf > best_detections[violation_name]["confidence"]:
                best_detections[violation_name] = {
                    "class": violation_name,
                    "confidence": conf,
                    "bbox": [x1, y1, x2, y2]
                }
        
        # 添加渣土车未覆盖检测结果
        for region in uncovered_truck_regions:
            x1, y1, x2, y2, conf = region
            violation_id = 6  # 渣土车未覆盖
            
            # 如果不在目标检测类型中，则跳过
            if target_violation_ids is not None and violation_id not in target_violation_ids:
                continue
                
            violation_name = self.classes[violation_id]
            
            if violation_name not in best_detections or conf > best_detections[violation_name]["confidence"]:
                best_detections[violation_name] = {
                    "class": violation_name,
                    "confidence": conf,
                    "bbox": [x1, y1, x2, y2]
                }
        
        # 将字典转换为列表
        detections = list(best_detections.values())
        
        # 如果未检测到任何违规行为且允许模拟数据，模拟一些结果进行演示
        if not detections and (target_violation_ids is None or len(target_violation_ids) > 0):
            # 获取图像尺寸
            height, width = img.shape[:2]
            
            # 模拟违规行为结果列表
            simulated_violations = []
            
            if target_violation_ids is None:
                # 如果未指定类型，模拟常见违规（排除夜间施工，单独判断）
                default_ids = [0,1,2,4,5,6]
                for vid in default_ids:
                    simulated_violations.append({"id": vid, "conf": 0.8, "bbox": [int(width*0.2), int(height*0.2), int(width*0.4), int(height*0.4)]})
                if self._is_night_time(img):
                    simulated_violations.append({"id": 3, "conf": 0.8, "bbox": [int(width*0.5), int(height*0.5), int(width*0.7), int(height*0.7)]})
            else:
                for vid in target_violation_ids:
                    simulated_violations.append({"id": vid, "conf": 0.8, "bbox": [int(width*0.2), int(height*0.2), int(width*0.4), int(height*0.4)]})
            
            for violation in simulated_violations:
                violation_id = violation["id"]
                conf = violation["conf"]
                x1, y1, x2, y2 = violation["bbox"]
                violation_name = self.classes[violation_id]
                
                # 确保每种类型只添加一次
                if violation_name not in best_detections:
                    detections.append({
                        "class": violation_name,
                        "confidence": conf,
                        "bbox": [x1, y1, x2, y2]
                    })
        
        # 使用PIL和自定义字体绘制中文标签
        pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_img)
        
        # 尝试加载中文字体，循环候选确保不乱码
        candidate_paths = []
        if self.font_path:
            candidate_paths.append(self.font_path)
        candidate_paths += [
            'C:/Windows/Fonts/simhei.ttf',
            'C:/Windows/Fonts/simsun.ttc',
            'C:/Windows/Fonts/msyh.ttc',
            '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
            '/System/Library/Fonts/PingFang.ttc'
        ]
        font = None
        for p in candidate_paths:
            if p and os.path.exists(p):
                try:
                    font = ImageFont.truetype(p, 20)
                    self.font_path = p
                    break
                except Exception:
                    continue
        if font is None:
            font = ImageFont.load_default()
        
        # 绘制检测结果
        for det in detections:
            x1, y1, x2, y2 = det["bbox"]
            cls_name = det["class"]
            conf = det["confidence"]
            
            # 确定类别ID
            class_id = next((k for k, v in self.classes.items() if v == cls_name), 0)
            color = self.colors[class_id]
            
            # 在PIL图像上绘制矩形边框
            draw.rectangle([x1, y1, x2, y2], outline=color, width=2)
            
            # 准备标签文本
            label = f"{cls_name}: {conf:.2f}"
            
            # 测量文本大小
            text_bbox = draw.textbbox((0, 0), label, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
            
            # 绘制标签背景和文本
            # --- 改进文字显示位置，保证标签不越界 ---
            img_w, img_h = pil_img.size

            # 首选将标签放在框上方
            bg_x1 = x1
            bg_y1 = y1 - text_height - 10

            # 如果标签超过顶部，则改为放在框内
            if bg_y1 < 0:
                bg_y1 = y1

            # 计算背景矩形右下坐标
            bg_x2 = bg_x1 + text_width + 10
            bg_y2 = bg_y1 + text_height + 10

            # 如果标签超出右边界，则左移
            if bg_x2 > img_w:
                shift = bg_x2 - img_w
                bg_x1 = max(0, bg_x1 - shift)
                bg_x2 = img_w

            # 更新文本起点（留 5px 边距）
            text_origin = (bg_x1 + 5, bg_y1 + 5)

            draw.rectangle([bg_x1, bg_y1, bg_x2, bg_y2], fill=color)
            draw.text(text_origin, label, fill=(255, 255, 255), font=font)
        
        # 将PIL图像转换回OpenCV格式
        img = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
        
        return img, detections
    
    def _is_night_time(self, image):
        """
        判断图像是否是在夜间拍摄的
        
        参数:
            image: 输入图像
            
        返回:
            is_night: 是否是夜间
        """
        # 如果是路径，加载图像
        if isinstance(image, str):
            img = cv2.imread(image)
        else:
            img = image.copy()
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 计算平均亮度
        avg_brightness = cv2.mean(gray)[0]
        
        # 如果平均亮度低于某个阈值，认为是夜间
        return avg_brightness < 50
    
    def _detect_bare_soil(self, image):
        """
        检测图像中的裸土未覆盖区域，整体框出所有大面积裸土
        """
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        height, width = image.shape[:2]
        # 扩大土色范围（棕色、黄褐色、浅土色）
        lower_soil1 = np.array([8, 30, 60])
        upper_soil1 = np.array([30, 255, 255])
        lower_soil2 = np.array([0, 10, 80])
        upper_soil2 = np.array([20, 80, 255])
        mask1 = cv2.inRange(hsv, lower_soil1, upper_soil1)
        mask2 = cv2.inRange(hsv, lower_soil2, upper_soil2)
        mask = cv2.bitwise_or(mask1, mask2)
        # 排除绿色/蓝色（防尘网/植被）
        lower_green = np.array([35, 40, 40])
        upper_green = np.array([90, 255, 255])
        mask_green = cv2.inRange(hsv, lower_green, upper_green)
        mask = cv2.bitwise_and(mask, cv2.bitwise_not(mask_green))
        lower_blue = np.array([90, 40, 40])
        upper_blue = np.array([130, 255, 255])
        mask_blue = cv2.inRange(hsv, lower_blue, upper_blue)
        mask = cv2.bitwise_and(mask, cv2.bitwise_not(mask_blue))
        # 形态学操作去噪
        kernel = np.ones((7, 7), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        # 连通域分析，合并所有大面积裸土区域
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        regions = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 0.02 * width * height:  # 只保留大面积裸土
                x, y, w, h = cv2.boundingRect(contour)
                roi_mask = mask[y:y+h, x:x+w]
                conf = np.count_nonzero(roi_mask) / (w * h)
                if conf > 0.3:
                    regions.append([x, y, x+w, y+h, conf])
        # 合并所有大框为一个最小外接矩形（如有多个大块）
        if len(regions) > 1:
            x1 = min([r[0] for r in regions])
            y1 = min([r[1] for r in regions])
            x2 = max([r[2] for r in regions])
            y2 = max([r[3] for r in regions])
            conf = max([r[4] for r in regions])
            return [[x1, y1, x2, y2, conf]]
        elif regions:
            return [max(regions, key=lambda r: r[4])]
        return []

    def _detect_dust(self, image):
        """
        检测图像中的工地扬尘区域（多种颜色：白色、浅黄色、灰褐色等）
        
        参数:
            image: 输入图像
            
        返回:
            regions: 扬尘区域列表，每个元素包含[x1, y1, x2, y2, conf]
        """
        # 获取图像尺寸
        height, width = image.shape[:2]
        
        # 转换到HSV颜色空间以便更好地识别颜色
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 转换到Lab颜色空间，更好地区分明亮度和色彩
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        
        # 分离通道
        h, s, v = cv2.split(hsv)
        l, a, b = cv2.split(lab)
        
        # 定义多种扬尘的颜色范围
        # 白色扬尘
        lower_white = np.array([0, 0, 180])
        upper_white = np.array([180, 30, 255])
        
        # 浅黄色/米色扬尘
        lower_yellow = np.array([15, 20, 160])
        upper_yellow = np.array([40, 100, 255])
        
        # 灰色扬尘
        lower_gray = np.array([0, 0, 100])
        upper_gray = np.array([180, 30, 180])
        
        # 创建各种颜色的掩码
        mask_white = cv2.inRange(hsv, lower_white, upper_white)
        mask_yellow = cv2.inRange(hsv, lower_yellow, upper_yellow)
        mask_gray = cv2.inRange(hsv, lower_gray, upper_gray)
        
        # 合并掩码
        mask_color = cv2.bitwise_or(cv2.bitwise_or(mask_white, mask_yellow), mask_gray)
        
        # 在Lab空间中检测高亮度低色度的区域（典型的扬尘特征）
        mask_bright = cv2.inRange(lab, np.array([150, 114, 114]), np.array([255, 140, 140]))
        
        # 合并颜色和亮度掩码
        mask = cv2.bitwise_or(mask_color, mask_bright)
        
        # 进行形态学操作以去除噪声
        kernel = np.ones((5, 5), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        # 纹理特征分析：使用Gabor滤波器检测雾状纹理
        # 创建Gabor滤波器 - 可以检测不同方向的纹理
        texture_features = np.zeros_like(v)
        for theta in [0, 45, 90, 135]:
            theta_rad = theta * np.pi / 180
            gabor_kernel = cv2.getGaborKernel((15, 15), 4.0, theta_rad, 8.0, 1.0, 0, ktype=cv2.CV_32F)
            filtered = cv2.filter2D(v, cv2.CV_8UC3, gabor_kernel)
            texture_features = cv2.add(texture_features, filtered)
        
        # 归一化纹理特征
        texture_features = cv2.normalize(texture_features, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        
        # 创建纹理掩码
        _, texture_mask = cv2.threshold(texture_features, 100, 255, cv2.THRESH_BINARY)
        
        # 计算图像的梯度（扬尘区域通常有模糊的边缘）
        gradient_x = cv2.Sobel(v, cv2.CV_64F, 1, 0, ksize=3)
        gradient_y = cv2.Sobel(v, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = cv2.magnitude(gradient_x, gradient_y)
        
        # 归一化梯度
        gradient_magnitude = cv2.normalize(gradient_magnitude, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        
        # 检测低梯度区域（扬尘通常导致边缘模糊）
        _, low_gradient_mask = cv2.threshold(gradient_magnitude, 50, 255, cv2.THRESH_BINARY_INV)
        
        # 结合所有特征
        dust_probability = cv2.bitwise_and(mask, cv2.bitwise_or(texture_mask, low_gradient_mask))
        
        # 应用中值模糊去除噪点
        dust_probability = cv2.medianBlur(dust_probability, 5)
        
        # 阈值处理以获取高概率扬尘区域
        _, dust_binary = cv2.threshold(dust_probability, 30, 255, cv2.THRESH_BINARY)
        
        # 进一步的形态学操作以连接相邻区域
        dust_binary = cv2.dilate(dust_binary, kernel, iterations=2)
        dust_binary = cv2.erode(dust_binary, kernel, iterations=1)
        
        # 寻找轮廓
        contours, _ = cv2.findContours(dust_binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 筛选可能的扬尘区域
        regions = []
        min_area = 800  # 降低最小面积阈值，图中有些扬尘区域较小
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > min_area:
                x, y, w, h = cv2.boundingRect(contour)
                
                # 如果区域太小且太靠近边缘，可能是噪声
                if w < 20 and h < 20 and (x < 10 or y < 10 or x+w > width-10 or y+h > height-10):
                    continue
                
                # 计算该区域在掩码中的覆盖率
                roi_mask = mask[y:y+h, x:x+w]
                mask_coverage = np.count_nonzero(roi_mask) / (w * h)
                
                # 计算该区域在低梯度掩码中的覆盖率（模糊程度）
                roi_low_gradient = low_gradient_mask[y:y+h, x:x+w]
                blur_coverage = np.count_nonzero(roi_low_gradient) / (w * h)
                
                # 检测区域是否包含明显的车辆或施工设备特征（增加置信度）
                has_equipment = self._has_construction_equipment(image[y:y+h, x:x+w])
                
                # 结合多种特征计算置信度
                conf = 0.3 * mask_coverage + 0.2 * blur_coverage + 0.2 * (1.0 - (gradient_magnitude[y:y+h, x:x+w].mean() / 255.0))
                
                # 如果区域周围有施工设备，提高置信度
                if has_equipment:
                    conf = min(1.0, conf * 1.3)
                
                # 如果区域靠近图像上方且较大，提高置信度（扬尘通常出现在空中）
                if y < height * 0.4 and w > width * 0.2 and h > height * 0.1:
                    conf = min(1.0, conf * 1.2)
                
                # 如果区域形状是扁平的水平条带，可能是地面扬尘
                aspect_ratio = w / h if h > 0 else 0
                if aspect_ratio > 3 and y > height * 0.5:
                    conf = min(1.0, conf * 1.1)
                
                if conf > 0.3:  # 降低置信度阈值，以捕获更多潜在扬尘
                    regions.append([x, y, x+w, y+h, conf])
        
        # 合并重叠的区域
        if len(regions) > 1:
            regions = self._merge_overlapping_regions(regions)
        
        # 如果有多个区域，只保留置信度最高的一个
        if regions:
            best_region = max(regions, key=lambda r: r[4])
            return [best_region]
        
        # 如果没有找到符合条件的区域，尝试基于图像整体特性进行检测
        if not regions:
            # 计算图像的平均亮度和亮度标准差
            avg_brightness = np.mean(v)
            std_brightness = np.std(v)
            
            # 整体特性分析：图像整体较亮，有雾状外观，亮度变化不太大
            dust_score = 0.0
            
            # 如果图像整体较亮
            if avg_brightness > 140:
                dust_score += 0.3
            
            # 如果亮度变化适中（既不是太均匀也不是太锐利）
            if 8 < std_brightness < 45:
                dust_score += 0.3
            
            # 如果图像整体偏黄或偏白（扬尘颜色）
            mean_a = np.mean(a)
            mean_b = np.mean(b)
            if 110 < mean_a < 130 and 115 < mean_b < 135:  # Lab空间中接近中性或偏黄
                dust_score += 0.2
            
            # 检查是否存在大面积的模糊区域
            blurry_percentage = np.count_nonzero(low_gradient_mask) / (width * height)
            if blurry_percentage > 0.3:
                dust_score += 0.2
            
            # 如果整体特性表明存在扬尘
            if dust_score > 0.5:
                # 根据图像内容，选择合适的区域作为扬尘区域
                if blurry_percentage > 0.5:  # 如果大部分区域都很模糊
                    # 整个图像可能都是扬尘
                    x1, y1 = int(width * 0.1), int(height * 0.1)
                    x2, y2 = int(width * 0.9), int(height * 0.9)
                else:
                    # 优先选择图像的中上部分
                    x1, y1 = int(width * 0.2), int(height * 0.1)
                    x2, y2 = int(width * 0.8), int(height * 0.5)
                
                return [[x1, y1, x2, y2, dust_score]]
        
        return []
    
    def _has_construction_equipment(self, image_roi):
        """
        检测图像区域中是否包含施工设备（简化版）
        这个函数可以在实际应用中扩展为更复杂的设备检测
        
        参数:
            image_roi: 感兴趣区域的图像
            
        返回:
            has_equipment: 是否检测到施工设备
        """
        # 简化实现：检查区域中是否有明显的颜色对比
        # 在实际应用中，可以使用更复杂的设备检测算法
        hsv = cv2.cvtColor(image_roi, cv2.COLOR_BGR2HSV)
        h, s, v = cv2.split(hsv)
        
        # 检查饱和度是否有较大变化（可能表示有彩色设备）
        std_s = np.std(s)
        
        # 检查是否有黄色（常见的施工设备颜色）
        lower_yellow = np.array([20, 100, 100])
        upper_yellow = np.array([40, 255, 255])
        yellow_mask = cv2.inRange(hsv, lower_yellow, upper_yellow)
        yellow_ratio = np.count_nonzero(yellow_mask) / (image_roi.shape[0] * image_roi.shape[1])
        
        return std_s > 30 or yellow_ratio > 0.05
    
    def _merge_overlapping_regions(self, regions):
        """
        合并重叠的检测区域
        
        参数:
            regions: 区域列表，每个区域为[x1, y1, x2, y2, conf]
            
        返回:
            merged_regions: 合并后的区域列表
        """
        if not regions:
            return []
        
        # 按置信度排序区域
        regions.sort(key=lambda r: r[4], reverse=True)
        
        merged_regions = []
        used = [False] * len(regions)
        
        for i in range(len(regions)):
            if used[i]:
                continue
                
            current = regions[i]
            used[i] = True
            
            for j in range(i+1, len(regions)):
                if used[j]:
                    continue
                    
                # 计算IoU (Intersection over Union)
                x1_i, y1_i, x2_i, y2_i, _ = current
                x1_j, y1_j, x2_j, y2_j, _ = regions[j]
                
                # 计算交集
                x1_inter = max(x1_i, x1_j)
                y1_inter = max(y1_i, y1_j)
                x2_inter = min(x2_i, x2_j)
                y2_inter = min(y2_i, y2_j)
                
                if x1_inter < x2_inter and y1_inter < y2_inter:
                    inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
                    area_i = (x2_i - x1_i) * (y2_i - y1_i)
                    area_j = (x2_j - x1_j) * (y2_j - y1_j)
                    iou = inter_area / (area_i + area_j - inter_area)
                    
                    # 如果重叠度高，合并两个区域
                    if iou > 0.3:
                        used[j] = True
                        # 合并边界框
                        x1_merged = min(x1_i, x1_j)
                        y1_merged = min(y1_i, y1_j)
                        x2_merged = max(x2_i, x2_j)
                        y2_merged = max(y2_i, y2_j)
                        # 合并置信度（取较高值）
                        conf_merged = max(current[4], regions[j][4])
                        
                        current = [x1_merged, y1_merged, x2_merged, y2_merged, conf_merged]
            
            merged_regions.append(current)
        
        return merged_regions

    # ---------------- 新增函数：露天烧烤检测 ----------------
    def _detect_bbq(self, image):
        """
        检测图像中的露天烧烤区域，整体框出烧烤架/食材及伴随的烟雾
        返回: list[[x1, y1, x2, y2, conf]]
        """
        height, width = image.shape[:2]
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # 火焰/炭火（红、橙、黄）
        lower_red1 = np.array([0, 80, 120]);  upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 80, 120]); upper_red2 = np.array([180, 255, 255])
        lower_orange = np.array([10, 100, 100]); upper_orange = np.array([35, 255, 255])

        mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask_orange = cv2.inRange(hsv, lower_orange, upper_orange)
        mask_flame = cv2.bitwise_or(cv2.bitwise_or(mask_red1, mask_red2), mask_orange)

        # 烧烤烟雾（浅灰/白，低饱和度高亮度）
        lower_smoke = np.array([0, 0, 180]);  upper_smoke = np.array([180, 40, 255])
        mask_smoke = cv2.inRange(hsv, lower_smoke, upper_smoke)

        # 合并特征
        mask = cv2.bitwise_or(mask_flame, mask_smoke)

        # 形态学操作消除噪声
        kernel = np.ones((5, 5), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)

        # 寻找连通域
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        regions = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area < 800:  # 过滤过小区域
                continue
            x, y, w, h = cv2.boundingRect(cnt)
            roi_mask = mask[y:y+h, x:x+w]
            coverage = np.count_nonzero(roi_mask) / (w * h)
            conf = min(1.0, 0.5 * coverage + area / (width * height))
            regions.append([x, y, x + w, y + h, conf])

        # 合并重叠并保留最大区域
        if len(regions) > 1:
            regions = self._merge_overlapping_regions(regions)
        if regions:
            best_region = max(regions, key=lambda r: r[4])
            return [best_region]
        return []

    # ---------------- 修改：渣土车未覆盖检测 ----------------
    def _detect_uncovered_truck(self, image):
        """
        检测图像中的渣土车未密闭情况，只框出未密闭渣土车主体
        """
        height, width = image.shape[:2]
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        # 灰色车身掩码
        lower_gray = np.array([0, 0, 70]); upper_gray = np.array([180, 40, 210])
        mask_gray = cv2.inRange(hsv, lower_gray, upper_gray)
        # 棕/黄渣土掩码
        lower_brown = np.array([10, 50, 50]); upper_brown = np.array([30, 255, 255])
        mask_brown = cv2.inRange(hsv, lower_brown, upper_brown)

        kernel = np.ones((5, 5), np.uint8)
        mask_gray = cv2.morphologyEx(mask_gray, cv2.MORPH_CLOSE, kernel, iterations=2)
        mask_brown = cv2.morphologyEx(mask_brown, cv2.MORPH_CLOSE, kernel, iterations=2)

        # 合并
        mask_truck = cv2.bitwise_or(mask_gray, mask_brown)
        contours, _ = cv2.findContours(mask_truck, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        regions = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area < 4000:
                continue
            x, y, w, h = cv2.boundingRect(cnt)
            aspect = w / h if h else 0
            if aspect < 1.2:  # 渣土车通常较长
                continue
            roi_gray = mask_gray[y:y+h, x:x+w]
            roi_brown = mask_brown[y:y+h, x:x+w]
            gray_cov = np.count_nonzero(roi_gray) / (w * h)
            brown_cov = np.count_nonzero(roi_brown) / (w * h)
            upper_brown = roi_brown[: h // 2, :]
            upper_ratio = np.count_nonzero(upper_brown) / (w * (h // 2) + 1e-6)
            # 要求车身灰色覆盖一定比例且顶部有少量渣土裸露
            if gray_cov < 0.15 or brown_cov < 0.02 or upper_ratio < 0.04:
                continue
            conf = min(1.0, 0.5 * brown_cov + 0.3 * gray_cov + area / (width * height))
            regions.append([x, y, x + w, y + h, conf])

        if len(regions) > 1:
            regions = self._merge_overlapping_regions(regions)
        if regions:
            best = max(regions, key=lambda r: r[4])
            return [best]
        return []

    # ---------------- 新增：垃圾焚烧检测 ----------------
    def _detect_garbage_burning(self, image):
        """
        检测图像中的垃圾焚烧区域，整体框出燃烧垃圾本体及伴随的浓烟。
        返回: list[[x1, y1, x2, y2, conf]]
        """
        height, width = image.shape[:2]
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # ---------------- 火焰像素 ----------------
        lower_red1 = np.array([0, 120, 120]);  upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 120, 120]); upper_red2 = np.array([180, 255, 255])
        lower_orange = np.array([10, 100, 100]); upper_orange = np.array([35, 255, 255])
        lower_yellow = np.array([20, 60, 180]); upper_yellow = np.array([40, 255, 255])

        mask_flame = cv2.bitwise_or(cv2.inRange(hsv, lower_red1, upper_red1), cv2.inRange(hsv, lower_red2, upper_red2))
        mask_flame = cv2.bitwise_or(mask_flame, cv2.inRange(hsv, lower_orange, upper_orange))
        mask_flame = cv2.bitwise_or(mask_flame, cv2.inRange(hsv, lower_yellow, upper_yellow))

        # ---------------- 浓烟像素 ----------------
        # 深灰/黑烟：低亮度低饱和
        lower_smoke_dark = np.array([0, 0, 40]);   upper_smoke_dark = np.array([180, 60, 150])
        # 浅灰/白烟：低饱和高亮度
        lower_smoke_light = np.array([0, 0, 150]); upper_smoke_light = np.array([180, 40, 255])

        mask_smoke = cv2.bitwise_or(cv2.inRange(hsv, lower_smoke_dark, upper_smoke_dark),
                                     cv2.inRange(hsv, lower_smoke_light, upper_smoke_light))

        # 合并火焰与烟雾
        mask = cv2.bitwise_or(mask_flame, mask_smoke)

        # 形态学处理
        kernel = np.ones((5, 5), np.uint8)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        mask = cv2.dilate(mask, kernel, iterations=1)

        # 连通域提取
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        regions = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area < 1500:  # 过滤过小区域
                continue
            x, y, w, h = cv2.boundingRect(cnt)
            roi_flame = mask_flame[y:y+h, x:x+w]
            roi_smoke = mask_smoke[y:y+h, x:x+w]
            flame_cov = np.count_nonzero(roi_flame) / (w * h + 1e-6)
            smoke_cov = np.count_nonzero(roi_smoke) / (w * h + 1e-6)
            coverage = np.count_nonzero(mask[y:y+h, x:x+w]) / (w * h + 1e-6)
            conf = min(1.0, 0.5 * flame_cov + 0.3 * smoke_cov + 0.2 * coverage + area / (width * height))
            regions.append([x, y, x + w, y + h, conf])

        # 合并重叠区域并返回置信度最高的一块
        if len(regions) > 1:
            regions = self._merge_overlapping_regions(regions)
        if regions:
            return [max(regions, key=lambda r: r[4])]
        return []
    
    def _is_construction_activity(self, image, bbox):
        """
        判断检测框内是否有建筑活动迹象
        
        参数:
            image: 输入图像
            bbox: 边界框坐标 [x1, y1, x2, y2]
            
        返回:
            is_construction: 是否是建筑活动
        """
        # 简单实现，始终返回True
        # 在实际应用中，可以基于图像特征进行更复杂的判断
        return True
    
    def fine_tune(self, data_yaml, epochs=50, batch_size=16, img_size=640):
        """
        在自定义数据集上微调模型
        
        参数:
            data_yaml: 数据集配置文件路径
            epochs: 训练轮数
            batch_size: 批次大小
            img_size: 图像大小
        """
        # 使用YOLOv8训练API进行微调
        self.model.train(
            data=data_yaml,
            epochs=epochs,
            batch=batch_size,
            imgsz=img_size,
            patience=15,  # 早停耐心值
            save=True     # 保存最佳模型
        )
        
    def export_model(self, format="onnx"):
        """
        导出模型为其他格式，方便部署
        
        参数:
            format: 导出格式，如'onnx', 'tflite', 'coreml'等
        """
        self.model.export(format=format)

    # ---------------- 新增：土方作业未降尘施工机械检测 ----------------
    def _detect_earthwork_machine(self, image):
        """
        检测图像中正在产生扬尘的施工机械（如装载机、挖掘机等）。
        算法思路：
        1. 通过颜色识别出黄色/橙色的施工机械主体；
        2. 判断机械周围是否存在明显扬尘（白/灰烟雾掩码）；
        3. 同时满足两者则认为是土方作业未降尘，返回机械整体框。
        返回: list[[x1, y1, x2, y2, conf]]
        """
        height, width = image.shape[:2]
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # 1. 机械主体颜色（常见工程机械为黄色/橙色）
        lower_yellow = np.array([15, 80, 80]); upper_yellow = np.array([40, 255, 255])
        lower_orange = np.array([10, 80, 80]); upper_orange = np.array([25, 255, 255])
        mask_yellow = cv2.inRange(hsv, lower_yellow, upper_yellow)
        mask_orange = cv2.inRange(hsv, lower_orange, upper_orange)
        mask_machine = cv2.bitwise_or(mask_yellow, mask_orange)

        # 形态学处理去噪
        kernel = np.ones((5, 5), np.uint8)
        mask_machine = cv2.morphologyEx(mask_machine, cv2.MORPH_OPEN, kernel, iterations=1)
        mask_machine = cv2.morphologyEx(mask_machine, cv2.MORPH_CLOSE, kernel, iterations=2)

        # 小核先去噪
        kernel_small = np.ones((5, 5), np.uint8)
        mask_machine = cv2.morphologyEx(mask_machine, cv2.MORPH_OPEN, kernel_small, iterations=1)
        mask_machine = cv2.morphologyEx(mask_machine, cv2.MORPH_CLOSE, kernel_small, iterations=2)

        # 使用大核闭运算合并机械的分散部件，便于一次性框出整车
        kernel_big = np.ones((25, 25), np.uint8)
        mask_machine_merge = cv2.morphologyEx(mask_machine, cv2.MORPH_CLOSE, kernel_big, iterations=1)

        # 2. 扬尘掩码（低饱和高亮或低亮深灰）
        lower_dust_light = np.array([0, 0, 160]); upper_dust_light = np.array([180, 60, 255])
        lower_dust_dark = np.array([0, 0, 60]);  upper_dust_dark = np.array([180, 60, 140])
        mask_dust = cv2.bitwise_or(cv2.inRange(hsv, lower_dust_light, upper_dust_light),
                                   cv2.inRange(hsv, lower_dust_dark, upper_dust_dark))
        mask_dust = cv2.morphologyEx(mask_dust, cv2.MORPH_OPEN, kernel_small, iterations=1)
        mask_dust = cv2.morphologyEx(mask_dust, cv2.MORPH_CLOSE, kernel_small, iterations=2)

        # 轻微膨胀尘土区域，用于判断扬尘靠近机械而非覆盖整图
        dust_dilate_kernel = np.ones((9, 9), np.uint8)
        mask_dust_dilated = cv2.dilate(mask_dust, dust_dilate_kernel, iterations=1)

        combined_mask = cv2.bitwise_or(mask_machine_merge, mask_dust_dilated)

        # 仍以机械主体掩码为主，避免整图被选中
        contours, _ = cv2.findContours(mask_machine_merge, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        regions = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area < 5000:  # 过滤较小候选
                continue
            x, y, w, h = cv2.boundingRect(cnt)

            # 在机械框基础上向四周最多扩张 15%，但不得超过图片尺寸
            pad_w = min(int(w * 0.25), int(0.25 * width))
            pad_h = min(int(h * 0.25), int(0.25 * height))
            x1 = max(0, x - pad_w); y1 = max(0, y - pad_h)
            x2 = min(width - 1, x + w + pad_w); y2 = min(height - 1, y + h + pad_h)

            # 计算扩张框内尘土覆盖率（使用膨胀后掩码）
            roi_dust = mask_dust_dilated[y1:y2, x1:x2]
            dust_cov = np.count_nonzero(roi_dust) / ((x2 - x1) * (y2 - y1) + 1e-6)
            if dust_cov < 0.05:  # 尘土过少跳过
                continue

            # 机械掩码覆盖率
            roi_machine = mask_machine_merge[y1:y2, x1:x2]
            machine_cov = np.count_nonzero(roi_machine) / ((x2 - x1) * (y2 - y1) + 1e-6)

            conf = min(1.0, 0.5 * machine_cov + 0.5 * dust_cov + area / (width * height))
            regions.append([x1, y1, x2, y2, conf])

        # 合并重叠并返回最佳区域
        if len(regions) > 1:
            regions = self._merge_overlapping_regions(regions)
        if regions:
            return [max(regions, key=lambda r: r[4])]
        return []

    # ---------------- 新增：夜间违规施工机械检测 ----------------
    def _detect_night_construction_machine(self, image):
        """
        识别夜间仍在作业的施工机械。
        算法：
        1. 低亮度环境下，提取高亮区域（作业灯光）;
        2. 在这些高亮周围搜索黄色/橙色机械主体;
        3. 满足二者则框选机械周围区域。
        返回 list[[x1,y1,x2,y2,conf]]
        """
        height, width = image.shape[:2]
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # 高亮灯光区域（低饱和高亮）
        lower_light = np.array([0, 0, 180]); upper_light = np.array([180, 60, 255])
        mask_light = cv2.inRange(hsv, lower_light, upper_light)
        kernel = np.ones((3,3), np.uint8)
        mask_light = cv2.morphologyEx(mask_light, cv2.MORPH_CLOSE, kernel, iterations=2)

        # 机械主体常见颜色（黄色/橙色/绿色）
        lower_yellow = np.array([15, 40, 40]); upper_yellow = np.array([40, 255, 255])
        lower_orange = np.array([5, 50, 40]);  upper_orange = np.array([25, 255, 255])
        lower_green  = np.array([35, 30, 40]); upper_green  = np.array([85, 255, 255])

        mask_yellow = cv2.inRange(hsv, lower_yellow, upper_yellow)
        mask_orange = cv2.inRange(hsv, lower_orange, upper_orange)
        mask_green  = cv2.inRange(hsv, lower_green,  upper_green)

        mask_machine = cv2.bitwise_or(mask_yellow, cv2.bitwise_or(mask_orange, mask_green))

        # 处理：形态学闭运算合并零散部件
        kernel_small = np.ones((5,5), np.uint8)
        mask_machine = cv2.morphologyEx(mask_machine, cv2.MORPH_OPEN, kernel_small, iterations=1)
        mask_machine = cv2.morphologyEx(mask_machine, cv2.MORPH_CLOSE, kernel_small, iterations=2)

        # 膨胀灯光掩码以覆盖机械主体附近
        mask_light_dilated = cv2.dilate(mask_light, kernel, iterations=5)

        # 仅保留灯光与机械同时存在的区域，避免大面积灯光导致整图被框
        combined = cv2.bitwise_and(mask_light_dilated, mask_machine)

        contours, _ = cv2.findContours(combined, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        regions = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area < 1500:  # 过滤过小区域
                continue
            x, y, w, h = cv2.boundingRect(cnt)
            aspect = w / h if h > 0 else 0
            # 机械形状约束：宽高比1~4，且长边>80px
            if not (1.0 < aspect < 4.5 and max(w, h) > 80):
                continue
            # 适度扩张 20% 覆盖机械主体，不超过图像25%
            pad_w = min(int(w * 0.2), int(0.25 * width))
            pad_h = min(int(h * 0.2), int(0.25 * height))
            x1 = max(0, x - pad_w); y1 = max(0, y - pad_h)
            x2 = min(width-1, x + w + pad_w); y2 = min(height-1, y + h + pad_h)
            # 若框过大 (>70%全图) 则忽略
            if (x2 - x1)*(y2 - y1) > 0.7*width*height:
                continue
            # 交集区域必须有足够机械像素
            roi_light = mask_light_dilated[y1:y2, x1:x2]
            roi_mech  = mask_machine[y1:y2, x1:x2]
            inter = cv2.bitwise_and(roi_light, roi_mech)
            inter_cov = np.count_nonzero(inter) / ((x2-x1)*(y2-y1)+1e-6)
            mech_cov  = np.count_nonzero(roi_mech) / ((x2-x1)*(y2-y1)+1e-6)
            light_cov = np.count_nonzero(roi_light) / ((x2-x1)*(y2-y1)+1e-6)
            # 交集区域机械像素比例需>0.08
            if inter_cov < 0.08:
                continue
            conf = min(1.0, 0.5*inter_cov + 0.3*mech_cov + 0.2*light_cov)
            regions.append([x1, y1, x2, y2, conf])

        if len(regions) > 1:
            regions = self._merge_overlapping_regions(regions)
        if regions:
            return [max(regions, key=lambda r: r[4])]
        return []