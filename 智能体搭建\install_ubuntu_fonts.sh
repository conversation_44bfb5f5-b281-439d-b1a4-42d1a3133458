#!/bin/bash

# Ubuntu中文字体安装脚本
# 用于解决matplotlib图表中文显示问题

echo "🚀 开始安装Ubuntu中文字体..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  请使用sudo运行此脚本"
    echo "用法: sudo bash install_ubuntu_fonts.sh"
    exit 1
fi

# 更新包列表
echo "📦 更新包列表..."
apt-get update

# 安装中文字体包
echo "🔤 安装中文字体包..."
apt-get install -y fonts-wqy-microhei fonts-wqy-zenhei fonts-arphic-ukai fonts-arphic-uming

# 安装额外的中文字体
echo "📝 安装额外字体..."
apt-get install -y fonts-droid-fallback ttf-wqy-zenhei ttf-wqy-microhei

# 安装字体工具
echo "🔧 安装字体工具..."
apt-get install -y fontconfig

# 刷新字体缓存
echo "🔄 刷新字体缓存..."
fc-cache -fv

# 检查安装的中文字体
echo "✅ 检查已安装的中文字体:"
fc-list :lang=zh | head -10

# 创建matplotlib字体缓存目录
echo "📁 创建matplotlib缓存目录..."
mkdir -p /root/.cache/matplotlib
mkdir -p /home/<USER>/.cache/matplotlib 2>/dev/null || true

# 清除matplotlib字体缓存
echo "🗑️  清除matplotlib字体缓存..."
find /root/.cache/matplotlib -name "*.cache" -delete 2>/dev/null || true
find /home/<USER>/.cache/matplotlib -name "*.cache" -delete 2>/dev/null || true

# 设置环境变量
echo "🌍 设置字体环境变量..."
echo 'export LANG=zh_CN.UTF-8' >> /etc/environment
echo 'export LC_ALL=zh_CN.UTF-8' >> /etc/environment

# 生成locale
echo "🌐 配置locale..."
locale-gen zh_CN.UTF-8

echo ""
echo "✨ 字体安装完成!"
echo ""
echo "📋 安装的字体包括:"
echo "  - WenQuanYi Micro Hei (文泉驿微米黑)"
echo "  - WenQuanYi Zen Hei (文泉驿正黑)"
echo "  - AR PL UKai (文鼎PL简中楷)"
echo "  - AR PL UMing (文鼎PL简明体)"
echo ""
echo "🔄 请重启Python应用以使字体生效"
echo "💡 或者运行: python -c \"import matplotlib.pyplot as plt; plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei']\""
echo ""
echo "🧪 测试字体是否正常:"
echo "python3 -c \"import matplotlib.pyplot as plt; import matplotlib.font_manager as fm; print([f.name for f in fm.fontManager.ttflist if 'WenQuanYi' in f.name])\""
