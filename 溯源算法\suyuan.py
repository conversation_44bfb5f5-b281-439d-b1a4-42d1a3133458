"""
基于遗传-模式搜索算法的微尺度管控区域大气污染物PM2.5溯源系统
参考论文：董红召等，基于遗传-模式搜索算法的微尺度管控区域大气污染物PM2.5溯源，浙江大学学报(工学版)，2024
"""

import numpy as np
from deap import base, creator, tools, algorithms
from scipy.optimize import minimize, differential_evolution
from sklearn.ensemble import RandomForestRegressor
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler
import random
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

class AtmosphericStabilityClassifier:
    """大气稳定度分类器 - 根据论文表1实现"""

    @staticmethod
    def classify_stability(wind_speed, solar_radiation=None, cloud_cover=None, is_daytime=True):
        """
        根据风速、太阳辐射强度和云量确定大气稳定度等级
        参数:
            wind_speed: 风速 (m/s)
            solar_radiation: 太阳辐射强度 ('强'/'中'/'弱')
            cloud_cover: 云量 (0-1)
            is_daytime: 是否白天
        返回:
            stability: 大气稳定度等级 ('A'/'B'/'C'/'D'/'E'/'F')
        """
        if is_daytime and solar_radiation:
            # 白天条件下根据太阳辐射和风速确定
            if wind_speed < 2:
                if solar_radiation == '强':
                    return 'A'
                elif solar_radiation == '中':
                    return 'A' if wind_speed < 1 else 'B'
                else:  # 弱
                    return 'C'
            elif wind_speed < 3:
                if solar_radiation == '强':
                    return 'A' if wind_speed < 2.5 else 'B'
                elif solar_radiation == '中':
                    return 'B'
                else:  # 弱
                    return 'C'
            elif wind_speed < 5:
                if solar_radiation == '强':
                    return 'B'
                elif solar_radiation == '中':
                    return 'B' if wind_speed < 4 else 'C'
                else:  # 弱
                    return 'D'
            elif wind_speed < 6:
                return 'C' if solar_radiation in ['强', '中'] else 'D'
            else:
                return 'D'
        else:
            # 夜晚条件下根据云量和风速确定
            if cloud_cover is None:
                cloud_cover = 0.5  # 默认值

            if wind_speed < 2:
                return 'F' if cloud_cover < 0.4 else 'E'
            elif wind_speed < 3:
                return 'E' if cloud_cover < 0.7 else 'D'
            elif wind_speed < 5:
                return 'D'
            else:
                return 'D'

class WRFMeteorologicalModel:
    """WRF气象模型模拟器 - 增强版"""
    def __init__(self):
        self.wind_field = {}
        self.stability_field = {}
        self.stability_classifier = AtmosphericStabilityClassifier()

    def get_meteorological_data(self, x, y, time):
        """获取指定位置和时间的气象数据"""
        # 模拟WRF气象数据（实际应用中应从WRF输出文件读取）
        wind_speed = max(0.1, 2.0 + 0.5 * np.sin(time * 0.1) + 0.1 * np.random.randn())
        wind_direction = 45 + 10 * np.cos(time * 0.05) + 5 * np.random.randn()

        # 根据时间确定是否白天
        hour = time % 24
        is_daytime = 6 <= hour <= 18

        # 模拟太阳辐射和云量
        if is_daytime:
            solar_radiation = random.choice(['强', '中', '弱'])
            stability = self.stability_classifier.classify_stability(
                wind_speed, solar_radiation=solar_radiation, is_daytime=True
            )
        else:
            cloud_cover = random.uniform(0, 1)
            stability = self.stability_classifier.classify_stability(
                wind_speed, cloud_cover=cloud_cover, is_daytime=False
            )

        return wind_speed, wind_direction, stability

class GaussianPlumeModel:
    """
    增强的高斯烟羽模型 - 基于论文公式(1)实现
    适用于微尺度管控区域PM2.5污染物扩散计算
    """
    def __init__(self, wrf_model=None):
        self.wrf_model = wrf_model or WRFMeteorologicalModel()
        # 论文表2中的扩散系数参数
        self.diffusion_params = {
            'A': {'sigma_y': [0.32, 0.0004, -0.5], 'sigma_z': [0.24, 0.0001, -0.5]},
            'B': {'sigma_y': [0.32, 0.0004, -0.5], 'sigma_z': [0.24, 0.0001, -0.5]},
            'C': {'sigma_y': [0.22, 0.0004, -0.5], 'sigma_z': [0.20, 0.0, 0.5]},
            'D': {'sigma_y': [0.16, 0.0004, -0.5], 'sigma_z': [0.14, 0.0003, -0.5]},
            'E': {'sigma_y': [0.11, 0.0004, -0.5], 'sigma_z': [0.08, 0.0150, -0.5]},
            'F': {'sigma_y': [0.11, 0.0004, -0.5], 'sigma_z': [0.08, 0.0150, -0.5]}
        }

    def get_sigma(self, x, stability):
        """
        根据论文表2计算扩散系数
        参数:
            x: 下风向距离 (m)
            stability: 大气稳定度等级
        返回:
            sigma_y, sigma_z: 水平和垂直扩散系数
        """
        x = max(x, 1.0)  # 避免x为0的情况

        if stability not in self.diffusion_params:
            raise ValueError(f"Unknown stability class: {stability}")

        params = self.diffusion_params[stability]

        # 计算sigma_y
        a_y, b_y, c_y = params['sigma_y']
        if b_y == 0:  # 特殊情况：C类稳定度的sigma_z
            sigma_y = a_y * (x ** c_y)
        else:
            sigma_y = a_y * x * ((1 + b_y * x) ** c_y)

        # 计算sigma_z
        a_z, b_z, c_z = params['sigma_z']
        if b_z == 0:  # 特殊情况：C类稳定度的sigma_z
            sigma_z = a_z * (x ** c_z)
        else:
            sigma_z = a_z * x * ((1 + b_z * x) ** c_z)

        return max(sigma_y, 1.0), max(sigma_z, 1.0)  # 避免过小的扩散系数

    def compute_concentration(self, x_s, y_s, H, Q, u, theta_deg, stability, x_r, y_r, z_r, time=0):
        """
        根据论文公式(1)计算污染物浓度
        参数:
            x_s, y_s: 污染源位置坐标 (m)
            H: 排放源高度 (m)
            Q: 大气污染源排放源强 (g/s)
            u: 平均风速 (m/s)
            theta_deg: 风向角度 (度)
            stability: 大气稳定度等级
            x_r, y_r, z_r: 监测点位置坐标 (m)
            time: 时间步长（可选）
        返回:
            rho_cal: 理论质量浓度 (μg/m³)
        """
        # 坐标变换：建立以污染源为原点，风向为x轴的坐标系
        theta = np.radians(theta_deg)

        # 坐标变换矩阵
        dx = x_r - x_s
        dy = y_r - y_s

        # 转换到风向坐标系
        x_prime = dx * np.cos(theta) + dy * np.sin(theta)  # 下风向距离
        y_prime = -dx * np.sin(theta) + dy * np.cos(theta)  # 横风向距离

        # 如果监测点在上风向，浓度为0
        if x_prime <= 0:
            return 0.0

        # 获取扩散系数
        sigma_y, sigma_z = self.get_sigma(x_prime, stability)

        # 根据论文公式(1)计算浓度
        # ρ_cal = (q / (2π * u * σ_y * σ_z)) * exp(-y²/(2σ_y²)) * [exp(-(z-H)²/(2σ_z²)) + exp(-(z+H)²/(2σ_z²))]

        term1 = Q / (2 * np.pi * u * sigma_y * sigma_z)
        term2 = np.exp(-y_prime**2 / (2 * sigma_y**2))
        term3 = (np.exp(-(z_r - H)**2 / (2 * sigma_z**2)) +
                np.exp(-(z_r + H)**2 / (2 * sigma_z**2)))

        concentration = term1 * term2 * term3

        # 转换单位：从 g/m³ 到 μg/m³
        return concentration * 1e6

    def simulate_dispersion_trajectory(self, x_s, y_s, H, Q, time_steps=24):
        """模拟污染物扩散轨迹"""
        trajectory = []
        for t in range(time_steps):
            u, theta_deg, stability = self.wrf_model.get_meteorological_data(x_s, y_s, t)
            # 计算不同距离处的浓度分布
            distances = np.linspace(100, 5000, 50)
            concentrations = []
            for dist in distances:
                x_r = x_s + dist * np.cos(np.radians(theta_deg))
                y_r = y_s + dist * np.sin(np.radians(theta_deg))
                z_r = 1.5  # 地面高度
                conc = self.compute_concentration(x_s, y_s, H, Q, u, theta_deg, stability, x_r, y_r, z_r, t)
                concentrations.append(conc)
            trajectory.append({
                'time': t,
                'wind_speed': u,
                'wind_direction': theta_deg,
                'stability': stability,
                'distances': distances,
                'concentrations': concentrations
            })
        return trajectory

class DataPreprocessor:
    """
    数据预处理模块 - 根据论文数据处理方法实现
    包括异常值处理和MLP神经网络数据补全
    """
    def __init__(self):
        self.mlp_model = MLPRegressor(
            hidden_layer_sizes=(100, 50, 25),
            max_iter=2000,
            random_state=42,
            early_stopping=True,
            validation_fraction=0.1
        )
        self.scaler = StandardScaler()
        self.is_trained = False

    def detect_anomalies(self, pm25_data, threshold_factor=3):
        """
        检测PM2.5数据中的异常值
        参数:
            pm25_data: PM2.5浓度数据数组
            threshold_factor: 异常值检测阈值因子
        返回:
            anomaly_mask: 异常值掩码（True表示异常值）
        """
        # 1. 检测明显异常值（负数、超出量程9999等）
        obvious_anomalies = (pm25_data < 0) | (pm25_data > 9000)

        # 2. 使用3σ准则检测统计异常值
        mean_val = np.nanmean(pm25_data[~obvious_anomalies])
        std_val = np.nanstd(pm25_data[~obvious_anomalies])
        statistical_anomalies = np.abs(pm25_data - mean_val) > threshold_factor * std_val

        return obvious_anomalies | statistical_anomalies

    def fill_missing_data(self, sensor_data):
        """
        使用MLP神经网络填补缺失的PM2.5数据
        参数:
            sensor_data: 传感器数据字典，包含PM2.5, PM10, 温度, 湿度, 风速, 风向
        返回:
            filled_pm25: 填补后的PM2.5数据
        """
        pm25 = sensor_data['PM2.5'].copy()
        pm10 = sensor_data.get('PM10', np.full_like(pm25, np.nan))
        temperature = sensor_data.get('temperature', np.full_like(pm25, 20.0))
        humidity = sensor_data.get('humidity', np.full_like(pm25, 50.0))
        wind_speed = sensor_data.get('wind_speed', np.full_like(pm25, 2.0))
        wind_direction = sensor_data.get('wind_direction', np.full_like(pm25, 45.0))

        # 检测异常值和缺失值
        anomaly_mask = self.detect_anomalies(pm25)
        missing_mask = np.isnan(pm25) | anomaly_mask

        if np.sum(~missing_mask) < 10:  # 如果有效数据太少，无法训练
            print("警告：有效数据点太少，无法进行MLP补全")
            return pm25

        # 准备训练数据
        features = np.column_stack([pm10, temperature, humidity, wind_speed, wind_direction])
        valid_indices = ~missing_mask & ~np.isnan(features).any(axis=1)

        if np.sum(valid_indices) < 10:
            print("警告：有效特征数据太少，无法进行MLP补全")
            return pm25

        # 训练MLP模型
        X_train = features[valid_indices]
        y_train = pm25[valid_indices]

        X_train_scaled = self.scaler.fit_transform(X_train)
        self.mlp_model.fit(X_train_scaled, y_train)
        self.is_trained = True

        # 预测缺失值
        missing_indices = missing_mask & ~np.isnan(features).any(axis=1)
        if np.sum(missing_indices) > 0:
            X_missing = features[missing_indices]
            X_missing_scaled = self.scaler.transform(X_missing)
            predicted_values = self.mlp_model.predict(X_missing_scaled)

            # 填补缺失值
            filled_pm25 = pm25.copy()
            filled_pm25[missing_indices] = predicted_values

            return filled_pm25

        return pm25

    def preprocess_sensor_network_data(self, sensor_network_data):
        """
        预处理传感器网络数据
        参数:
            sensor_network_data: 多传感器数据字典
        返回:
            processed_data: 预处理后的数据
        """
        processed_data = {}

        for sensor_id, sensor_data in sensor_network_data.items():
            print(f"处理传感器 {sensor_id} 的数据...")

            # 时间序列匹配和数据补全
            filled_pm25 = self.fill_missing_data(sensor_data)

            processed_data[sensor_id] = {
                'PM2.5': filled_pm25,
                'PM10': sensor_data.get('PM10'),
                'temperature': sensor_data.get('temperature'),
                'humidity': sensor_data.get('humidity'),
                'wind_speed': sensor_data.get('wind_speed'),
                'wind_direction': sensor_data.get('wind_direction'),
                'timestamp': sensor_data.get('timestamp')
            }

        return processed_data

class MachineLearningEnhancer:
    """机器学习增强模块"""
    def __init__(self):
        self.rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.nn_model = MLPRegressor(hidden_layer_sizes=(100, 50), max_iter=1000, random_state=42)
        self.scaler = StandardScaler()
        self.is_trained = False

    def prepare_features(self, x_s, y_s, Q, x_r, y_r, z_r, u, theta_deg, stability_code):
        """准备机器学习特征"""
        stability_map = {'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5, 'F': 6}
        features = [
            x_s, y_s, Q, x_r, y_r, z_r, u, theta_deg,
            stability_map.get(stability_code, 4),
            np.sqrt((x_r - x_s)**2 + (y_r - y_s)**2),  # 距离
            np.arctan2(y_r - y_s, x_r - x_s) * 180 / np.pi  # 方位角
        ]
        return np.array(features).reshape(1, -1)

    def train(self, training_data):
        """训练机器学习模型"""
        X, y = [], []
        for data_point in training_data:
            x_s, y_s, Q, x_r, y_r, z_r, u, theta_deg, stability, C_obs = data_point
            features = self.prepare_features(x_s, y_s, Q, x_r, y_r, z_r, u, theta_deg, stability)
            X.append(features.flatten())
            y.append(C_obs)

        X = np.array(X)
        y = np.array(y)

        X_scaled = self.scaler.fit_transform(X)
        self.rf_model.fit(X_scaled, y)
        self.nn_model.fit(X_scaled, y)
        self.is_trained = True

    def predict(self, x_s, y_s, Q, x_r, y_r, z_r, u, theta_deg, stability):
        """预测浓度"""
        if not self.is_trained:
            return 0
        features = self.prepare_features(x_s, y_s, Q, x_r, y_r, z_r, u, theta_deg, stability)
        features_scaled = self.scaler.transform(features)
        rf_pred = self.rf_model.predict(features_scaled)[0]
        nn_pred = self.nn_model.predict(features_scaled)[0]
        return (rf_pred + nn_pred) / 2  # 集成预测

class HybridSourceInversion:
    """
    混合模型污染源反算系统 - 基于遗传-模式搜索算法
    实现论文中的微尺度管控区域PM2.5溯源方法
    """
    def __init__(self):
        self.gaussian_model = GaussianPlumeModel()
        self.ml_enhancer = MachineLearningEnhancer()
        self.wrf_model = WRFMeteorologicalModel()
        self.data_preprocessor = DataPreprocessor()

    def objective_function(self, params, data, use_ml=False):
        """
        目标函数 - 根据论文公式(2)实现
        计算理论质量浓度与测量质量浓度的误差平方和
        """
        x_s, y_s, Q = params
        total_error = 0

        for x_r, y_r, z_r, C_obs, u, theta_deg, stability in data:
            if use_ml and self.ml_enhancer.is_trained:
                # 使用机器学习增强预测
                C_model = self.ml_enhancer.predict(x_s, y_s, Q, x_r, y_r, z_r, u, theta_deg, stability)
            else:
                # 使用高斯烟羽模型计算理论浓度
                C_model = self.gaussian_model.compute_concentration(
                    x_s, y_s, 10.0, Q, u, theta_deg, stability, x_r, y_r, z_r
                )

            # 计算误差平方和（论文公式2）
            error = (C_obs - C_model)**2
            total_error += error

        return total_error

    def fitness_function(self, params, data, T=1.0):
        """
        适应度函数 - 根据论文公式(4)实现
        使用指数形式的适应度函数，适应度越高表示解越接近真实值
        参数:
            params: 参数向量 [x_s, y_s, Q]
            data: 观测数据
            T: 可调节常数
        返回:
            fitness: 适应度值（非负）
        """
        f_value = self.objective_function(params, data)

        # 根据论文公式(4)计算适应度
        # γ = exp(-|f(x,y,z,q)|/T) / Σexp(-|f(xi,yi,zi,qi)|/T)
        # 这里简化为单个个体的适应度计算
        fitness = np.exp(-abs(f_value) / T)

        return fitness

    def pattern_search_optimization(self, initial_point, data, bounds=None,
                                  step_size=10.0, tolerance=1e-6, max_iterations=100):
        """
        模式搜索算法 - 根据论文描述实现
        用于解决最优化问题，通过找出接近梯度方向的向量进行搜索
        参数:
            initial_point: 初始搜索点
            data: 观测数据
            bounds: 搜索边界
            step_size: 初始步长
            tolerance: 收敛容差
            max_iterations: 最大迭代次数
        """
        if bounds is None:
            bounds = [(-500, 500), (-500, 500), (0.1, 10.0)]

        current_point = np.array(initial_point, dtype=float)
        current_fitness = self.fitness_function(current_point, data)

        # 坐标轴搜索方向
        directions = np.array([
            [1, 0, 0], [-1, 0, 0],    # x方向
            [0, 1, 0], [0, -1, 0],    # y方向
            [0, 0, 1], [0, 0, -1]     # q方向
        ])

        iteration = 0
        improvement_history = []

        print(f"模式搜索初始点: {current_point}, 初始适应度: {current_fitness:.6f}")

        while step_size > tolerance and iteration < max_iterations:
            improved = False
            best_direction = None
            best_point = None
            best_fitness = current_fitness

            # 在所有方向上搜索
            for direction in directions:
                test_point = current_point + step_size * direction

                # 边界约束
                for i, (min_val, max_val) in enumerate(bounds):
                    test_point[i] = max(min_val, min(max_val, test_point[i]))

                try:
                    test_fitness = self.fitness_function(test_point, data)

                    # 寻找适应度更高的点（最大化适应度）
                    if test_fitness > best_fitness:
                        best_fitness = test_fitness
                        best_point = test_point.copy()
                        best_direction = direction.copy()
                        improved = True

                except:
                    continue  # 如果计算失败，跳过这个点

            if improved:
                current_point = best_point
                current_fitness = best_fitness
                improvement_history.append({
                    'iteration': iteration,
                    'point': current_point.copy(),
                    'fitness': current_fitness,
                    'step_size': step_size,
                    'direction': best_direction
                })

                if iteration % 10 == 0:
                    print(f"模式搜索第 {iteration} 次迭代: 适应度 = {current_fitness:.6f}, 步长 = {step_size:.3f}")
            else:
                # 如果没有改进，减小步长
                step_size *= 0.5
                if iteration % 20 == 0:
                    print(f"模式搜索第 {iteration} 次迭代: 减小步长至 {step_size:.6f}")

            iteration += 1

        # 计算最终的目标函数值（用于比较）
        final_objective = self.objective_function(current_point, data)

        print(f"模式搜索完成: 最终适应度 = {current_fitness:.6f}, 目标函数值 = {final_objective:.6f}")
        print(f"最终解: x={current_point[0]:.2f}, y={current_point[1]:.2f}, q={current_point[2]:.4f}")

        return current_point, final_objective

    def genetic_algorithm_with_pattern_search(self, data, population_size=50, generations=50,
                                            search_bounds=None):
        """
        遗传算法嵌入模式搜索 - 根据论文图1流程实现
        参数:
            data: 观测数据
            population_size: 种群大小
            generations: 遗传代数
            search_bounds: 搜索边界 [(x_min, x_max), (y_min, y_max), (q_min, q_max)]
        """
        if search_bounds is None:
            search_bounds = [(-500, 500), (-500, 500), (0.1, 10.0)]

        # 清理之前的DEAP类定义
        if hasattr(creator, "FitnessMax"):
            del creator.FitnessMax
        if hasattr(creator, "Individual"):
            del creator.Individual

        # DEAP 遗传算法设置 - 使用最大化适应度（论文中适应度越高越好）
        creator.create("FitnessMax", base.Fitness, weights=(1.0,))
        creator.create("Individual", list, fitness=creator.FitnessMax)

        toolbox = base.Toolbox()

        def init_individual():
            """初始化个体"""
            individual = []
            for (min_val, max_val) in search_bounds:
                individual.append(random.uniform(min_val, max_val))
            return individual

        toolbox.register("individual", tools.initIterate, creator.Individual, init_individual)
        toolbox.register("population", tools.initRepeat, list, toolbox.individual)

        def evaluate_individual(individual):
            """评估个体适应度"""
            try:
                fitness = self.fitness_function(individual, data)
                return (fitness,)
            except:
                return (0.0,)  # 如果计算失败，返回最低适应度

        toolbox.register("evaluate", evaluate_individual)
        toolbox.register("mate", tools.cxBlend, alpha=0.5)
        toolbox.register("mutate", tools.mutGaussian, mu=0, sigma=50, indpb=0.2)
        toolbox.register("select", tools.selRoulette)  # 使用轮盘赌选择（论文中提到）

        # 初始化种群
        pop = toolbox.population(n=population_size)
        hof = tools.HallOfFame(1)
        stats = tools.Statistics(lambda ind: ind.fitness.values)
        stats.register("avg", np.mean)
        stats.register("max", np.max)
        stats.register("min", np.min)

        # 遗传算法参数（根据论文）
        P_C = 0.6  # 交叉率
        P_M = 0.2  # 变异率
        P_E = 0.1  # 精英保留率

        print(f"开始遗传-模式搜索算法，种群大小: {population_size}, 代数: {generations}")

        # 运行遗传算法主循环
        generation_log = []
        for gen in range(generations):
            # 评估种群
            fitnesses = list(map(toolbox.evaluate, pop))
            for ind, fit in zip(pop, fitnesses):
                ind.fitness.values = fit

            # 记录统计信息
            fits = [ind.fitness.values[0] for ind in pop]
            generation_log.append({
                'generation': gen,
                'max_fitness': max(fits),
                'avg_fitness': np.mean(fits),
                'min_fitness': min(fits)
            })

            # 更新名人堂
            hof.update(pop)

            if gen % 10 == 0:
                print(f"第 {gen} 代: 最大适应度 = {max(fits):.6f}, 平均适应度 = {np.mean(fits):.6f}")

            # 选择下一代
            # 1. 精英保留
            elite_size = max(1, int(population_size * P_E))
            elite = tools.selBest(pop, elite_size)

            # 2. 轮盘赌选择
            selected = toolbox.select(pop, population_size - elite_size)

            # 3. 交叉和变异
            offspring = list(map(toolbox.clone, selected))

            # 交叉
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                if random.random() < P_C:
                    toolbox.mate(child1, child2)
                    del child1.fitness.values
                    del child2.fitness.values

            # 变异
            for mutant in offspring:
                if random.random() < P_M:
                    toolbox.mutate(mutant)
                    # 边界约束
                    for i, (min_val, max_val) in enumerate(search_bounds):
                        mutant[i] = max(min_val, min(max_val, mutant[i]))
                    del mutant.fitness.values

            # 组合新种群
            pop = elite + offspring

        # 获取最优个体
        best_ga = hof[0]
        print(f"遗传算法完成，最优适应度: {best_ga.fitness.values[0]:.6f}")

        # 对最优个体进行模式搜索优化
        print("开始模式搜索局部优化...")
        refined_solution, refined_fitness = self.pattern_search_optimization(
            best_ga, data, bounds=search_bounds
        )

        return refined_solution, refined_fitness, generation_log

    def dual_validation_mechanism(self, source_params, data):
        """双向验证机制：反算溯源与正算模拟"""
        x_s, y_s, Q = source_params

        # 正算验证：使用估计的源参数计算浓度分布
        forward_predictions = []
        for x_r, y_r, z_r, C_obs, u, theta_deg, stability in data:
            C_pred = self.gaussian_model.compute_concentration(
                x_s, y_s, 10.0, Q, u, theta_deg, stability, x_r, y_r, z_r
            )
            forward_predictions.append(C_pred)

        # 计算验证指标
        observed = [item[3] for item in data]  # C_obs
        mse = np.mean([(pred - obs)**2 for pred, obs in zip(forward_predictions, observed)])
        rmse = np.sqrt(mse)
        mae = np.mean([abs(pred - obs) for pred, obs in zip(forward_predictions, observed)])

        # 相关系数
        correlation = np.corrcoef(forward_predictions, observed)[0, 1] if len(observed) > 1 else 0

        return {
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'correlation': correlation,
            'predictions': forward_predictions,
            'observations': observed
        }

    def run_complete_analysis(self, data, use_ml_enhancement=True):
        """运行完整的混合模型分析"""
        print("=== 混合模型污染源反算分析 ===")

        # 1. 准备机器学习训练数据
        if use_ml_enhancement:
            print("1. 训练机器学习模型...")
            ml_training_data = []
            for x_r, y_r, z_r, C_obs, u, theta_deg, stability in data:
                # 假设源位置（用于训练）
                ml_training_data.append((500, 500, 5.0, x_r, y_r, z_r, u, theta_deg, stability, C_obs))
            self.ml_enhancer.train(ml_training_data)

        # 2. 遗传算法+模式搜索反算
        print("2. 执行遗传算法+模式搜索反算...")
        best_solution, best_fitness, ga_log = self.genetic_algorithm_with_pattern_search(data)

        # 3. 双向验证
        print("3. 执行双向验证...")
        validation_results = self.dual_validation_mechanism(best_solution, data)

        # 4. 扩散轨迹模拟
        print("4. 模拟污染物扩散轨迹...")
        trajectory = self.gaussian_model.simulate_dispersion_trajectory(
            best_solution[0], best_solution[1], 10.0, best_solution[2]
        )

        return {
            'source_location': (best_solution[0], best_solution[1]),
            'emission_rate': best_solution[2],
            'fitness': best_fitness,
            'validation': validation_results,
            'trajectory': trajectory,
            'ga_log': ga_log
        }

def setup_chinese_font():
    """设置中文字体支持"""
    import matplotlib
    import platform

    # 根据操作系统设置中文字体
    system = platform.system()
    if system == "Windows":
        # Windows系统常用中文字体
        fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        fonts = ['Heiti TC', 'Arial Unicode MS', 'PingFang SC']
    else:  # Linux
        fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC']

    # 尝试设置字体
    for font in fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font]
            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            # 测试字体是否可用
            fig, ax = plt.subplots(figsize=(1, 1))
            ax.text(0.5, 0.5, '测试', fontsize=12)
            plt.close(fig)
            print(f"成功设置中文字体: {font}")
            return True
        except:
            continue

    print("警告: 无法设置中文字体，将使用默认字体")
    return False

def visualize_results(results):
    """可视化分析结果"""
    # 设置中文字体
    setup_chinese_font()

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    # 1. 源位置和观测点
    ax1.scatter(results['source_location'][0], results['source_location'][1],
               c='red', s=200, marker='*', label='估计源位置')
    ax1.set_xlabel('X坐标 (m)')
    ax1.set_ylabel('Y坐标 (m)')
    ax1.set_title('污染源位置估计')
    ax1.legend()
    ax1.grid(True)

    # 2. 观测值vs预测值
    validation = results['validation']
    ax2.scatter(validation['observations'], validation['predictions'], alpha=0.7)
    max_val = max(max(validation['observations']), max(validation['predictions']))
    ax2.plot([0, max_val], [0, max_val], 'r--', label='1:1线')
    ax2.set_xlabel('观测浓度 (μg/m³)')
    ax2.set_ylabel('预测浓度 (μg/m³)')
    ax2.set_title(f'预测精度 (R={validation["correlation"]:.3f})')
    ax2.legend()
    ax2.grid(True)

    # 3. 扩散轨迹
    if 'trajectory' in results and results['trajectory']:
        trajectory = results['trajectory']
        times = [t['time'] for t in trajectory]
        max_concs = [max(t['concentrations']) if t['concentrations'] else 0 for t in trajectory]
        ax3.plot(times, max_concs, 'b-o', markersize=4)
        ax3.set_xlabel('时间 (小时)')
        ax3.set_ylabel('最大浓度 (μg/m³)')
        ax3.set_title('污染物扩散时间序列')
        ax3.grid(True)
    else:
        # 如果没有轨迹数据，显示收敛曲线
        if 'ga_log' in results and results['ga_log']:
            generations = [log['generation'] for log in results['ga_log']]
            max_fitness = [log['max_fitness'] for log in results['ga_log']]
            ax3.plot(generations, max_fitness, 'b-', linewidth=2)
            ax3.set_xlabel('遗传代数')
            ax3.set_ylabel('最大适应度')
            ax3.set_title('算法收敛曲线')
            ax3.grid(True)
        else:
            ax3.text(0.5, 0.5, '无轨迹数据', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('扩散轨迹')

    # 4. 验证指标
    metrics = ['MSE', 'RMSE', 'MAE', '相关系数']
    values = [validation['mse'], validation['rmse'], validation['mae'], validation['correlation']]
    bars = ax4.bar(metrics, values, color=['skyblue', 'lightgreen', 'orange', 'pink'])
    ax4.set_title('模型验证指标')
    ax4.set_ylabel('数值')

    # 在柱状图上添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:.3f}', ha='center', va='bottom', fontsize=10)

    plt.tight_layout()
    plt.show()

def visualize_pm25_results(results, sensor_positions=None):
    """
    专门用于PM2.5溯源结果的可视化
    参数:
        results: 分析结果字典
        sensor_positions: 传感器位置列表 [(x1,y1), (x2,y2), ...]
    """
    setup_chinese_font()

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 1. 污染源位置和传感器分布图
    source_x, source_y = results['source_location']
    ax1.scatter(source_x, source_y, c='red', s=300, marker='*',
               label=f'估计源位置 ({source_x:.0f}, {source_y:.0f})', zorder=5)

    # 绘制传感器位置
    if sensor_positions:
        sensor_x = [pos[0] for pos in sensor_positions]
        sensor_y = [pos[1] for pos in sensor_positions]
        ax1.scatter(sensor_x, sensor_y, c='blue', s=100, marker='o',
                   label='传感器位置', alpha=0.7, zorder=3)

        # 添加传感器编号
        for i, (x, y) in enumerate(sensor_positions):
            ax1.annotate(f'S{i+1}', (x, y), xytext=(5, 5),
                        textcoords='offset points', fontsize=8)

    ax1.set_xlabel('X坐标 (m)')
    ax1.set_ylabel('Y坐标 (m)')
    ax1.set_title('PM2.5污染源定位结果')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.axis('equal')

    # 2. 观测值vs预测值散点图
    validation = results['validation']
    ax2.scatter(validation['observations'], validation['predictions'],
               alpha=0.7, s=60, c='steelblue', edgecolors='white', linewidth=0.5)

    max_val = max(max(validation['observations']), max(validation['predictions']))
    min_val = min(min(validation['observations']), min(validation['predictions']))
    ax2.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='理想拟合线')

    ax2.set_xlabel('观测浓度 (μg/m³)')
    ax2.set_ylabel('预测浓度 (μg/m³)')
    ax2.set_title(f'模型预测精度\n(相关系数 R = {validation["correlation"]:.3f})')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 算法收敛过程
    if 'ga_log' in results and results['ga_log']:
        ga_log = results['ga_log']
        generations = [log['generation'] for log in ga_log]
        max_fitness = [log['max_fitness'] for log in ga_log]
        avg_fitness = [log['avg_fitness'] for log in ga_log]

        ax3.plot(generations, max_fitness, 'r-', linewidth=2, label='最大适应度')
        ax3.plot(generations, avg_fitness, 'b--', linewidth=1.5, label='平均适应度')
        ax3.set_xlabel('遗传代数')
        ax3.set_ylabel('适应度')
        ax3.set_title('遗传-模式搜索算法收敛过程')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
    else:
        ax3.text(0.5, 0.5, '无收敛数据', ha='center', va='center',
                transform=ax3.transAxes, fontsize=14)
        ax3.set_title('算法收敛过程')

    # 4. 模型性能指标雷达图
    metrics = ['RMSE\n(越小越好)', 'MAE\n(越小越好)', '相关系数\n(越大越好)', '目标函数\n(越小越好)']
    values = [
        1 - min(validation['rmse'] / 100, 1),  # 归一化RMSE (反向)
        1 - min(validation['mae'] / 100, 1),   # 归一化MAE (反向)
        max(0, validation['correlation']),      # 相关系数
        1 - min(results['fitness'] / 1000, 1)  # 归一化目标函数值 (反向)
    ]

    # 创建雷达图
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    values += values[:1]  # 闭合图形
    angles += angles[:1]

    ax4.plot(angles, values, 'o-', linewidth=2, color='green', alpha=0.7)
    ax4.fill(angles, values, alpha=0.25, color='green')
    ax4.set_xticks(angles[:-1])
    ax4.set_xticklabels(metrics, fontsize=10)
    ax4.set_ylim(0, 1)
    ax4.set_title('模型综合性能评估')
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # 输出详细结果
    print("\n" + "="*60)
    print("PM2.5污染源溯源分析结果详报")
    print("="*60)
    print(f"估计污染源位置: ({source_x:.1f}, {source_y:.1f}) 米")
    print(f"估计排放强度: {results['emission_rate']:.3f} g/s")
    print(f"目标函数值: {results['fitness']:.6f}")
    print(f"模型验证RMSE: {validation['rmse']:.3f} μg/m³")
    print(f"模型验证MAE: {validation['mae']:.3f} μg/m³")
    print(f"预测相关系数: {validation['correlation']:.3f}")
    print("="*60)

# 示例使用
if __name__ == "__main__":
    # 示例数据 (x_r, y_r, z_r, C_obs, u, theta_deg, stability)
    sample_data = [
        (100, 100, 1.5, 10.0, 2.0, 45, 'D'),
        (200, 150, 1.5, 8.0, 2.2, 50, 'D'),
        (300, 200, 1.5, 6.0, 1.8, 40, 'C'),
        (150, 250, 1.5, 7.5, 2.1, 48, 'D'),
        (250, 300, 1.5, 5.5, 1.9, 42, 'C')
    ]

    # 创建混合模型系统
    hybrid_system = HybridSourceInversion()

    # 运行完整分析
    results = hybrid_system.run_complete_analysis(sample_data, use_ml_enhancement=True)

    # 输出结果
    print(f"\n=== 分析结果 ===")
    print(f"估计源位置: ({results['source_location'][0]:.1f}, {results['source_location'][1]:.1f})")
    print(f"估计排放率: {results['emission_rate']:.3f}")
    print(f"目标函数值: {results['fitness']:.6f}")
    print(f"验证RMSE: {results['validation']['rmse']:.3f}")
    print(f"验证相关系数: {results['validation']['correlation']:.3f}")

    # 可视化结果
    visualize_results(results)