已完成功能

1.可以通过大模型将用户的自然语言处理为sql语句

2.通过插件将上一步生成的sql语句向目标数据库查询并返回结果

3.dify智能体通过上一步查询的结果给出各类建议并生成对应图像（较简单，通过echarts前端渲染）

4.将dify的智能体回答使用markdown转word生成可供用户下载的文档



问题：

1.现有智能体只能根据用户模板修改提示词才能获得需要的回答，比较麻烦

2.由于需要在提示词内声明数据库各个字段及含义，当数据库字段较多时提示词数量明显增长可能影响智能体理解能力

3.由于现在是通过自己开发的插件获取下载链接，下载得到的word文档内容较简单且图像无法直接生成在word内部





需求（按紧急顺序排序，最上方为最迫切的需求）：

1.智能体可以通过sql查询的结果，通过代码等形式生成图像并将其插入到最后的word文档中

2.可以通过某种方式，给定如何解析用户模板，而无需当用户更换不同模板时修改大量提示词