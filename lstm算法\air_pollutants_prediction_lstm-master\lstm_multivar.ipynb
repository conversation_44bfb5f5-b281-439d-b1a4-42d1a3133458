{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "lstm_multivar.ipynb", "provenance": [], "collapsed_sections": [], "toc_visible": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "metadata": {"id": "ukOLd5z9o2NQ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 357}, "executionInfo": {"status": "ok", "timestamp": 1597838430069, "user_tz": -60, "elapsed": 2709, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "a231e112-9d57-48d8-83b4-59dbd9b376de"}, "source": ["  !nvidia-smi"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["Wed Aug 19 12:00:28 2020       \n", "+-----------------------------------------------------------------------------+\n", "| NVIDIA-SMI 450.57       Driver Version: 418.67       CUDA Version: 10.1     |\n", "|-------------------------------+----------------------+----------------------+\n", "| GPU  Name        Persistence-M| Bus-Id        Disp.A | Volatile Uncorr. ECC |\n", "| Fan  Temp  Perf  Pwr:Usage/Cap|         Memory-Usage | GPU-Util  Compute M. |\n", "|                               |                      |               MIG M. |\n", "|===============================+======================+======================|\n", "|   0  Tesla T4            Off  | 00000000:00:04.0 Off |                    0 |\n", "| N/A   63C    P8    10W /  70W |      0MiB / 15079MiB |      0%      Default |\n", "|                               |                      |                 ERR! |\n", "+-------------------------------+----------------------+----------------------+\n", "                                                                               \n", "+-----------------------------------------------------------------------------+\n", "| Processes:                                                                  |\n", "|  GPU   GI   CI        PID   Type   Process name                  GPU Memory |\n", "|        ID   ID                                                   Usage      |\n", "|=============================================================================|\n", "|  No running processes found                                                 |\n", "+-----------------------------------------------------------------------------+\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "AIdpFAYUpDRR", "colab_type": "code", "colab": {}}, "source": ["import pandas as pd\n", "from matplotlib import pyplot as plt\n", "import numpy as np\n", "from sklearn.metrics import mean_squared_error"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "GdwmDyu4pG2O", "colab_type": "text"}, "source": ["# 导入数据"]}, {"cell_type": "code", "metadata": {"id": "Iib6DHLKDN84", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 122}, "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": -60, "elapsed": 21667, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "11fc0527-f2e7-4cdc-bf20-651f2e6a3afe"}, "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["Go to this URL in a browser: https://accounts.google.com/o/oauth2/auth?client_id=************-6bn6qk8qdgf4n4g3pfee6491hc0brc4i.apps.googleusercontent.com&redirect_uri=urn%3aietf%3awg%3aoauth%3a2.0%3aoob&scope=email%20https%3a%2f%2fwww.googleapis.com%2fauth%2fdocs.test%20https%3a%2f%2fwww.googleapis.com%2fauth%2fdrive%20https%3a%2f%2fwww.googleapis.com%2fauth%2fdrive.photos.readonly%20https%3a%2f%2fwww.googleapis.com%2fauth%2fpeopleapi.readonly&response_type=code\n", "\n", "Enter your authorization code:\n", "··········\n", "Mounted at /content/drive\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "E9MpBLKwpIlm", "colab_type": "code", "colab": {}}, "source": ["dataset=pd.read_csv('/content/drive/My Drive/air_inference/data/Bloomsbury_clean.csv')"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "ihnTMkg0pWjr", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 289}, "executionInfo": {"status": "ok", "timestamp": *************, "user_tz": -60, "elapsed": 23176, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "4ff24e9f-03e9-4773-8323-5cce8d0c6dc1"}, "source": ["dataset.head()"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>site</th>\n", "      <th>code</th>\n", "      <th>date</th>\n", "      <th>nox</th>\n", "      <th>no2</th>\n", "      <th>no</th>\n", "      <th>o3</th>\n", "      <th>pm2.5</th>\n", "      <th>ws</th>\n", "      <th>wd</th>\n", "      <th>air_temp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>London Bloomsbury</td>\n", "      <td>CLL2</td>\n", "      <td>2018-01-01 00:00:00</td>\n", "      <td>38.719371</td>\n", "      <td>27.599582</td>\n", "      <td>7.252141</td>\n", "      <td>47.360318</td>\n", "      <td>7.497625</td>\n", "      <td>4.598855</td>\n", "      <td>257.279906</td>\n", "      <td>5.378717</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>London Bloomsbury</td>\n", "      <td>CLL2</td>\n", "      <td>2018-01-01 01:00:00</td>\n", "      <td>38.976582</td>\n", "      <td>27.836512</td>\n", "      <td>7.265368</td>\n", "      <td>47.042127</td>\n", "      <td>7.449653</td>\n", "      <td>4.603798</td>\n", "      <td>257.009139</td>\n", "      <td>5.412134</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>London Bloomsbury</td>\n", "      <td>CLL2</td>\n", "      <td>2018-01-01 02:00:00</td>\n", "      <td>39.251382</td>\n", "      <td>28.072885</td>\n", "      <td>7.290429</td>\n", "      <td>46.715825</td>\n", "      <td>7.416401</td>\n", "      <td>4.621557</td>\n", "      <td>256.762603</td>\n", "      <td>5.453970</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>London Bloomsbury</td>\n", "      <td>CLL2</td>\n", "      <td>2018-01-01 03:00:00</td>\n", "      <td>37.985254</td>\n", "      <td>27.997451</td>\n", "      <td>6.513879</td>\n", "      <td>46.400863</td>\n", "      <td>7.358787</td>\n", "      <td>4.636919</td>\n", "      <td>256.538550</td>\n", "      <td>5.502388</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>London Bloomsbury</td>\n", "      <td>CLL2</td>\n", "      <td>2018-01-01 04:00:00</td>\n", "      <td>38.973919</td>\n", "      <td>28.512513</td>\n", "      <td>6.822754</td>\n", "      <td>46.033610</td>\n", "      <td>7.302818</td>\n", "      <td>4.658491</td>\n", "      <td>256.342472</td>\n", "      <td>5.554477</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                site  code                 date  ...        ws          wd  air_temp\n", "0  London Bloomsbury  CLL2  2018-01-01 00:00:00  ...  4.598855  257.279906  5.378717\n", "1  London Bloomsbury  CLL2  2018-01-01 01:00:00  ...  4.603798  257.009139  5.412134\n", "2  London Bloomsbury  CLL2  2018-01-01 02:00:00  ...  4.621557  256.762603  5.453970\n", "3  London Bloomsbury  CLL2  2018-01-01 03:00:00  ...  4.636919  256.538550  5.502388\n", "4  London Bloomsbury  CLL2  2018-01-01 04:00:00  ...  4.658491  256.342472  5.554477\n", "\n", "[5 rows x 11 columns]"]}, "metadata": {"tags": []}, "execution_count": 6}]}, {"cell_type": "markdown", "metadata": {"id": "qsw13S-QuS9t", "colab_type": "text"}, "source": ["# 多变量进行预测"]}, {"cell_type": "code", "metadata": {"id": "3zTUeXsCuZ3V", "colab_type": "code", "colab": {}}, "source": ["var_origin=dataset[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']].values"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "hw0hFQeWup8R", "colab_type": "text"}, "source": ["数据进行归一化操作"]}, {"cell_type": "code", "metadata": {"id": "T9bUOj3purhd", "colab_type": "code", "colab": {}}, "source": ["from sklearn.preprocessing import MinMaxScaler\n", "scaler = MinMaxScaler(feature_range=(-1, 1))\n", "scaled = scaler.fit_transform(var_origin)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "k0vvmm5puuTJ", "colab_type": "text"}, "source": ["将数据转为cuda类型"]}, {"cell_type": "code", "metadata": {"id": "5b-DE_7huwo4", "colab_type": "code", "colab": {}}, "source": ["import torch\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "var= torch.FloatTensor(scaled).to(device)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "vAm8BF6Nu3eI", "colab_type": "text"}, "source": ["划分训练集，验证集和测试集"]}, {"cell_type": "code", "metadata": {"id": "S5Z9FaVUu7ji", "colab_type": "code", "colab": {}}, "source": ["def splitData(var,per_val,per_test):\n", "    num_val=int(len(var)*per_val)\n", "    num_test=int(len(var)*per_test)\n", "    train_size=int(len(var)-num_val-num_test)\n", "    train_data=var[0:train_size]\n", "    val_data=var[train_size:train_size+num_val]\n", "    test_data=var[train_size+num_val:train_size+num_val+num_test]\n", "    return train_data,val_data,test_data"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "hAbQ9M0pu-6W", "colab_type": "text"}, "source": ["我们的验证集合测试集都取10%"]}, {"cell_type": "code", "metadata": {"id": "12s54oyKu_qb", "colab_type": "code", "colab": {}}, "source": ["train_data,val_data,test_data=splitData(var,0.1,0.1)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "B9MMKedCvBcQ", "colab_type": "text"}, "source": ["查看长度"]}, {"cell_type": "code", "metadata": {"id": "3CL_EC11vDhf", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1597838464185, "user_tz": -60, "elapsed": 36789, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "369ecbda-ff57-4f06-b66d-03ee7ed1866f"}, "source": ["print('The length of train data, validation data and test data are:',len(train_data),',',len(val_data),',',len(test_data))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The length of train data, validation data and test data are: 14016 , 1752 , 1752\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "xM8DYu0RvGSt", "colab_type": "text"}, "source": ["\n", "取一定大小的窗口进行滑动，每个窗口的label值是窗口下一个预测的第一个空气污染物的值"]}, {"cell_type": "code", "metadata": {"id": "Neov5unqwMkx", "colab_type": "code", "colab": {}}, "source": ["train_window = 240\n", "def create_train_sequence(input_data, tw):\n", "    inout_seq = []\n", "    L = len(input_data)\n", "    for i in range(L-tw):\n", "        train_seq = input_data[i:i+tw]\n", "        train_label = input_data[i+tw:i+tw+1]\n", "        inout_seq.append((train_seq ,train_label))\n", "    return inout_seq"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "Nq2VNCQZwQNb", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1597838464186, "user_tz": -60, "elapsed": 36781, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "8b9f42dc-fcd5-4692-c6e4-d6b7b978a187"}, "source": ["train_inout_seq = create_train_sequence(train_data, train_window)\n", "print('The total number of train windows is',len(train_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of train windows is 13776\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "SRKEVERCwOQs", "colab_type": "text"}, "source": ["注意，与上面创建train_data的sequence不同，验证集数据(实验是96个验证集数据)只是label。其数据部分还是需要借助于train集中的数据，大小为一个窗口。而这一个窗口的数据并不会在训练过程中被使用"]}, {"cell_type": "code", "metadata": {"id": "pYIJhKWkwWSW", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1597838464186, "user_tz": -60, "elapsed": 36773, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "51bc5900-1cb4-47f2-9f9d-d45cd306f5e8"}, "source": ["def create_val_sequence(train_data,val_data, tw):\n", "    temp=torch.cat((train_data,val_data))   #先将训练集和测试集合并\n", "    inout_seq = []\n", "    L = len(val_data)\n", "    for i in range(L):\n", "        val_seq = temp[-(train_window+L)+i:-L+i]\n", "        val_label = test_data[i:i+1]\n", "        inout_seq.append((val_seq ,val_label))\n", "\n", "    return inout_seq\n", "\n", "val_inout_seq = create_val_sequence(train_data, val_data,train_window)\n", "print('The total number of validation windows is',len(val_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of validation windows is 1752\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "9Rr2_qTvTKkm", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1597838464187, "user_tz": -60, "elapsed": 36765, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "1064d261-6841-4c72-d465-65d0a52ef230"}, "source": ["def create_test_sequence(train_data,val_data,test_data, tw):\n", "    temp=torch.cat((train_data,val_data))   #先将训练集和测试集合并\n", "    temp=torch.cat((temp,test_data))\n", "    inout_seq = []\n", "    L = len(test_data)\n", "    for i in range(L):\n", "        test_seq = temp[-(train_window+L)+i:-L+i]\n", "        test_label = test_data[i:i+1]\n", "        inout_seq.append((test_seq ,test_label))\n", "\n", "    return inout_seq\n", "\n", "test_inout_seq = create_test_sequence(train_data, val_data, test_data,train_window)\n", "print('The total number of validation windows is',len(val_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of validation windows is 1752\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "C-1_1zW9wdih", "colab_type": "text"}, "source": ["# 定义LSTM"]}, {"cell_type": "markdown", "metadata": {"id": "sFYIJDyqwhH5", "colab_type": "text"}, "source": ["与单变量lstm不同的是，这次的数据的维度为8维，而单变量只有1维"]}, {"cell_type": "code", "metadata": {"id": "zF00gWiSwgqg", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1597838464187, "user_tz": -60, "elapsed": 36758, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "a523cc86-a958-4568-d8a0-a95e6ec6416f"}, "source": ["train_data.shape"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([14016, 8])"]}, "metadata": {"tags": []}, "execution_count": 17}]}, {"cell_type": "code", "metadata": {"id": "hyM64rs-yieR", "colab_type": "code", "colab": {}}, "source": ["from torch import nn\n", "import torch.nn.init as init\n", "import torch.nn.functional as F\n", "class LSTM(nn.Module):\n", "    def __init__(self,input_size=8,hidden_layer_size=50,output_size=1,num_layers=2):\n", "        super().__init__()\n", "        self.hidden_layer_size=hidden_layer_size\n", "        self.lstm=nn.LSTM(input_size,hidden_layer_size,num_layers)\n", "        self.linear1=nn.Linear(hidden_layer_size,hidden_layer_size)\n", "        self.linear2=nn.Linear(hidden_layer_size,output_size)\n", "        self.hidden_cell=(torch.zeros(num_layers,1,self.hidden_layer_size),torch.zeros(num_layers,1,self.hidden_layer_size))\n", "        init_rnn(self.lstm,'xavier')\n", "        \n", "    def forward(self,input_seq):\n", "        lstm_out, self.hidden_cell = self.lstm(input_seq.reshape(len(input_seq),1,8), self.hidden_cell)\n", "        out=self.linear1(lstm_out.view(len(input_seq), -1))\n", "        out=torch.tanh(out)\n", "        predictions = self.linear2(out)\n", "        return predictions[-1]\n", "\n", "\n", "#设定初始化\n", "def init_rnn(x, type='uniform'):\n", "    for layer in x._all_weights:\n", "        for w in layer:\n", "            if 'weight' in w:\n", "                if type == 'xavier':\n", "                    init.xavier_normal_(getattr(x, w))\n", "                elif type == 'uniform':\n", "                    stdv = 1.0 / math.sqrt(x.hidden_size)\n", "                    init.uniform_(getattr(x, w), -stdv, stdv)\n", "                elif type == 'normal':\n", "                    stdv = 1.0 / math.sqrt(x.hidden_size)\n", "                    init.normal_(getattr(x, w), .0, stdv)\n", "                else:\n", "                    raise ValueError\n"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "Y4wFgdDb3yRu", "colab_type": "text"}, "source": ["# 训练模型"]}, {"cell_type": "markdown", "metadata": {"id": "khFmQ2awIfht", "colab_type": "text"}, "source": ["在训练模型我们需要了解到，该实验是变量预测单变量，根据单变量数量进行多次预测。。。比方说，我们现在有8个attributes,用来预测1个attribute(比如nox)。。。刚开始我是准备8个attributes预测8个attributes，但是实验结果真的是太差了，还不如单变量预测单变量。。。"]}, {"cell_type": "code", "metadata": {"id": "MNLpUCgxTa9e", "colab_type": "code", "colab": {}}, "source": ["import copy\n", "\n", "epochs=5\n", "\n", "\n", "#为了实现多变量预测多个单变量，我这里用了五个LSTM模型\n", "model_nox=LSTM().to(device)\n", "model_no2=LSTM().to(device)\n", "model_no=LSTM().to(device)\n", "model_o3=LSTM().to(device)\n", "model_pm25=LSTM().to(device)\n", "\n", "loss_function=nn.MSELoss()\n", "optimizer_nox = torch.optim.SGD(model_nox.parameters(), lr=0.003,momentum=0.4, weight_decay=6e-4)\n", "optimizer_no2 = torch.optim.SGD(model_no2.parameters(), lr=0.003,momentum=0.2, weight_decay=6e-4)\n", "optimizer_no = torch.optim.SGD(model_no.parameters(), lr=0.002,momentum=0.2, weight_decay=6e-4)\n", "optimizer_o3 = torch.optim.SGD(model_o3.parameters(), lr=0.002,momentum=0.2, weight_decay=6e-4)\n", "optimizer_pm25 = torch.optim.SGD(model_pm25.parameters(), lr=0.001,momentum=0.2, weight_decay=6e-4)\n", "\n", "attr_dic={\n", "    'nox':model_nox,\n", "    'no2':model_no2,\n", "    'no':model_no,\n", "    'o3':model_o3,\n", "    'pm2.5':model_pm25\n", "}\n", "\n", "index_dic={\n", "    'nox':0,\n", "    'no2':1,\n", "    'no':2,\n", "    'o3':3,\n", "    'pm2.5':4\n", "    \n", "}\n", "\n", "optimizer_dic={\n", "    'nox':optimizer_nox,\n", "    'no2':optimizer_no2,\n", "    'no':optimizer_no,\n", "    'o3':optimizer_o3,\n", "    'pm2.5':optimizer_pm25\n", "}\n", "\n", "loss_train_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "loss_val_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "value_train_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "value_val_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "yNE6Evl4i_40", "colab_type": "code", "colab": {}}, "source": ["def train_model(attr,model):\n", "  model.train()\n", "  print('训练',attr,'模型')\n", "  for i in range(epochs):\n", "    #train\n", "    add=0\n", "    for seq,label in train_inout_seq:   \n", "        optimizer_dic[attr].zero_grad()\n", "        seq=seq.to(device)\n", "        label=label.to(device)\n", "        model.hidden_cell = (torch.zeros(2, 1, model.hidden_layer_size).to(device),torch.zeros(2, 1, model.hidden_layer_size).to(device))\n", "        y_pred = model(seq)\n", "\n", "        if(i==epochs-1):  #对最后一次epoch的值进行记录\n", "          value_train_dic[attr].append(y_pred)\n", "\n", "        single_loss = loss_function(y_pred[0], label[0,index_dic[attr]])   #这里只预测label[i]的数值，即某个单一的空气污染物\n", "        add+=single_loss\n", "        single_loss .backward()\n", "        optimizer_dic[attr].step()\n", "    loss_train=add/len(train_inout_seq)\n", "    loss_train_dic[attr].append(loss_train)\n", "\n", "\n", "    #val\n", "    add=0 \n", "    t=0\n", "\n", "    val_inputs=train_data[-train_window:]\n", "    fut_pred = len(val_data)\n", "\n", "    for seq,label in val_inout_seq:\n", "      with torch.no_grad():\n", "        seq = val_inputs[-train_window:].to(device)\n", "        label=label.to(device)\n", "        y_pred=model(seq)\n", "        single_loss=loss_function(y_pred[0],label[0,index_dic[attr]])  \n", "\n", "        add+=single_loss\n", "\n", "        if(i==epochs):  #对最后一次epoch的值进行记录\n", "          value_val_dic[attr].append(y_pred)\n", "\n", "        #这一步可能比较模糊。首先我们要明确一点的是，y_pred是一个常数。但是我们的val的是nx8维的数。也就是一个\n", "        #数据就有8维。我们对val_inputs的更新实际上是应该更新8维的数。可因为我们是多变量预测单变量，我们每次只是预测一个值\n", "        #所以，解决方法就是，我先把未来的验证集里的8个数添加进去。然后用得出来的值取覆盖相应污染物的值\n", "        temp=copy.deepcopy(val_data[t])\n", "        temp[index_dic[attr]]=y_pred\n", "        temp=temp.view(1,-1)\n", "        \n", "        val_inputs=torch.cat((val_inputs,temp),0)\n", "        t+=0\n", "\n", "    loss_val=add/len(val_inout_seq)\n", "    loss_val_dic[attr].append(loss_val)\n", "\n", "    print(f'epoch: {i:3}  train_loss:{loss_train:10.8f} val_loss:{loss_val:10.8f}')\n", "  print('----------------------')"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "bTXI3MxItlVd", "colab_type": "code", "colab": {}}, "source": ["from sklearn.metrics import mean_squared_error\n", "from sklearn.metrics import mean_absolute_error\n", "\n", "def test_model(attr,model):\n", "  temp=torch.cat((train_data,val_data))\n", "  test_inputs=temp[-train_window:,:]\n", "\n", "  fut_pred = len(test_data)\n", "  test_list=[]\n", "  test_results=copy.deepcopy(test_data)\n", "\n", "  model.eval()\n", "\n", "  for i in range(fut_pred):\n", "      seq = test_inputs[-train_window:].to(device)\n", "      with torch.no_grad():\n", "          model.hidden = (torch.zeros(1, 1, model.hidden_layer_size),\n", "                          torch.zeros(1, 1, model.hidden_layer_size))\n", "          y_pred=model(seq)\n", "          temp=copy.deepcopy(test_data[i]).view(1,-1)\n", "          # temp[index_dic[attr]]=y_pred\n", "          # temp=temp.view(1,-1)\n", "          test_inputs=torch.cat((test_inputs,temp),0)\n", "          test_results[i]=y_pred\n", "\n", "\n", "\n", "\n", "  actual_predictions = scaler.inverse_transform(np.array(test_results.cpu()))\n", "\n", "\n", "\n", "\n", "  x = np.arange(len(train_data)+len(val_data), len(dataset), 1)\n", "  plt.figure(figsize=(8, 6))\n", "  plt.grid(True)\n", "\n", "  plt.plot(dataset.loc[len(dataset)-len(test_data):,attr].values,color=\"red\",label='real value')\n", "  plt.plot(actual_predictions[:,index_dic[attr]],label='prediction')\n", "\n", "  plt.title('hours vs '+attr)\n", "  plt.ylabel(attr)\n", "  plt.xlabel('hour')\n", "\n", "  plt.legend(loc='upper right',fontsize=15)\n", "\n", "  y_true=dataset.loc[len(dataset)-len(test_data):,attr].values\n", "  y_pred=actual_predictions[:,index_dic[attr]]\n", "  print('mse: ',mean_squared_error(y_true, y_pred))\n", "  print('mae: ',mean_absolute_error(y_true, y_pred))"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "dcEij6uAMKg5", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 136}, "executionInfo": {"status": "ok", "timestamp": 1597839522605, "user_tz": -60, "elapsed": 1095154, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "70d4f096-9e0d-498d-b893-a054845a8d69"}, "source": ["train_model('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 nox 模型\n", "epoch:   0  train_loss:0.01505147 val_loss:0.20785004\n", "epoch:   1  train_loss:0.00215626 val_loss:0.21019857\n", "epoch:   2  train_loss:0.00162173 val_loss:0.21215442\n", "epoch:   3  train_loss:0.00141838 val_loss:0.21289381\n", "epoch:   4  train_loss:0.00130570 val_loss:0.21319278\n", "----------------------\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "RRsvxtbiuDaF", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1597839534750, "user_tz": -60, "elapsed": 1107292, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "b2266961-e691-4a09-f53d-49b571e90294"}, "source": ["test_model('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  102.33592852175462\n", "mae:  8.919999674260687\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "gvn89ILdjgYX", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 136}, "executionInfo": {"status": "ok", "timestamp": 1597840589877, "user_tz": -60, "elapsed": 2162411, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "d8c3c0f5-b381-423c-f5ac-4ed7839fb16c"}, "source": ["train_model('no2',model_no2)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 no2 模型\n", "epoch:   0  train_loss:0.02225758 val_loss:0.30786943\n", "epoch:   1  train_loss:0.00372342 val_loss:0.30929700\n", "epoch:   2  train_loss:0.00253465 val_loss:0.31320465\n", "epoch:   3  train_loss:0.00203136 val_loss:0.31487009\n", "epoch:   4  train_loss:0.00174919 val_loss:0.31552061\n", "----------------------\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "uiobF6CHvCYW", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1597840602275, "user_tz": -60, "elapsed": 2174802, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "9204cf19-cae8-4283-8a81-e3dbd445a38e"}, "source": ["test_model('no2',model_no2)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  14.762185802061731\n", "mae:  3.216721847516672\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "5yEx1bQSLxJu", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 136}, "executionInfo": {"status": "ok", "timestamp": 1597841658933, "user_tz": -60, "elapsed": 19069, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "9fe5667c-983b-46f3-8e89-3c90f5392151"}, "source": ["train_model('no',model_no)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 no 模型\n", "epoch:   0  train_loss:0.03165650 val_loss:0.12305069\n", "epoch:   1  train_loss:0.00754218 val_loss:0.12968919\n", "epoch:   2  train_loss:0.00362852 val_loss:0.12979338\n", "epoch:   3  train_loss:0.00271704 val_loss:0.12947145\n", "epoch:   4  train_loss:0.00226472 val_loss:0.12902597\n", "----------------------\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "7xIGo5JwP5eQ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1597841671414, "user_tz": -60, "elapsed": 12485, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "c844d7bc-a48b-4107-c86c-73364a1b02ad"}, "source": ["test_model('no',model_no)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  10.579146126468272\n", "mae:  2.596288950282603\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "x2tnzs_tQBu1", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 136}, "executionInfo": {"status": "ok", "timestamp": 1597842732539, "user_tz": -60, "elapsed": 1061130, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "3f7911a2-7fa9-4d02-fbd6-2e2a4337373a"}, "source": ["train_model('o3',model_o3)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 o3 模型\n", "epoch:   0  train_loss:0.03706445 val_loss:0.37640348\n", "epoch:   1  train_loss:0.00834059 val_loss:0.33454713\n", "epoch:   2  train_loss:0.00303071 val_loss:0.32829842\n", "epoch:   3  train_loss:0.00209323 val_loss:0.32490218\n", "epoch:   4  train_loss:0.00181653 val_loss:0.32128149\n", "----------------------\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "13s3RdWuULJ3", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1597842745044, "user_tz": -60, "elapsed": 12518, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "aa38ac23-82b3-40b3-e198-9c2b060ab8a7"}, "source": ["test_model('o3',model_o3)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  9.189415168539666\n", "mae:  2.4461444079681196\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "4gC2bva0UT_m", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 136}, "executionInfo": {"status": "ok", "timestamp": 1597843813850, "user_tz": -60, "elapsed": 1068810, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "f96be75a-dc45-45e3-c95e-dfde748bba96"}, "source": ["train_model('pm2.5',model_pm25)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 pm2.5 模型\n", "epoch:   0  train_loss:0.06699225 val_loss:0.08744960\n", "epoch:   1  train_loss:0.01624161 val_loss:0.09419649\n", "epoch:   2  train_loss:0.00590276 val_loss:0.09186187\n", "epoch:   3  train_loss:0.00280317 val_loss:0.09209376\n", "epoch:   4  train_loss:0.00192104 val_loss:0.09281474\n", "----------------------\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "lf7IELqHYaEJ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1597843826132, "user_tz": -60, "elapsed": 12294, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "61034ca1-bfc8-40ec-dbc1-2d6cd279d820"}, "source": ["test_model('pm2.5',model_pm25)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  1.206711345164902\n", "mae:  0.8920448894808445\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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************************************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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "YsEqlGK5L-m-", "colab_type": "code", "colab": {}}, "source": ["def predict_future(attr,model):\n", "  temp=torch.cat((train_data,val_data))\n", "  test_inputs=temp[-train_window:,:]\n", "\n", "  fut_pred = 96\n", "  test_list=[]\n", "  test_results=copy.deepcopy(test_data)\n", "\n", "  model.eval()\n", "\n", "  for i in range(fut_pred):\n", "      seq = test_inputs[-train_window:].to(device)\n", "      with torch.no_grad():\n", "          model.hidden = (torch.zeros(1, 1, model.hidden_layer_size),\n", "                          torch.zeros(1, 1, model.hidden_layer_size))\n", "          y_pred=model(seq)\n", "          temp=copy.deepcopy(test_data[i])\n", "          temp[index_dic[attr]]=y_pred\n", "          temp=temp.view(1,-1)\n", "          test_inputs=torch.cat((test_inputs,temp),0)\n", "          test_results[i]=y_pred\n", "\n", "\n", "\n", "\n", "  actual_predictions = scaler.inverse_transform(np.array(test_results.cpu()))\n", "\n", "\n", "\n", "\n", "\n", "  plt.figure(figsize=(8, 6))\n", "  plt.grid(True)\n", "\n", "  plt.plot(dataset.loc[len(dataset)-len(test_data):len(dataset)-len(test_data)+fut_pred,attr].values,color=\"red\",label='real value')\n", "  plt.plot(actual_predictions[:fut_pred,index_dic[attr]],label='prediction')\n", "\n", "  plt.title('hours vs '+attr)\n", "  plt.ylabel(attr)\n", "  plt.xlabel('hour')\n", "\n", "  plt.legend(loc='upper right',fontsize=15)\n", "\n", "  y_true=dataset.loc[len(dataset)-len(test_data):len(dataset)-len(test_data)+fut_pred-1,attr].values\n", "  y_pred=actual_predictions[:fut_pred,index_dic[attr]]\n", "\n", "  print('mse: ',mean_squared_error(y_true, y_pred))\n", "  print('mae: ',mean_absolute_error(y_true, y_pred))\n", "\n", "  y_pred=pd.DataFrame(y_pred)\n", "\n", "  y_pred.to_csv('/content/drive/My Drive/air_inference/result24/lstm_mul_'+attr+'.csv',index=False)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "q5pGb9U6MqBP", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1597843828376, "user_tz": -60, "elapsed": 2248, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "84773c0b-a6f9-44e3-c018-bdbd1017a724"}, "source": ["predict_future('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  375.5894603366774\n", "mae:  16.82928464501776\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "S4keIj6WTbjE", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1597843829707, "user_tz": -60, "elapsed": 1335, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "d03dbe12-6a0e-418b-eb98-67328cb76a6d"}, "source": ["predict_future('no2',model_no2)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  28.524309508968496\n", "mae:  4.828858913661392\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "ryiCRVAvUNUL", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1597843831342, "user_tz": -60, "elapsed": 1639, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "8d6f2d6c-baed-418d-d705-4d7e2e8de1de"}, "source": ["predict_future('no',model_no)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  77.84997642389003\n", "mae:  7.308888088674728\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "JKBcRyOgUjVr", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1597843832440, "user_tz": -60, "elapsed": 1102, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "5d6c6328-6520-4296-8021-d2999d364501"}, "source": ["predict_future('o3',model_o3)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  173.43947997041974\n", "mae:  12.50396776643863\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "FvzUB1x2UnkT", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1597843833507, "user_tz": -60, "elapsed": 1072, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "6f9584b2-5608-4a86-91c1-52c6141a3083"}, "source": ["predict_future('pm2.5',model_pm25)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  6.430434766603521\n", "mae:  2.117404218875743\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAfQAAAGDCAYAAADd8eLzAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAgAElEQVR4nOzdd3RU1d7G8e8G0hMCgST03nu1gdJEBBUbIig2vCr3RUVBsaGIBREUEcSCiGBBigqo2NFYLpYLCgLSWyiBEAghvc1+/ziBC0gJZFqS57NWFpmZM2f/5hjzZJ+zz97GWouIiIgUb2V8XYCIiIgUnQJdRESkBFCgi4iIlAAKdBERkRJAgS4iIlICKNBFRERKAAW6iBcYY7YZYy72dR0iUnIp0EXEbxhjLjPG/GyMOWiM2WOMmW6MiTjF9tuMMZnGmLSCr6+9Wa+IP1Ggi5Qgxphyvq6hiCKBZ4BqQFOgOjDhNO+5wlobXvB1iacLFPFXCnQR72ljjPnLGJNijJlrjAk+/IIx5g5jzCZjzAFjzCfGmGoFz9cxxtijg9oYE2eM+VfB97caY/5jjHnJGLMfeNIY08AY80NBO0nGmLknKsYY84Ux5u7jnltpjLnGOF4yxiQaYw4ZY1YZY1qcZD9xxpjnjDG/F2y7yBgTdVz9txljdhhjko0xQ4wxHQuOxUFjzCuH92WtnW2t/dJam2GtTQbeBDqd9REXKUUU6CLe0x+4FKgLtAJuBTDGdAeeK3i9KrAdmHMG+z0X2ALEAs8CTwNfAxWBGsCUk7zvA2Dg4QfGmGZAbWAxcAlwEdAIp9fcH9h/ihpuBgYX1J8HTD5BjQ2B64FJwGPAxUBzoL8xpstJ9nsRsOYU7QK8b4zZZ4z52hjT+jTbipRYCnQR75lsrd1trT0AfAq0KXj+RmCGtfYPa2028AhwvjGmTiH3u9taO8Vam2etzQRycYK5mrU2y1r780netwDnrEHto+r4uKCGXCACaAIYa+1aa23CKWp411q72lqbDjyOE9Jlj3r96YJavgbSgQ+stYnW2l3AT0Db43dojOkJ3AI8cYp2bwTqFHze74GvjDEVTrG9SImlQBfxnj1HfZ8BhBd8Xw2nVw6AtTYNpzdcvZD73XHc45GAAX43xqwxxgw+0Zustak4vfEBBU8NBN4veO074BVgKpBojJlmjClfyBq2AwFA5aOe23vU95kneBx+1GOMMecBs4F+1toNJ2vUWvsfa21mwSn654CDwIWnqFOkxFKgi/jebpweJgDGmDCgErALpzcLEHrU9lWOe/8xSyZaa/dYa++w1lYD7gJeNcY0OEnbHwADjTHnA8E4vdzD+5lsrW0PNMM59f7gKT5DzaO+r4XTw086xfYnZYxpC3wCDLbWLjnDt1ucP2ZESh0FuojvfQDcZoxpY4wJAsYCv1lrt1lr9+EE+yBjTNmC3nb9U+3MGHOdMaZGwcNknJBznWTzz3H+mHgKmGutdRXso6Mx5lxjTADOHxVZp9gHBfU1M8aEFuzrQ2ttfiE++/G1twC+BO6x1n56mm1rGWM6GWMCjTHBxpgHcc4K/OdM2xUpCRToIj5mrf0W57rzR0ACTmAPOGqTO3B6x/txBpEtPc0uOwK/GWPScHq6w6y1W07SdjbwMc4AtdlHvVQeZ4R5Ms4p9P2c+vaxd4GZOJcVgoF7T1PjyYwAooG3jrq3/MigOGPM68aY1wseRgCvFdS4C2fAYW9r7akG74mUWMZae/qtREROwhgTB7xnrZ3u61pESjP10EVEREoABbqIiEgJoFPuIiIiJYB66CIiIiWAAl1ERKQEKBYrM1WuXNnWqVPHbftLT08nLCzMbfsTHVN30/F0Px1T99LxdL+jj+ny5cuTrLXRZ/L+YhHoderUYdmyZW7bX1xcHF27dnXb/kTH1N10PN1Px9S9dDzd7+hjaozZfuqt/0mn3EVEREoABbqIiEgJoEAXEREpARToIiIiJYACXUREpAQoFqPcRURKmkOHDpGYmEhubq6vSzkrkZGRrF271tdlFEsBAQHExMRQvnx5t+5XgS4i4mWHDh1i7969VK9enZCQEIwxvi7pjKWmphIREeHrMooday2ZmZns2rULwK2hrlPuIiJelpiYSPXq1QkNDS2WYS5nzxhDaGgo1atXJzEx0a37VqCLiHhZbm4uISEhvi5DfCgkJMTtl1sU6CIiPqCeeenmif/+CnQREZESQIEuIiJyBtKz88jOzfd1Gf+gQBcREb8TFxeHMYbVq1d7vK1t27ZhjOGzzz475XZ5+S52JmeweV8ae1OzPV7XmdJtayIiIqdgrSU5I4c9KVnkuyA6IoiYiGBfl/UPCnQRESmy/Px88vPzCQwM9HUpbpWVm8/O5EwycvIICyxHtYohhASU9XVZJ6RT7iIicsaGDBlChw4dWLhwIc2bNyc4OJjffvsNgEWLFtGhQweCg4OpUqUKI0eOPOYWrXXr1jFgwABq1qxJaGgozZs3Z9KkSbhcrkK3n56eTlhYGFOnTv3Hax07dmTQoEEAJCQkMHjwYOrVq0dISAiNGjVi1KhR5OTknHL/xhjGvvASGxPTyMnLp0bFUN6ZOoGaVWOP2S4+Pp4BAwYQFRVFaGgovXr1Yv369YX+HO6kHrqIiJyVbdu2MXLkSJ544gmqVKlC3bp1mTdvHgMHDuSuu+5i7NixbN68mUceeQSXy8ULL7wAwK5du2jcuDE33ngjERERrFixgtGjR5OZmckjjzxSqLbDwsK4/PLLmTdvHkOHDj3y/JYtW1i2bBmjR48GICkpiaioKCZOnEjFihXZsGEDTz75JPv27eONN9444b4zcvIASM3KJTIkgGqRwZQrW+Yft5odOHCAzp07U6lSJV5//XVCQ0MZN24cF198MRs2bPD6XAMKdBERf3DffbBihW/abtMGJk0647ft37+fb7/9ljZt2gDOteYHH3yQm2++mVdfffXIdkFBQQwdOpRHHnmESpUq0aNHD3r06HHkPZ07dyYjI4M333yz0IEOMGDAAPr168fu3bupVq0aAHPnzqVixYr06tULgJYtWx75QwKgU6dOhIWFMXjwYKZMmXLMJQKXy5KQkklSwYC3SqFB1IoKPWn7L730Eunp6axYsYKoqKgj+69Tpw4zZsw45g8Nb9ApdxEROSvVq1c/EuYAGzZsID4+nv79+5OXl3fkq3v37mRlZR0ZsZ6VlcXo0aNp0KABQUFBBAQE8Nhjj7F161by8vIK3X7v3r0JDw9n/vz5R56bO3cuV199NQEBAYDzB8OkSZNo1qwZISEhBAQEcOONN5KdnU18fPwx+0tIyWRfajYVw5yQDw489bXyb7/9lp49e1K+fPkjnzUiIoL27duzbNmyQn8Od1EPXUTEH5xFD9nXYmOPvZ6clJQEQJ8+fU64/Y4dOwB46KGHmD59OqNHj6Zdu3ZUqFCBRYsW8cwzz5CVlUV4eHih2g8ODubKK69k7ty5DBs2jPXr17Ny5UomTJhwZJtJkybx4IMP8tBDD9GlSxcqVqzIf//7X4YOHUpWVhYua9lX0CN3WahTOYzywQGFaj8pKYlff/2VuXPn/uO1w2cgvEmBLiIiZ+X4a8qHTztPmzaNtm3b/mP7unXrAjB//nzuueceRo4ceeS1xYsXn1UN119/PVdccQXx8fHMnTuX6OhounfvfuT1+fPn069fP5599tkjz/39998A5OTms2VfGklpTqBXiww+EuZBQUH/GDiXnJz8j8/bt29fHn/88X/U5YuV6BToIiLiFo0bN6Z69eps27aNO+6446TbZWZmEhQUdORxfn4+c+bMOas2L7nkEipUqMC8efOYO3cu/fr1o2zZ/50qP74tgPfffx+A7QcyaBTronoF557ysmX/dxW6Ro0ax6z37nK5WLJkyTH76dGjB/PmzaN58+Z+sdiOAl1ERNyiTJkyvPjii9x0000cOnSI3r17ExgYyJYtW1i4cCEffvghoaGh9OzZk6lTp9KgQQOioqKYOnUq2dlnN/NaQEAA11xzDRMnTiQhIeGYwXgAPXv2ZPLkyZx77rnUr1+fd997j3UbNgIQHFCWhjER7M5J/sd+r776aqZOnUrbtm2pV68e06dP59ChQ8dsM3z4cN577z26d+/OPffcQ/Xq1dm7dy8//PADnTt3ZuDAgWf1mc6WAl1ERNzm+uuvp3z58owdO5YZM2ZQtmxZ6tWrx+WXX35kRPmUKVMYMmQIQ4cOJSQkhFtuuYWrr76aO++886zaHDBgAG+99RbVqlXjwgsvPOa1J554gn379jFq1CgAul96OSOfHMc9tw2gRsUQAsudeGz46NGjSUxMZNSoUQQGBnL33XfTvHnzY+57r1y5Mr/++iuPPfYY999/PwcPHqRq1ap07tyZVq1andVnKQpjrfV6o2eqQ4cO1p0jBuPi4ujatavb9ic6pu6m4+l+/nRM165dS9OmTX1dRpGkpqb65Drx2bDWsj89h4SULALKGGpFhRIa5Pv+7PE/B0f/jBpjlltrO5zJ/nz/iURERArjcAf0DNYSz3e52JmcSUpmLuWDA6hRMYRyZUvmHdsKdBER8S1rIS8PDk8PW6aME9rGQFYWpKX978vlgoAACAx0/g0OhooVITT0H0GflZvPtv3p5OZZqkYGUzk86B8j80sSBbqIiHiXtbB/PyQnQ3Y25OQ4QX0qISFQqRKUK+cEf06OE/YpKbBnjxPsUVHOV1AQKZm57EjOpEwZQ73oMML84BS7p5X8TygiIv7B5YIDByAhwQnyoCAnqCMjnR734WlYXS4n9F0uZ5uwMCfITyQvz/nD4MAB2L0bu3s3eyNjSAwpT6ix1I4MJuA0M76VFAp0ERHxrMM98t27nZ51aCg0aOAEeVFPgZcrB9HREB1NfnY2O/anc8hVhqisNKod3EuZBOv8UVChgnNqPiys6G36KQW6iIh4hrVw8CDs2uWcHg8NhVq13BPkx8nJy2fbwRyyXWWpXiGYqLBITHZlOHTIqSExEfbuda67h4U5/x7+CgkpEUGvQBcREfdLS4MdOyA93bm+Xb++00v2QGhmZOexbX8GFkvdyqGEH56LPTjY+YqJcU7Np6Q44X54oN3RC8EEBUHlys51+qNWYCtOFOgiIuJe+/bB9u1O77d2bScoPdT7PZiRw87kTMqVNdSpFE5wwEmul5cr54R1pUr/e87lckI9NRWSkpwzCbt2Qfnyzun5ChWcz1BMKNBFRMQ9rHUCcc8e57R6vXpQ1jMD0qy17EvLZk9KFmGB5ahdKfTM7y8vU8bpjR8O+uxsJ9gPHHD+INm+3TkVX7Gi80fJyQbm+YmSeXe9iIh4l8sFW7Y4YR4d7Qx6c3OYv/LKKxhjsNaSkJLFp19+S+uaFUnfs7XQYT5t2jQWLlz4j+fr1KnDA489BtWrQ4sW0KwZVKvm/JGycyesXu2cefDj2VX9+88NERHxf3l5sGmTc126Rg2IjfXoALP4AxmkZObS+dyOLF26lIYNGxT6vdOmTaNFixZcddVVxzy/YMECKh0+HW+MM4AvNNQJ9YwMiI93euz79jkD+wq5Zrs3KdBFROTs5ebCxo2QmemcYi9YE/1EMjMzi7TMaH5B7zglM5eqkSFER1SAWrFnvb+jnWj99iNCQ6FxY+dU/M6dsG6dc509MtK5zn7c8qy+olPuIiJyxoYMGUKH9u1Z+NprNOnTh+BOnejcty9///33kW2MMUycOJH77ruP6OhoWrZsCUBWVhYjR46kZs2aBAUF0bp1az7//PNj9p+dnc3dd99NhQoViIqKYtiw+9ibnAZArahQoiOCiIuLwxjD6tWrj7wvPz+f5557jkaNGhEUFESNGjW49dZbAejatSvLly9n1qxZGGMwxjBz5kyg4JT7Aw8cU8O8efNo2bIlQUFB1KxVi8cmTiSvSROn156Tw8xXX8UEB7NqwQJ6XnQRYWFhNGnShI8//tjdh7tQPBboxpgZxphEY8zqo55rY4z51RizwhizzBhzjqfaFxERD7KW7Vu3Mnz8eB4fNYrZs2eTkpJCr169yMrKOrLZhAkTSEhI4N1332Xy5MkA9OvXj5kzZ/Loo4/y6aef0rFjR/r27cuKFSuOvO/hhx9m+vTpPP7448x85x3+3riZt157BYAKoSe/reyuu+5i9OjR9O/fn88++4wXX3yRjIwMAF599VWaNGlCnz59+OWXX/jll1+47LLLTrifr7/+muuvv5527dqxaNEi7rnnHl544QXuHjbMCfQWLZzLC8ANDz1E3y5dWLBgAQ0bNmTAgAHs3LmzaMf3LHjylPtM4BXgnaOeGw+MsdZ+YYzpU/C4qwdrEBEpFsZ8uoa/dx/ySdvNqpVn9BXNC7dxXh4cOkS59HSSkpNZNG8eF1x8MQDt27enfv36zJw5kyFDhgBQtWpV5s6de+TtS5YsYfHixcTFxdGlSxcALrnkEjZs2MCzzz7L/Pnz2b9/P6+//jpjxoxh6L33sTUpnYnTOtGvx/kk7jl5aevWreOtt97i5Zdf5t577z3y/PXXX+98zmbNCAsLIzo6mvPOO++UH/OJJ56ga9euzJo1C4BLL70UgEceeYRRo0ZRo0aNI7e03f/wwwy+7TYwhvbt2xMbG8tnn3125Bh4i8d66NbaH4EDxz8NlC/4PhLY7an2RUTETbKzndHr69fDypXOaHZriYmOPhLmALVr16Z9+/b8/vvvR57r06fPMbv69ttvqVKlCp06dSIvL+/IV48ePVi2bBkAq1atIisri569L2PzPuc0e8PY8lx11ZWnLPP7778HOHKK/Wzl5+fzxx9/cN111x3z/PXXX4/L5eKXX3455vlLLrnkyCDASpUqERMTU+J66CdyH/CVMeYFnD8mLvBy+yIifqnQPWRvcbmcWdWSkpzpU8GZIjU2FiIjyYuIICb2nwPSYmJiSEhIOPI49rhtkpKS2LNnDwEnmLClbMFtbnv2ON3wzDLhhJYx1KscRmC5ssTExJyy5P379xMWFkb58uVPud3pJCUlkZub+4/aDz8+cODYvmqFChWOeRwYGHjMZQdv8Xag/xu431r7kTGmP/AWcPGJNjTG3AncCc5BjIuLc1sRaWlpbt2f6Ji6m46n+/nTMY2MjCQ1NdXXZZyYtQTu30/AwYOUyc/HVa4cuZUqkVu+PPaoKVGttezdu/cfnyMhIYGmTZseeT47O/uYbcLDw6lWrRqzZ88+YfOpqakEhkYAkHJgH81qRpGdmUE2HOn1Ht7f4Wvj6enppKamEhYWRnp6Ort27TppqLtcLnJzc/9Rt7WWnJwcUlNTCQoKIiAggPj4+GO2i4+PByAkJITU1NQjoZ2amoo96v70o/d1KllZWcf8TBb1Z9TbgX4LMKzg+/nA9JNtaK2dBkwD6NChg+3atavbioiLi8Od+xMdU3fT8XQ/fzqma9euJSIiwtdl/FNOjnM6PS3NuR0rOpoy5csTZAzH35hljGHfvn2sWrWKCy5wTrbGx8ezcuVK/vWvfx35fMHBwcd81t69ezNlyhRiY2Np0qTJP0o4mJFDdN1mBAUHs/I/S+hzUUfACeIvvvgC4Mj+QkNDAQgLCyMiIoI+ffowfPhwFixYwN13333CjxgcHEx+fv4/jr8xhsDAwCPPt2/fnk8//ZT777//yDaff/45ZcqUoXv37kRERBAcHHyknvCj7ks/fl8nExwcfMztckX9GfV2oO8GugBxQHdgo5fbFxGRE0lJga1bnVPtdeseO+f5SVSuXJlBgwbxzDPPEBISwujRo4mJiTnlNeyePXvSq1cvevbsyUMPPUTz5s05dOgQK1asIDk1jcH3PUa12GjuvOMOxox5ksDAAJo3b86bb75JWlraKetp3Lgxd955JyNGjCAxMZGLLrqIgwcP8uGHHzJnzhwAmjRpwldffcVXX31FpUqVqFu37v8mlDnKmDFj6NWrF7fddhsDBgxg1apVPP7449xxxx3OgDg/5LFAN8Z8gDOCvbIxZicwGrgDeNkYUw7IouCUuoiI+Ii1kJDgrFUeEuJMDlPIyV9q167No48+ysMPP8z27dvp0KEDs2fPPtJzPRFjDB9//DFjx45l0qRJxMfHExUVRbOWrbhm0O2EB5WjTqUwJkyYQF5eHk899RRlypRh0KBBDB8+nBEjRpyypldffZXatWszffp0xo0bR0xMjDNorcCoUaOIj4+nf//+HDp0iLfffvuEf4BccsklzJkzh2eeeYb333+fmJgYRowYwZgxYwp1bHzBWD+el/awDh062MOjH93Bn069lRQ6pu6l4+l+/nRM165dS9OmTX1dhhPmO3Y4a4VXquRMaVrI+ddvvPFG1q9fjzt+NyemZrEnJYvIkABqRoVSppivS15Yx/8cHP0zaoxZbq3tcCb709SvIiKl0dFhHhvrTJLi5SC11rL3UDaJqVlUCA2kZsUQTCkJc09QoIuIlDbWOouN7Nvn0zBPSMkiKS2bqLBAqldQmBeVAl1EpDQ5OsyrVHGWCz2LIH399dfPeqS+tZZdBzM5kJ5D5fAgqkYGK8zdQIEuIlJaZGbCtm2Qnl6kMC8Kl7XsTM7kYEYOMRHBxJYPUpi7iQJdRKSkc7mckex79jiD3urWdZY59XaYuyzxBzI4lJVLlchgYiJOPhpezpwCXUTEB6y13umZZmQ4k8VkZTkj2Y9aVMSb8l2W7fvTScvOo3qFECqF+8ca4r7iiTvMFOgiIl4WEBBAZmbmkZnOPCYlBTZvdnrlDRtCZKRn2zuJPJeLbUkZZObkUbNiKBXDTr78aWmRmZl5wvnsi8Jjq62JiMiJxcTEsGvXLjIyMjzSUwOcRVU2bYKgIGja1GdhnpvvYsu+dDJz86kVpTC31pKRkcGuXbtOu9jMmVIPXUTEyw4vHLJ7925yc3Pd30BKirNSWnCw87V5s9ubyMrKOuWMcAB5Lsv+tGzyXZaosEB2HyqrNbNxztDExsYWeVW44ynQRUR8oHz58m7/hQ7AQw/B+PFw440wYwYEeqZHHBcXd8zCIsfbvC+N26f/Rmp2HjNv60jb2lEeqUP+R4EuIlJSzJnjhPmQITB1KpTxzVXV1btSuGXG7xgDc+88n2bVPPCHi/yDAl1EpCT4+2/417+gUyeYPNlnYb50cxJ3vrOcyJAA3r39HOpFh5/+TeIWCnQRkeIuNRWuvRbCwmDuXJ/clgbw5eoE7v1gBXUqh/LO4HOpEqn7zL1JgS4iUpxZC7ffDhs2wJIlzuxvPvDB7/E8tmAVbWpWYMatHakQWrpHs/uCAl1EpDh7+WWYPx+efx58sDystZYp321i4jcb6No4mldvbEdooKLFF3TURUSKqx9+gAcegKuuggcf9Hrz+S7L44tWM/u3eK5pV53nr21FQFlNb+IrCnQRkeJoxw647jpo0ABmzvT6vOw5+ZYh7y3nm7/38u+u9RnZq7EWWfExBbqISHGTleUMgsvKgoULvT4LXHJ6DuP/m8XmlAyevKIZt3aq69X25cQU6CIixYm1MHQo/Pe/sGABNGni1ea370/n1rf/y44UF68MbMdlrap6tX05OQW6iEhx8sYbzgxwjz/uXDv3ouXbk7njnWW4rGXkOcEKcz+j0QsiIsXFr7/CvfdCnz7w5JNebfqLVQnc8OavRASX4+N/X0CjimW92r6cnnroIiLFwf790L+/s575e+95bSY4ay3TftzCuC/X0bZmBd68uQOVwoOI90rrciYU6CIi/s7lgptvhr17YelSqFjRK83m5Ll4bMEq5i/fyWUtq/Ji/9YEB6hn7q8U6CIi/u755+Hzz+HVV6F9e680eSA9hyHvLef3rQe4t0dD7uvRkDJldFuaP1Ogi4j4s7g4GDUKBgxwVlHzgk2JqQyeuYw9h7J4eUAbrmzjm+lk5cwo0EVE/FVCAgwcCA0bwrRpXpk8ZsnavQybs4LggLLMvfM82tbyzul9KToFuoiIP0pMhB49nJXUvv4aIiI82py1llfjNvPC1+tpXq08027qQLUKIR5tU9xLgS4i4m+Skpww37YNvvgCWrb0aHMZOXk8OP8vFq9K4Mo21Xj+2lYa/FYMKdBFRPzJ/v1w8cWwaRMsXgxduni0ufj9Gdz13nLW7znEo32acMeF9TQnezGlQBcR8RfJydCzJ6xbB59+Ct27e7S5uPWJDJuzAmstM27tSNfGMR5tTzxLgS4i4i9uvRXWrIFFi5xg95Cjr5c3jo3gjZvaU7tSmMfaE+9QoIuI+IPFi+GTT2D8eLj0Uo81k5qVywPzV/LVmr30bV2Ncde2JDRQUVAS6L+iiIivZWU5c7Q3aQLDhnmsmXV7DvHv9/4g/kAGoy5ryu2d6+p6eQmiQBcR8bUJE2DLFvj2WwgM9EgTH/+xk0cXrCIiOIDZ/zqXc+tV8kg74jsKdBERX9q2DcaOheuuc25Vc7Os3Hye+uxvZv8Wz7l1o5hyQ1tiIoLd3o74ngJdRMSX7r/fWTntxRfdvuttSekMnf0Ha3YfYkiX+jxwSSPKldWq2SWVx/7LGmNmGGMSjTGrj3v+HmPMOmPMGmPMeE+1LyLi9774AhYuhMcfh5o13brrT1fu5vIpP7MzOZPpN3fg4d5NFOYlnCd76DOBV4B3Dj9hjOkGXAm0ttZmG2N006OIlE6pqTB0KDRqBMOHu223Wbn5PP3Z37z/WzztalVgyg3tqK4pXEsFjwW6tfZHY0yd457+NzDOWptdsE2ip9oXEfFr994L27fDDz+4bSDcxr2p3PPBn6zbk8pdXerxwCWNCVCvvNQw1lrP7dwJ9M+stS0KHq8AFgGXAlnAA9ba/57kvXcCdwLExsa2nzNnjtvqSktLIzw83G37Ex1Td9PxdD9/OqbRcXE0HzOGbTfdxLbBg4u8P2stcTvy+GBdDkHl4F8tg2gd7dkhUv50PEuKo49pt27dlltrO5zJ+70d6KuB74F7gY7AXKCePU0RHTp0sMuWLXNbXXFxcXTt2tVt+xMdU3fT8XQ/vzmmO3dCq1bOkqg//wwBAUXaXXJ6Dg9//BdfrdnLhQ0r82L/1l4Zxe43x7MEOfqYGmPOONC9Pcp9J/BxQYD/boxxAZWBfV6uQ0TE+1wuuPlmyMmB998vcpj/vE3/e5MAACAASURBVDGJB+avZH96NqMua8rgTnUpU0YTxZRW3g70hUA34HtjTCMgEEjycg0iIr7x4ovw/ffw1lvQoMFZ7yYrN5/nv1zH2//ZRv3oMKbf0okW1SPdWKgURx4LdGPMB0BXoLIxZicwGpgBzCg49Z4D3HK60+0iIiXC2rXw2GNwzTVw221nvZs1u1O4b84KNiamccv5tXm4d1NCArV2uXh2lPvAk7w0yFNtioj4JWvh//4PwsPhtdfgLOZPz3dZ3vhxMy99s4EKoYHMvE3LncqxNFOciIinvf8+xMXB669DzJmH8Pb96Qyft5Ll25Pp3aIKz17dkqgwz8z5LsWXAl1ExJOSk2HECDj3XLjjjjN6q7WW2b/H8+zitZQtY5h0fRuubFNNK6TJCSnQRUQ86bHHICkJvvzSmbO9kBJTsxj54V/Erd9H5waVGd+vFdU045ucggJdRMRTfv/dOc1+773Qtm2h3/bl6gQe+XgVGTn5jL6iGbecX0e3o8lpKdBFRDwhPx/+/W+oWhWeeqpQb0nNymXMp3/z4fKdtKhenknXt6FBTISHC5WSQoEuIuIJs2bBH3/AnDlQvvxpN1+6KYkHP/yLhJRM7unegHu6NySwnOZhl8JToIuIuFtODjz9NHToAP37n3LTjJw8xn2xjnd+2U7dymHMH3IB7WtX9FKhUpIo0EVE3G3WLNi2DaZOPeU95//ddoAH5q9k+/4MbutUh5G9mmiSGDlrCnQREXfKyYFnnnFuU+vd+4SbZObk88LX65nxn63UqBjCnDvP47x6lbxcqJQ0CnQREXd6+22Ij4c33jhh73z59gM8MP8vtialM+i8WjzSuylhQfpVLEWnnyIREXfJznZ65+edB716HfNSVm4+E7/ZwJs/baFaZAiz/3UuFzSo7KNCpSRSoIuIuMtbbznrnc+YcUzvfOWOg4yYv5JNiWnccG4tHu3TlHD1ysXN9BMlIuIOWVkwdix06gQXXwxATp6LV77byNS4zcREBPHO4HO4qFG0jwuVkkqBLiLiDtOnw65dzgh3Y1i/J5X7567g74RDXNuuBk9c0YzIkABfVyklmAJdRKSosrNh3Djo3Jn8rt1468fNvPDVBsqHlOPNmzvQs1msryuUUkCBLiJSVG+/Dbt2sfO1txkx/Td+23qAS5rF8tw1LakUHuTr6qSUUKCLiBRFTg72uef46Ko7eXKZCzjEhH6t6Ne+hpY5Fa9SoIuIFMHBt9/l0bY38HnjzpxTtTwv9m9NzahQX5clpZACXUTkLP28bg8j1gSzv9H5jOzVmLu61KesljkVH1Ggi4icoey8fCZ8uZ7pP2+lfmYqb10QSYtuDXxdlpRyCnQRkTOwNSmdez74g9W7DjFoy888tuU7Qqb96uuyRBToIiKF9fEfO3l84WrKlS3DG7XS6fX8OFiw4JQrqol4iwJdROQ00rLzeGLhaj7+cxfn1I1iUr+WVOvcEVq2hL59fV2eCKBAFxE5pTW7U7h79p9s35/OfRc35J7uDSk75wNYtw4+/BDKlPF1iSKAAl1E5ISstbz763ae+WwtFcMCmH1HwZrl+fnw1FPQqhVcfbWvyxQ5QoEuInKclIxcHvroL75cs4fuTWJ44brWRIUFOi/OmQPr18NHH6l3Ln5FgS4icpQ/4pO5Z/af7D2UxWN9mnJ757qUOXxveV7e/3rnV13l20JFjqNAFxEBXC7L9J+3MP7L9VSJDObDf19Am5oVjt1ozhzYsEG9c/FLCnQRKfUOpOcwYt4Kvl+/jz4tq/DcNa3+udTp4d5569bqnYtfUqCLSKn265b9DJvzJ8kZuTx9VQsGnVvrxIuqfPABbNwIH3+s3rn4JQW6iJRK+S7LK99t4uUlG6hTKYwZt3akebXIE2+clwdPPw1t2qh3Ln5LgS4ipU7ioSyGzVnBL1v2c3Xb6jx9VQvCg07x6/D9953euWaFEz+mQBeRUiVufSIj5q0kIye/cOuW5+Y6vfO2beHKK71XqMgZUqCLSKmQ57KM/Xwt037cQpMqEUwZ2JaGsRGnf+O778LmzbBokXrn4tcU6CJS4sXvz2Dsb1lsSdnCoPNqMeqyZgQHlD39G3Nz4ZlnoH17uOIKzxcqUgQeG6ppjJlhjEk0xqw+wWsjjDHWGFPZU+2LiAAsWrGLyyb/REK6i1dvbMczV7UsXJgDzJoFW7fCmDHqnYvf8+S9FzOBS49/0hhTE7gEiPdg2yJSyqVl5zFi3kqGzVlBw9hwnroghD4tqxZ+Bzk5Tu/8nHOgTx/PFSriJh4LdGvtj8CBE7z0EjASsJ5qW0RKt1U7U7h88k8s+HMn93ZvwLy7zic69Ax/3b39Nmzfrt65FBvGWs/lqjGmDvCZtbZFweMrge7W2mHGmG1AB2tt0kneeydwJ0BsbGz7OXPmuK2utLQ0wsPD3bY/0TF1Nx3Ps+Oyli+35vLRxlwigwx3tQqicZRzev1MjqnJyeHcm24iu3Jl/nzlFQX6Cehn1P2OPqbdunVbbq3tcCbv99qgOGNMKPAozun207LWTgOmAXTo0MF27drVbbXExcXhzv2Jjqm76Xieud0HMxk+bwW/bsmgT8sqjL26JRVCA4+8fkbHdNYsSEwkeNYsunbr5pmCizn9jLpfUY+pN0e51wfqAisL7vmsAfxhjDnHWrvHi3WISAnz6crdPLZgFfkuW7h7y0/FWnj5ZWjWDHr1cm+hIh7ktUC31q4CYg4/Pt0pdxGR00nJzOXJT9aw4M9dtKlZgUnXt6FO5bCi7fSnn+DPP+GNN3SqXYoVjwW6MeYDoCtQ2RizExhtrX3LU+2JSOmydFMSD8xfyd7UbIb1aMjd3RsQUNYN43xffhmiomDQoKLvS8SLPBbo1tqBp3m9jqfaFpGSKys3n/FfrmfGf7ZSr3IYH51o3fKztW0bLFwII0dCaKh79iniJZopTkSKjZU7DjJ83go270vnlvNr83DvpoQEFnKSmMI4PKJ96FD37VPESxToIuL3cvJcvPLdRqbGbSY6PIh3Bp/DRY2i3dtIWhpMnw79+kGNGu7dt4gXKNBFxK+t35PK8HkrWLP7ENe0rc7ovs2JDAlwf0OzZkFKCgwb5v59i3iBAl1E/FK+yzLtxy289M0GIoLL8fqg9lzaoopnGnO5YPJkZ5rX887zTBsiHqZAFxG/s2VfGiPmr+TP+IP0blGFp69qQeXwIM81+OWXsGEDvP++blWTYkuBLiJ+w+WyzFy6jfFfrSOoXFleHtCGvq2rnf0kMYX18stQtapz/VykmFKgi4hf2HEggwfmr+S3rQfo3iSG565pSWz5YM83vHYtfP01PP00BAaefnsRP6VAFxGfstbywe87eHbx3xhjGN+vFdcVZerWMzV5MgQFwV13eac9EQ9RoIuIz+xJyeKhj/7ihw37uKB+Jcb3a0WNil6c0CU5Gd55B264AaLdfBuciJcp0EXE66y1LFqxmycWrSYn38WYvs256bzalCnj5QFp06dDRoZuVZMSQYEuIl61Py2bUQtX88XqPbSrVYEX+7ehblEXVDkbeXnOzHBdukDr1t5vX8TNFOgi4jXf/r2Xhz/+i5TMXEZe2pi7LqpPWW/3yg9btAji42HSJN+0L+Jmp1yayBgz+KjvaxhjlhhjDhpjlhpjGnm+PBEpCQ5l5fLA/JX8651lREcE88ndnfm/rg18F+bg3KpWpw707eu7GkTc6HQ99LuBGQXfTwTmAj2BK4HXgB6eK01ESoKlm5J48MO/SEjJZGi3+gzr0YjAcm5Y5rQo/vzTWff8hRegrBsXdxHxoTM55d7IWtu/4PsFxpgnPFGQiJQMmTn5PP/lOmYu3Ua9ymF8+O8LaFeroq/LckycCGFhcPvtvq5ExG1OF+g1jDGTAQNEG2MCrLW5Ba95YHUEESkJVhQsc7rFU8ucFsX69TB7Ntx3H1Rw0zrqIn7gdIH+4FHfLwPCgWRjTBXgE49VJSLFUm6+iynfbWLq95uIiQji/X+dS6cGlX1d1rGefhqCg+Ghh3xdiYhbnTLQrbWzTvL8HuBRj1QkIsXSpsRU7p+7klW7UrimXXVGX+GhZU6LYu1ap3f+4IMQE+PrakTc6qxvWzPGXG6t/cydxYhI8eNyWd75ZRvPfbGOsKByvD6oHZe2qOrrsk7sqacgNNQJdJESpij3oXcEFOgipdielCwe/HAlP21MonuTGMZd25KYCC8sqHIWQrduhblz4eGHobKfXQYQcYOzDnRr7Wh3FiIixcvnqxJ45ONV5OS5ePbqFtxwTi3vLahyFurMmgXh4TBihK9LEfGI0wa6MaY8EG2t3Xzc862stX95rDIR8Utp2Xk8+ckaPly+k9Y1Innp+jbUiw73dVmntmoVMT/8AI89BpUq+boaEY84ZaAbY/oDk4BEY0wAcKu19r8FL88E2nm2PBHxJ8u3J3P/3BXsTM7gnu4NuLdHQwLK+niSmMIYM4a8sDDKDR/u60pEPOZ0PfRHgfbW2gRjzDnAu8aYR6y1C3DuTReRUiAv38Ur329iynebqBoZzNy7zqdjnShfl1U469bBxx+zc9Ag6kQVk5pFzsLpAr2stTYBwFr7uzGmG/CZMaYmYD1enYj43I4DGQyb8yd/xB/k6rbVGXNlc8oH+9ntaKfy4osQFMSuq6+mjq9rEfGg0wV6qjGm/uHr5wU99a7AQqC5p4sTEd+x1rJwxS4eX7gGA7w8oA1Xtqnu67LOTEICvPMO3H47uRX9ZNpZEQ85XaD/m+NOrVtrU40xlwL9T/wWESnuUjJzeXzhaj5ZuZuOdSoysX8bakaF+rqsMzd5srPu+fDhsHOnr6sR8ajTzRS38ujHBSPeD7/nC08VJSK+8/vWA9w/dwV7DmUxvGcjhnbz8TKnZ+vQIXjtNbj2WmjQQIEuJV6h7kM3xtwFjAGy+N+1cwvU81BdIuJlufkuJi/ZyNTvN1EzKpQPh5xPW39ZHe1svPkmpKRoVjgpNQo7scwDQAtrbZInixER39iWlM6wuStYueMg17Wvwei+zQkPKspEkj6WkwMvvQTdukHHjr6uRsQrCvt/7GYgw5OFiIj3WWuZv2wnT366hoCyZZh6Qzsua+Wn87CfiQ8+gF27YPp0X1ci4jWFDfRHgKXGmN+A7MNPWmvv9UhVIuJxBzNyeOTjVXyxeg/n16vExOtbUzUyxNdlFZ21MGECtGwJvXr5uhoRrylsoL8BfAesAlyeK0dEvGHppiSGz1vJ/vRsHundhDsurEeZ4jjw7US++w7WrIGZM8GP55YXcbfCBnqAtVZzJooUczl5LiZ+s4E3ftxM3cphTL+lEy2qR/q6LPd67TVnvvbrr/d1JSJeVdhA/8IYcyfwKceecj/gkapExO227Etj2JwVrNqVwsBzavH45U0JDSzGA99OZPduWLgQ7r8fgv1zGVcRTyns/80DcW5Te/i4509625oxZgZwOZBorW1R8NwE4AogB2eg3W3W2oNnWrSIFJ61lvnLd/LkJ2sILFeG1we159IWVXxdlme89Rbk58Ndd/m6EhGvK+wySc2AqcBKYAUwhdNP/ToTuPS4577Buf2tFbABZ7CdiHhISmYud3/wJyM//ItWNSL5YtiFJTfM8/Jg2jTo2dOZSEaklClsD30WcAiYXPD4hoLnTjr9q7X2R2NMneOe+/qoh78C/QpbqIicmWXbDjBsjjPj24O9GjOkS/3iOeNbYX3+uTMb3OTJp99WpAQqbKC3sNY2O+rx98aYv4vY9mBgbhH3ISLHyct3MfX7zby8ZAPVK4YU/xnfCuu116BaNbjiCl9XIuITxtrTr4JqjHkPeMVa+2vB43OBodbam0/zvjrAZ4evoR/1/GNAB+Aae5ICCgbh3QkQGxvbfs6cOaets7DS0tIIDw932/5Ex9TdzvZ47s908cZf2WxIdnF+1bLc3DyIkHIluFdeIHj3bs4dNIjtN9/MtltvPeE2+hl1Lx1P9zv6mHbr1m25tbbDmby/sIG+FmgMxBc8VQtYD+QBtuCa+IneV4fjAt0YcytwF9DDWluo2ec6dOhgly1bVphNCyUuLo6uXbu6bX+iY+puZ3M8P1+VwMMf/UW+y/LM1S24um0NzxTnjx5+GF54AbZtgxon/tz6GXUvHU/3O/qYGmPOONALe8r9+MFtZ6Vg2dWRQJfChrmInFp6dh5Pffo3c5ftoHXNCkwe0IbalcJ8XZb3ZGfDjBnOqfaThLlIaVCoQLfWbj/THRtjPgC6ApWNMTuB0Tij2oOAb4wzg9Ov1tohZ7pvEXEs357M8HkriD+Qwf91rc/9PRsRULawN6+UEB99BPv2wRD9KpHSzWOzSlhrB57g6bc81Z5IaZKb72LKd5t45buNVI0MYe6d53NO3Shfl+UbU6ZAw4bO7WoipVgJmyZKpOTblJjGiPkrWbnjINe0q86TfZtTPjjA12X5xrJl8Ouv8PLLUKaUnZkQOY4CXaSYyHdZ3v7PViZ8tZ6QwLIlZ6nTopgyBcLD4SQj20VKEwW6SDGwfX86D8xfyX+3JXNx01jGXtOCmIhSPld5YiLMmQN33AHly/u6GhGfU6CL+LF8l2XW0m1M+Go95coaXryuNde0q47RsqDw5puQkwN33+3rSkT8ggJdxE9t2JvKyA//YsWOg3RrHM3Ya1pSNTLE12X5h9xcePVVuOQSaNLE19WI+AUFuoifyclzsWhTDp998xPhQeV4eUAb+raupl750RYscJZKfeMNX1ci4jcU6CJ+5PetB3h0wSo2JeZyZZtqPHF5MyqFB/m6LP8zZQrUqwe9e/u6EhG/oUAX8QPJ6TmM+2Idc5ftoHqFEO5vH8Sw69r6uiz/tGIF/PwzvPgilC3r62pE/IYCXcSHrLV89Mcuxn6+lpTMXO7qUo9hPRry+9KffV2a/3rpJQgNhcGDfV2JiF9RoIv4yLo9h3h84Wr+uy2ZtrUqMPbqljStqtuvTmnrVnj/fbj3XqhQwdfViPgVBbqIl6Vm5TLp243MXLqN8sHleP7allzXviZlymjQ22lNmODMCDdihK8rEfE7CnQRL7HWsnDFLp77fB370rIZ0LEWI3s1pmJYoK9LKx4SEpxV1W69FapX93U1In5HgS7iBat3pfDkJ2tYtj2Z1jUimXZzB9rU1CnjMzJxonP/+UMP+boSEb+kQBfxoOT0HF74ej2zf48nKjRQp9fP1oED8NprMGAA1K/v62pE/JICXcQD8vJdvPfrdl76diNp2Xnccn4d7u/ZiMiQUroqWlFNngzp6fDII76uRMRvKdBF3OznjUk89dkaNuxNo3ODyjxxRTMaxUb4uqziKzXVCfQrr4QWLXxdjYjfUqCLuMm2pHSe/Xwt3/y9l1pRoUy7qT09m8Vqytaiev11SE6GRx/1dSUifk2BLlJEqVm5vPLdJmb8ZyuBZcvwYK/G3N65LsEBmsWsyLKynMFwPXrAOef4uhoRv6ZAFzlL+S7LvGU7ePHr9exPz6Ffuxo82KsxMeVL+Trl7jRzJuzZ40wmIyKnpEAXOQs/b0zimcV/s25PKh1qV2TGrR1pVUO3oblVXh6MH+/0zLt183U1In5PgS5yBjbvS2Ps4rUsWZdIjYohTL2hHX1aVtF1ck+YN8+Z6nXiRNDxFTktBbpIISSn5/Dyko289+t2ggPK8nDvJtx6QR1dJ/cUa2HcOGjWDPr29XU1IsWCAl3kFLLz8nln6XamfOfcTz7wnFrc37MRlbVGuWctXgyrVsGsWc7c7SJyWgp0kROw1vLF6j2M+2Id8Qcy6No4mkf7NNX95N5gLTz3HNSuDQMH+roakWJDgS5ynD/jk3l28VqWbU+mcWwEswafQ5dG0b4uq/T46SdYuhSmTIEAzawnUlgKdJECOw5kMOGr9XyycjeVw4N47pqWXNe+BuXK6pSvVz33HERHw+DBvq5EpFhRoEupdzAjh6nfb2LW0u0YA3d3a8CQrvUJD9L/Hl63ZAl8+aUT6qGhvq5GpFjRbywptQ4PeHvl+00cysqlX7saDL+kEVUjQ3xdWumUnQ1Dhzqrqd13n6+rESl2FOhS6uS7LItW7OLFrzew62AmXRtH83DvJjSpUt7XpZVuEyfC+vXw+ecQrNn2RM6UAl1KDWstcRv28fwX61i3J5WW1SMZ368VnRpU9nVpsn07PP00XH019O7t62pEiiUFupQKK3ccZNwX6/hly35qRYUyZWBbLmtZlTJlNAOZX7jvPmc2uEmTfF2JSLGlQJcSbWtSOi98tZ7FqxKoFBbImL7NGXhOLQLLaeS631i8GBYudGaGq1XL19WIFFsKdCmR9qVmM3nJRj74PZ7AcmUY1qMhd1xUTyPX/U1mJtxzDzRtCvff7+tqRIo1/XaTEiUjJ4/pP23ljR82k53nYsA5Nbm3R0NiIjTIyi9NmeIswLJkCQQG+roakWJNgS4lQl6+i/nLdzLxmw3sS83m0uZVGHlpY+pFh/u6NDmZAwec+80vuwy6d/d1NSLFnscC3RgzA7gcSLTWtih4LgqYC9QBtgH9rbXJnqpBSj5rLd+tS2TcF+vYmJhGu1oVeH1QO9rXjvJ1aXI648ZBSooT6iJSZJ4cGTQTuPS45x4GllhrGwJLCh6LnJW/dh5k4Ju/cvusZeS5LK/d2I6P/n2Bwrw4iI+HyZPh5puhZUtfVyNSInish26t/dEYU+e4p68EuhZ8PwuIAx7yVA1SMu04kMELX69n0YrdVAoL5KkrnZHrAZpzvfgYPdr596mnfFuHSAni7WvosdbahILv9wCxXm5firGUjFymxm1i5n+2YQwM7VafIV3qExGsFbmKlcPrnI8YodvURNzIWGs9t3Onh/7ZUdfQD1prKxz1erK1tuJJ3nsncCdAbGxs+zlz5ritrrS0NMLDNVjKnTx5TPNcliXxeXyyOYeMXOhUvRzXNAwgKrjk9shL8s9oi0cfpcJff/Hr7NnklffedLsl+Zj6go6n+x19TLt167bcWtvhTN7v7R76XmNMVWttgjGmKpB4sg2ttdOAaQAdOnSwXbt2dVsRcXFxuHN/4pljaq1l8aoExn+5nvgDOVzYsDKP9G5Ks2olf871Evsz+sMP8MsvMG4cnfv29WrTJfaY+oiOp/sV9Zh6O9A/AW4BxhX8u8jL7Usx8fvWA4z9fC0rdhykSZUI3hl8Dhc1ivZ1WVIUublw991Qs6YzmYyIuJUnb1v7AGcAXGVjzE5gNE6QzzPG3A5sB/p7qn0pnjbuTeX5L9fx7dpEYssHMaFfK65pV4OymnO9+Js4EVavhkWLtNa5iAd4cpT7wJO81MNTbUrxtScli0nfbmDesh2EBZbjwV6NGdypLiGBZX1dmrjD1q0wZgxcdRV4+VS7SGmhmeLEp1Iyc3njh83M+M9W8l2WWy+oy93dGxAVpmlASwxrnVPtZcs6956LiEco0MUnsnLzeeeXbUz9fjOHsnK5snU1hvdsTK1KOhVb4nz4IXz+Obz0knP9XEQ8QoEuXpWX7+LjP3cx6ZsN7E7JokujaEZe2pjm1SJ9XZp4QkoKDBsGbds6vXQR8RgFuniFtZav1uzlha/XsykxjdY1K/BC/9ZcUL+yr0sTTxo9GvbscQbCldOvGxFP0v9h4nFLNycx/sv1rNhxkPrRYbw+qD29msdijEaul2i7dsFrr8HgwdCxo6+rESnxFOjiMat2pjD+q3X8tDGJqpHBjL+2Fde0q045zbleOjz/PLhcMGqUrysRKRUU6OJ2m/elMfHrDSxelUDF0ABGXdaUQefVJjhAt6CVGrt3w7RpcMstUKeOr6sRKRUU6OI2uw5m8vK3G/hw+U6CA8pyb4+G3HFhXS2eUhqNHw95efDoo76uRKTUUKBLkSWlZfP+2mx++CYOgFsvqMv/datP5fAg3xYmvpGQAG+8ATfdBPXq+boakVJDgS5n7VBWLm/+uIW3ft5KVm4+/TvU5J4eDaleIcTXpYkvTZjgzNv+2GO+rkSkVFGgyxnLys1n1tJtvPbDZg5m5HJZq6p0Ln+QgZe38nVp4mt798Lrr8ONN0KDBr6uRqRUUaBLoeXmu5i3bAeTl2xk76FsujSK5sFejWlRPZK4uDhflyf+YMIEyM7WyHYRH1Cgy2m5XJZP/9rNxG82sH1/Bu1rV2TygLacW6+Sr0sTf7JpE0ydCjfcAA0b+roakVJHgS4nZa1lydpEXvh6Pev2pNK0anlm3NqBbo1jNCmMHMtaGDIEAgOd+89FxOsU6HJCSzcnMeGr9fwZf5A6lUKZPLAtl7esShmtSy4n8s47sGSJMzNctWq+rkakVFKgyzFW7jjIhK/W8/MmZ3a3cde05Nr2NQjQ7G5yMomJMHw4dOoEd97p62pESi0FugCwcW8qL3y9nq/W7CUqLJDHL2/GjefW0uxucnrDh0NqqjMzXBn94SfiKwr0Um5ncgaTvt3Ix3/sJDSwHPdf3IjbL6xLeJB+NKQQvvoK3n8fnngCmjXzdTUipZp+a5dS+9OyeeX7Tbz/azwYuL1zXf7dtQFRYYG+Lk2Ki/R0ZyBc48aa4lXEDyjQS5m07Dze+mkrb/60hYycPK5rX5NhFzekmmZ3kzM1ciRs3w4//ABBmuZXxNcU6KVEbr6LOb/H8/KSjSSl5XBp8yo80KsxDWLCfV2aFEfffAOvvgr33w8XXujrakQEBXqJZ63ly9V7GP/VerYmpXNO3Sim3dyEdrUq+ro0Ka4OHoTBg6FJE3j2WV9XIyIFFOgl2B/xyTzz2d/8EX+QhjHhvHVLB7o30aQwUkT33eesqPbLLxCiSzUi/kKBXgLtOpjJ81+s45OVu4mJCOL5a1tybbsalNO95FJUixbBrFnw+OPQsaOvqxGRoyjQS5CMnDxej9vMGz9uAeDe7g24q0t9wnQLmrjDvn3OxDFt22rxFRE/pN/0JYC1lsWrEnh28VoSUrK4sk01Rl7aROuSi/tYe66dkAAAFPRJREFU61w3P3gQvv3WmbNdRPyKAr2Y27A3ldGL1vDLlv00r1aeV25oS/vaUb4uS0qayZPhs8+cf1u29HU1InICCvRiKjUrl0nfbmTm0m2EB5XjmataMPCcWpTV4inibn/+6dxz3rcv3H23r6sRkZNQoBcz1loWrtjF2M/XkZSWzYCOtXiwV2PN8CaekZYG118P0dEwYwboDgkRv6VAL0bWJhxi9KI1/9/enUdXVZ19HP8+BEIIaGJRwhDmGREQcEAsg2ArxaIFpRSpShGwVgW0q8W2vGjrPAJFrBYcUUDBgYJFKKAoAspkQQYVAgiCIGFKGELIfv/Y10WKTAk3Obnn/j5r3ZXcIScPm5373LPP3s/mkw2ZNK+eytgbW9O8emrQYUmY3X47rFsHc+ZAxYpBRyMiJ6GEHgN278/hyVlfMH7hRlLKleHh7hfQs3V17U0uRWvcOL9EbfhwaN8+6GhE5BSU0EuwI3mOCZ9s4omZa9lz4DB9Lq3JXVc2IDVZw+tSxJ5+Gu64Azp31hI1kRihhF5CfZKRyb1TP2fV1r1cUvtH3NvtfBpXOTvosCTsnIP77/fboXbrBhMnQmm9TYjEAv2lljCbd+3noXfXMH3FVqqmJDG694V0vaCKyrVK0cvLg7vugpEj4cYb/ZC7krlIzNBfawmRv8qbGQzuXJ+B7epSLjEh6NAk7LZtgxkzYMIEmDkTBg2CJ5+EUioVLBJLlNADlpfneHPZFh57bw3f7j1Et+ZVGdqlkfYnl6KRmQlr1/rbqlW+6tuyZf65KlXgscfg7ru1PE0kBgWS0M1sCHAL4IAVQF/n3MEgYgnSJxmZ/G3aKlZs2UPz6qmMuaGlqrxJ9GVnw1NPwejR8O23Rx8vXRratIEHH4QuXaB5cyVykRhW7AndzKoBdwJNnHMHzOx1oBfwYnHHEpSNO7N5+N9r+PfKbVRJSWLEL1vQrXlVLUOT6MrNhRde8MvOtm6Frl2hY0do2BAaNIDataFMmaCjFJEoCWrIvTRQzswOA8nANwHFUax278/h73O+4uUFGyiTUIohnRswoF0dXSeX6HvvPRgyBFavhssugzfegLZtg45KRIqQOeeK/5eaDQIeAA4AM51zNxznNQOAAQBpaWmtJk6cGLXfn5WVRYUKFaJ2vFPJzXPM3pTL1HU57D8MP04vTfd6ZUhNCs+ko+Ju07ArbHsmZmZS9+mnSZszh/3p6awfOJDv2rbVUDrqo9Gm9oy+/G3asWPHJc651gU6gHOuWG/AOcAc4DygDPA20OdkP9OqVSsXTXPnzo3q8U4kLy/PTfvsG/fjR+a4mn+c5vqMXehWfbOnWH53cSuuNo0XBW7PI0ece/ZZ51JTnUtMdO6++5w7eLBIYotV6qPRpfaMvvxtCix2BcyvQQy5dwYynHM7AMzsTeAyYHwAsRSZJRszeWD6apZu2k3DtLN46TcX077BeUGHJWFz4IAv/jJqFCxf7q+RP/OMv04uInEliIS+CbjUzJLxQ+6dgMUBxFEkMr7L5tEZfsJbpbPK8miPZvRola5tTSW6vv4axoyBf/4Tdu6E88+Hl1+GPn00vC4Sp4o9oTvnFpnZZGApkAssA54r7jiiLTM7h1Gzv2T8wo0klvYT3vq3q01yopb6S5RNnQq9e/uz82uu8TXXO3RQIheJc4FkG+fccGB4EL872g4ePsIL8zcwZu5XZOfk0uviGgzuXJ9KZyUFHZqEjXMwYoQv/NKqFUyaBHXqBB2ViJQQOn0spLw8x1vLtvDEzLV8s+cgnRpVYmiXRtRPOyvo0CSMcnPhzjv99fHu3eGVVyA5OeioRKQEUUIvhI++/I4H313Nqq17aZaewhM9W9CmbsWgw5IwysmBefPg0Udh1iz4wx/goYdUZ11EfkAJvQBWfbOXh/69mg+//I5qqeUY2asFP2+mCm8SZVlZVJ4+3c9cnzULsrKgXDl47jno3z/o6ESkhFJCPw1bdh/giZlreWvZFs5OKsNfujbm121qUra0KrxJFB054ku1DhtGo23bID0dbrjBl2y94gooXz7oCEWkBFNCP4mdWYd4eu46xi/cCAYD2tXhtvb1SElW/WuJslmz/GS3FSvgsstY+qc/0fL22zVzXUROmxL6cew7eJixH2Yw9sP1HDh8hOtapTOocwOqaUtTiaaDB2HKFHj2WfjwQ79ZyhtvQI8e7P3gAyVzESkQJfR89hw4zCsLNjDuowx27T9Ml6aVufsnDahXSTPXJUr27PEbpkyaBC+9BLt2Qd268OSTcNttULZs0BGKSIxSQscPrT8/P4OXP97IvkO5dGx4HoM7N6B59dSgQ5NY5hx8/DG89pofSv/ii6P7kZcp45efDRjgi8Jo1rqInKG4TehH8hwL1+9kypLNvLtyK4dy8+jStDK3dahH02opQYcnsWz7dl+Gddw4WLPGT2Zr0cJPbmvQwN8uvxzOU21/EYmeuEvo63dkMfmLHO5ZMIetew5yVlJpfnFhOv0ur6WhdSm8nByYPt0Po0+f7gvBtGnjk3rPnqBtJkWkiMVdQn9z6RbezThMh4bn8OeujencOI2kMlp+JoW0Zg2MHg0TJkBmJlSuDIMGwW9+A02aBB2diMSRuEvofdvWoh5buPanFwUdisSyDRvgvvv80HqZMnDttXDTTXDllVA67v6sRKQEiLt3nooVypJaVhOQpJC2bYMHHvBLzUqVgsGDYehQXQ8XkcDFXUIXKZTt23099TFj/PXyfv1g2DBfzU1EpARQQhc5me++g8ce89fJDx70pViHDYP69YOOTETkfyihH+vwYZg715+R5eQcvR058r+v27/fD79u3eq/7t4NKSlwzjn+VrEiNG8Ol14KDRtqnXGs2bkTHn8c/v53/3/du7dP5A0bBh2ZiMhxKaF/b906GDsWXnzRJ+jTkZoKVar4mc0NGsDevT7Bf/457NgB2dlHX3fJJVCjhl+TnJzsb+ee68t91qnjn0tMLLJ/npymzExftW3kSP//17MnDB8OjRsHHZmIyEkpoa9ZA7ffDrNnQ0KCL/5xyy3QqJEvw5mY6GcxHztzuWxZSEo68XHz8nxlsIULYcECWLQIPvvMn+1lZ//wjL9UKZ/c27SBtm194ZEmTfzjzh0dKThLa+WLxOrV8Mwz/gPdvn1w/fU+kZ9/ftCRiYiclvhO6FOnQp8+PjHffz/07QtVq0bn2KVK+Q8FjRrBzTf/8PmcHD+sv349ZGT4rytX+l23xo/3r0lO9ht0HDjgPyAAXHWV3xe7evXoxBnPcnPh7bf9RLe5c/0Ht+uu87PWmzULOjoRkQKJz4Sel+fXEN97L7RuDW+9VfyzlRMT/e9MT4d27Y4+7pxP7h99BMuX+1GDcuX8LTsbRozwZ42PPw79+2tHrsL66iv/YW7RIqhZEx56yBeDqVQp6MhERAol/hL63r00/b//g/nzfSGQf/zj5EPnxc3M775Vt66P71j9+vlEPnAgvP66Ly1as2bxxxmrnPNtNniwPyMfPx569fIfnEREYlj8Tb3+y1+ouGABjBoFL7xQspL56ahTB/7zH/9B5JNP/AjDxx8HHVVs2LHD73DWv7+fpLhihV+GpmQuIiEQfwn9b39j+VNPwR13xO5wtZk/Q1+61C+Ru+IKeOONoKMq2aZNgwsugHffhSee8HMVVBRGREIk/hJ6Sgp7wjLhqV49P4P+oov88qpHHvFDynLUvn3+jPznP4e0NPj0U7jrLtUFEJHQ0btarKtY0Z9t9urlZ2f36+fXw4ufWNisGTz/PNxzj79EEZYPcyIix1BCD4OkJHj1Vfjzn/066kaNYOLE+D5bHzsWOnb018fnzYMHH/S1A0REQkoJPSxKlfJr6Rct8mvpf/Ur6NzZF86JJ3l5/my8f3//71+61BfqEREJOSX0sLnoIp/Ux4yBJUv8EPP99/sa9WF34IC/9PDww3DrrfCvf8HZZwcdlYhIsVBCD6OEBPjtb33p2e7d/aYil1wC//1v0JEVnR07oFMnmDzZF90ZM+aH5XpFREJMCT3MKlXy19KnTIEtW6BVK18h79ChoCOLrtWr/QeWZcv88r27747dJYkiIoWkhB4Punf3O8D17OnL3TZs6Gd+5+YGHdmZmz3bb2izfz988AH06BF0RCIigVBCjxfnnutnws+c6c/c+/Xzu7lNmPDDnd9ixbhxfrOa9HQ/b+Dii4OOSEQkMEro8ebKK33ye+cdv+FL794+wV97ra+g9umnJf/M/dAhX+nvllt8lbz581XPXkTinhJ6PDKDbt38NecpU+AXv/BD8r//vT/LrVnTb9FaEmfGr1vnl6GNHu03WJk2DVJSgo5KRCRwSujxrFQpf3197Fj48ks/ce6113xCHzjQD8lPmnR0L/agTZ4MLVv6pP722/DUU37HNBERCSahm1mqmU02szVmttrM2gQRhxzj+4I08+f7IfmkJL+uu2lTGDDALwWbP9/XRy9OS5b4CX3XXw+NG/uRhWuuKd4YRERKuKDO0EcCM5xzjYDmwOqA4pDj+X5IfvlyeOUVqFzZnx3/7ndw+eWQmgo//amfZJedXTQxOAczZvi15a1bw3vv+fX08+ZBrVpF8ztFRGJYsVfeMLMUoB1wM4BzLgfIKe445DQkJECfPv7mHGzeDJ995vdfnzDBP16hAvToQcqFF0L79me+/vvgQT/sP2KE36+8alV49FE/QqBr5SIiJ2SumDfwMLMWwHPAKvzZ+RJgkHMu+5jXDQAGAKSlpbWaOHFi1GLIysqiQoUKUTteXMrLI2XFCtJmzaLS++9TOjub7Bo12Hr11Wz7yU/ITUkhISuL8hkZVMjIoPTeveyvUYPsWrU4WK0aLiHhf46VmJlJlenTqfbOOyTu2kVWnTp8ff31bO/UCReH18nVR6NPbRpdas/oy9+mHTt2XOKca12Qnw8iobcGFgJtnXOLzGwksNc5N+xEP9O6dWu3ePHiqMXw/vvv06FDh6gdL+7t38+av/6VRvPm+f3Zy5b1e49v2nT815ctC7Vr+7Px3bthz56jO8N17QpDhvjlaHFc7U19NPrUptGl9oy+/G1qZgVO6EEUu94MbHbOLYrcnwwMDSAOiZbkZLZddRWNHn7Y14sfO9bXVr/1VrjgAr9BTMWKfue3lSv9Ern166F8eT+Mnprqbz/7md/6VURECqzYE7pzbpuZfW1mDZ1za4FO+OF3CYNmzWDUqOM/16qVv4mISNQFtR3VHcCrZpYIrAf6BhSHiIhIKASS0J1zy4ECXRsQERGRE1OlOBERkRBQQhcREQkBJXQREZEQUEIXEREJASV0ERGREFBCFxERCQEldBERkRBQQhcREQkBJXQREZEQUEIXEREJASV0ERGRECj2/dALw8x2ABujeMhzge+ieDxRm0ab2jP61KbRpfaMvvxtWtM5d15BfjgmEnq0mdnigm4cLyenNo0utWf0qU2jS+0ZfWfaphpyFxERCQEldBERkRCI14T+XNABhJDaNLrUntGnNo0utWf0nVGbxuU1dBERkbCJ1zN0ERGRUIm7hG5mV5nZWjP7ysyGBh1PrDGz6mY218xWmdnnZjYo8viPzGyWmX0Z+XpO0LHGGjNLMLNlZjYtcr+2mS2K9NVJZpYYdIyxwsxSzWyyma0xs9Vm1kZ99MyY2ZDI3/xKM5tgZknqowVjZs+b2XYzW5nvseP2S/NGRdr2v2bW8lTHj6uEbmYJwNNAF6AJ8CszaxJsVDEnF7jbOdcEuBT4XaQNhwKznXP1gdmR+1Iwg4DV+e4/AjzlnKsH7AL6BRJVbBoJzHDONQKa49tVfbSQzKwacCfQ2jnXFEgAeqE+WlAvAlcd89iJ+mUXoH7kNgB45lQHj6uEDlwMfOWcW++cywEmAtcEHFNMcc5tdc4tjXy/D/9GWQ3fji9FXvYScG0wEcYmM0sHugJjI/cNuAKYHHmJ2vQ0mVkK0A4YB+Ccy3HO7UZ99EyVBsqZWWkgGdiK+miBOOfmAZnHPHyifnkN8LLzFgKpZlblZMePt4ReDfg63/3NkcekEMysFnAhsAhIc85tjTy1DUgLKKxYNQL4A5AXuV8R2O2cy43cV189fbWBHcALkUsYY82sPOqjheac2wI8DmzCJ/I9wBLUR6PhRP2ywPkq3hK6RImZVQCmAIOdc3vzP+f80gktnzhNZnY1sN05tyToWEKiNNASeMY5dyGQzTHD6+qjBRO5rnsN/sNSVaA8Pxw6ljN0pv0y3hL6FqB6vvvpkcekAMysDD6Zv+qcezPy8LffDwdFvm4PKr4Y1BboZmYb8JeBrsBfA06NDG+C+mpBbAY2O+cWRe5Pxid49dHC6wxkOOd2OOcOA2/i+6366Jk7Ub8scL6Kt4T+KVA/MjMzET+pY2rAMcWUyLXdccBq59yT+Z6aCtwU+f4m4J3iji1WOefucc6lO+dq4fvkHOfcDcBc4LrIy9Smp8k5tw342swaRh7qBKxCffRMbAIuNbPkyHvA922qPnrmTtQvpwI3Rma7XwrsyTc0f1xxV1jGzH6Gv16ZADzvnHsg4JBiipldDnwIrODo9d4/4a+jvw7UwO+M19M5d+zkDzkFM+sA/N45d7WZ1cGfsf8IWAb0cc4dCjK+WGFmLfATDBOB9UBf/AmM+mghmdl9wC/xK12WAbfgr+mqj54mM5sAdMDvqvYtMBx4m+P0y8gHp9H4Sxv7gb7OucUnPX68JXQREZEwirchdxERkVBSQhcREQkBJXQREZEQUEIXEREJASV0ERGREFBCF4lDZlYr/45PIhL7lNBFJCryVQwTkQAooYvErwQz+2dkj+uZZlbOzFqY2cLI/stv5dub+X0zax35/txImVrM7GYzm2pmc/BbP4pIQJTQReJXfeBp59z5wG6gB/Ay8EfnXDN8NcDhp3GclsB1zrn2RRapiJySErpI/Mpwzi2PfL8EqAukOuc+iDz2En5f8VOZpRKqIsFTQheJX/lrbh8BUk/y2lyOvl8kHfNcdjSDEpHCUUIXke/tAXaZ2Y8j938NfH+2vgFoFfn+OkSkxNGsVBHJ7ybgH2aWzNFdygAeB143swHA9KCCE5ET025rIiIiIaAhdxERkRBQQhcREQkBJXQREZEQUEIXEREJASV0ERGREFBCFxERCQEldBERkRBQQhcREQmB/wdDZHg8D0ZePgAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}]}