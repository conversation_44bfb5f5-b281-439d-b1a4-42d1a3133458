#!/bin/bash

# Ubuntu中文字体问题快速修复脚本
# 一键解决matplotlib图表中文显示和Word文档格式问题

echo "🚀 Ubuntu中文字体问题快速修复工具"
echo "=================================="

# 检查是否为root权限
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  此脚本需要root权限来安装系统字体"
    echo "请使用: sudo bash quick_fix_ubuntu.sh"
    exit 1
fi

# 获取当前用户（即使在sudo下）
REAL_USER=${SUDO_USER:-$USER}
REAL_HOME=$(eval echo ~$REAL_USER)

echo "👤 当前用户: $REAL_USER"
echo "🏠 用户目录: $REAL_HOME"

# 1. 更新包列表
echo ""
echo "📦 步骤1: 更新包列表..."
apt-get update -qq

# 2. 安装中文字体包
echo ""
echo "🔤 步骤2: 安装中文字体包..."
apt-get install -y \
    fonts-wqy-microhei \
    fonts-wqy-zenhei \
    fonts-arphic-ukai \
    fonts-arphic-uming \
    fonts-noto-cjk \
    fontconfig

# 3. 刷新字体缓存
echo ""
echo "🔄 步骤3: 刷新字体缓存..."
fc-cache -fv

# 4. 清除matplotlib缓存
echo ""
echo "🗑️  步骤4: 清除matplotlib字体缓存..."

# 清除root用户的缓存
if [ -d "/root/.cache/matplotlib" ]; then
    rm -rf /root/.cache/matplotlib
    echo "  ✅ 清除root用户matplotlib缓存"
fi

# 清除实际用户的缓存
if [ -d "$REAL_HOME/.cache/matplotlib" ]; then
    rm -rf "$REAL_HOME/.cache/matplotlib"
    echo "  ✅ 清除$REAL_USER用户matplotlib缓存"
fi

# 清除所有用户的matplotlib缓存
find /home -name ".cache" -type d -exec find {} -name "matplotlib" -type d -exec rm -rf {} + 2>/dev/null || true

# 5. 设置环境变量
echo ""
echo "🌍 步骤5: 配置环境变量..."

# 添加到系统环境
if ! grep -q "LANG=zh_CN.UTF-8" /etc/environment; then
    echo 'LANG=zh_CN.UTF-8' >> /etc/environment
fi

if ! grep -q "LC_ALL=zh_CN.UTF-8" /etc/environment; then
    echo 'LC_ALL=zh_CN.UTF-8' >> /etc/environment
fi

# 6. 生成locale
echo ""
echo "🌐 步骤6: 配置locale..."
locale-gen zh_CN.UTF-8 2>/dev/null || echo "  ⚠️  locale生成可能失败，但不影响字体显示"

# 7. 创建matplotlib配置
echo ""
echo "⚙️  步骤7: 创建matplotlib配置..."

MATPLOTLIB_CONFIG="
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
"

# 为root用户创建配置
mkdir -p /root/.matplotlib
echo "$MATPLOTLIB_CONFIG" > /root/.matplotlib/matplotlibrc.py

# 为实际用户创建配置
if [ "$REAL_USER" != "root" ]; then
    mkdir -p "$REAL_HOME/.matplotlib"
    echo "$MATPLOTLIB_CONFIG" > "$REAL_HOME/.matplotlib/matplotlibrc.py"
    chown -R $REAL_USER:$REAL_USER "$REAL_HOME/.matplotlib"
fi

# 8. 验证安装
echo ""
echo "✅ 步骤8: 验证字体安装..."

FONT_COUNT=$(fc-list :lang=zh | wc -l)
echo "  📊 找到 $FONT_COUNT 个中文字体"

if [ $FONT_COUNT -gt 0 ]; then
    echo "  🎯 主要中文字体:"
    fc-list :lang=zh | head -5 | while read line; do
        font_name=$(echo "$line" | cut -d: -f2 | cut -d, -f1 | xargs)
        echo "    - $font_name"
    done
else
    echo "  ❌ 未找到中文字体，安装可能失败"
fi

# 9. 重启相关服务提示
echo ""
echo "🔄 步骤9: 服务重启建议..."
echo "  为了使字体更改生效，建议："
echo "  1. 重启Python应用: pkill -f 'python.*app.py' && python app.py"
echo "  2. 或者重启整个系统: reboot"

# 10. 测试脚本提示
echo ""
echo "🧪 步骤10: 运行测试..."
echo "  安装完成后，请运行测试脚本验证效果："
echo "  python3 test_ubuntu_fix.py"

echo ""
echo "✨ 修复完成！"
echo ""
echo "📋 修复内容总结："
echo "  ✅ 安装了6个中文字体包"
echo "  ✅ 刷新了系统字体缓存"
echo "  ✅ 清除了matplotlib字体缓存"
echo "  ✅ 配置了环境变量和locale"
echo "  ✅ 创建了matplotlib配置文件"
echo ""
echo "🔍 如果问题仍然存在："
echo "  1. 检查服务是否重启: ps aux | grep python"
echo "  2. 运行测试脚本: python3 test_ubuntu_fix.py"
echo "  3. 查看详细日志: python3 -c \"import matplotlib.font_manager as fm; print([f.name for f in fm.fontManager.ttflist if 'WenQuanYi' in f.name])\""
echo ""
echo "💡 技术支持："
echo "  如果仍有问题，请提供以下信息："
echo "  - 系统版本: lsb_release -a"
echo "  - Python版本: python3 --version"
echo "  - 字体列表: fc-list :lang=zh"
echo "  - matplotlib版本: python3 -c \"import matplotlib; print(matplotlib.__version__)\""
