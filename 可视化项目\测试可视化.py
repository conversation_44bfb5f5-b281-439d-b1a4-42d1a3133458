#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化脚本 - 确保效率不超过95%
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def main():
    print("测试可视化脚本")
    print("="*50)
    
    try:
        # 加载数据
        ks_file = r'D:\连微\7.24数据_KS检验清洗结果_修改版.csv'
        data = pd.read_csv(ks_file, encoding='utf-8-sig')
        data['创建时间'] = pd.to_datetime(data['创建时间'])
        
        print(f"数据加载成功: {len(data)} 条记录")
        print(f"时间范围: {data['创建时间'].min()} 到 {data['创建时间'].max()}")
        
        # 分离进出口数据
        inlet_data = data[data['进口0出口1'] == 0]
        outlet_data = data[data['进口0出口1'] == 1]
        
        print(f"进口数据: {len(inlet_data)} 条")
        print(f"出口数据: {len(outlet_data)} 条")
        
        # 识别时间段
        all_times = sorted(data['创建时间'].unique())
        time_segments = []
        current_segment = [all_times[0]]
        
        for i in range(1, len(all_times)):
            time_diff = (all_times[i] - all_times[i-1]).total_seconds() / 60
            if time_diff > 60:  # 间隔超过1小时，开始新时间段
                time_segments.append(current_segment)
                current_segment = [all_times[i]]
            else:
                current_segment.append(all_times[i])
        
        if current_segment:
            time_segments.append(current_segment)
        
        print(f"识别到 {len(time_segments)} 个时间段")
        
        # 计算每个时间段的效率
        segment_data = []
        
        for segment_idx, time_segment in enumerate(time_segments):
            segment_start = time_segment[0]
            segment_end = time_segment[-1]
            
            segment_data_points = data[
                (data['创建时间'] >= segment_start) & 
                (data['创建时间'] <= segment_end)
            ]
            
            segment_inlet = segment_data_points[segment_data_points['进口0出口1'] == 0]
            segment_outlet = segment_data_points[segment_data_points['进口0出口1'] == 1]
            
            if len(segment_inlet) > 0 and len(segment_outlet) > 0:
                avg_inlet = segment_inlet['进口voc'].mean()
                avg_outlet = segment_outlet['出口voc'].mean()
                
                if avg_inlet > 0:
                    efficiency = (avg_outlet / avg_inlet) * 100
                    # 确保效率不超过95%
                    efficiency = min(efficiency, 95.0)
                    
                    segment_data.append({
                        'segment': segment_idx + 1,
                        'efficiency': efficiency,
                        'time_start_str': segment_start.strftime('%H:%M'),
                        'time_end_str': segment_end.strftime('%H:%M'),
                        'inlet_conc': avg_inlet,
                        'outlet_conc': avg_outlet
                    })
                    
                    print(f"时段{segment_idx+1}: {segment_start.strftime('%H:%M')}-{segment_end.strftime('%H:%M')}, "
                          f"进口={avg_inlet:.2f}, 出口={avg_outlet:.2f}, 效率={efficiency:.1f}%")
        
        if not segment_data:
            print("没有有效的时间段数据")
            return
        
        # 创建可视化
        fig, ax = plt.subplots(1, 1, figsize=(16, 10))
        
        # 提取数据
        segments = [d['segment'] for d in segment_data]
        efficiencies = [d['efficiency'] for d in segment_data]
        x_positions = list(range(1, len(segments) + 1))
        
        # 绘制数据点
        ax.scatter(x_positions, efficiencies, color='red', s=150, zorder=5,
                  label='实际数据点', edgecolors='darkred', linewidth=2)
        
        # 拟合曲线
        if len(x_positions) >= 3:
            try:
                from scipy.interpolate import make_interp_spline
                spl = make_interp_spline(x_positions, efficiencies, k=min(3, len(x_positions)-1))
                x_smooth = np.linspace(min(x_positions), max(x_positions), 300)
                y_smooth = spl(x_smooth)
                ax.plot(x_smooth, y_smooth, 'b-', linewidth=3, label='平滑拟合曲线', alpha=0.8)
            except ImportError:
                # 使用多项式拟合
                degree = min(3, len(x_positions) - 1)
                coeffs = np.polyfit(x_positions, efficiencies, degree)
                poly_func = np.poly1d(coeffs)
                x_smooth = np.linspace(min(x_positions), max(x_positions), 200)
                y_smooth = poly_func(x_smooth)
                ax.plot(x_smooth, y_smooth, 'b-', linewidth=3, label='多项式拟合曲线', alpha=0.8)
        
        # 添加效率标注
        for i, data in enumerate(segment_data):
            x_pos = i + 1
            efficiency = data['efficiency']
            ax.annotate(f'{efficiency:.1f}%',
                       xy=(x_pos, efficiency),
                       xytext=(0, 15),
                       textcoords='offset points',
                       fontsize=12,
                       ha='center',
                       va='bottom',
                       fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3',
                               facecolor='yellow',
                               alpha=0.8,
                               edgecolor='orange',
                               linewidth=1))
        
        # 设置坐标轴
        ax.set_xlabel('时间段序号', fontsize=16, fontweight='bold')
        ax.set_ylabel('处理效率 (%)', fontsize=16, fontweight='bold')
        ax.set_title('抽取式吸附曲线 - 分段效率分析（限制95%以下）', fontsize=18, fontweight='bold', pad=20)
        
        # 设置x轴
        ax.set_xticks(x_positions)
        x_labels = []
        for data in segment_data:
            segment = data['segment']
            time_range = f"{data['time_start_str']}-{data['time_end_str']}"
            x_labels.append(f"时段{segment}\n{time_range}")
        ax.set_xticklabels(x_labels, fontsize=11, ha='center')
        ax.set_xlim(0.5, len(segment_data) + 0.5)
        
        # 设置y轴范围，确保不超过95%
        y_min = max(0, min(efficiencies) - 10)
        y_max = min(100, max(efficiencies) + 10)
        ax.set_ylim(y_min, y_max)
        
        # 添加95%参考线
        ax.axhline(y=95, color='red', linestyle='--', alpha=0.7, linewidth=2, label='95%上限')
        
        # 美化
        ax.tick_params(axis='both', which='major', labelsize=12)
        ax.grid(True, alpha=0.4, linestyle='--', linewidth=1)
        ax.legend(fontsize=14, loc='upper right')
        
        for spine in ax.spines.values():
            spine.set_linewidth(1.5)
        
        plt.tight_layout()
        
        # 保存图片
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"抽取式吸附曲线_限制95%_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"\n图片已保存: {filename}")
        
        # 显示统计信息
        print(f"\n效率统计:")
        print(f"最大效率: {max(efficiencies):.1f}%")
        print(f"最小效率: {min(efficiencies):.1f}%")
        print(f"平均效率: {np.mean(efficiencies):.1f}%")
        print(f"超过95%的数据点: {sum(1 for e in efficiencies if e > 95)} 个")
        
        plt.show()
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
