数据库建表语句：



-- 0. 建库（字符集 utf8mb4 防止乱码）
CREATE DATABASE IF NOT EXISTS `hj1307_demo`
  DEFAULT CHARACTER SET utf8mb4
  COLLATE utf8mb4_unicode_ci;
USE `hj1307_demo`;

-- 1. 小时级原始/计算数据表
CREATE TABLE IF NOT EXISTS `incinerator_hourly` (
  id               BIGINT AUTO_INCREMENT PRIMARY KEY,
  plant_code       VARCHAR(32)  NOT NULL COMMENT '焚烧厂唯一编码',
  unit_no          TINYINT      NOT NULL COMMENT '焚烧炉序号 1/2/3...',
  data_time        DATETIME     NOT NULL COMMENT '数据时间(yyyy-mm-dd hh:00:00)',

  -- 炉膛温度 (℃)
  furnace_temp_avg DECIMAL(6,2) COMMENT '炉膛温度 5-min 均值(℃)',

  -- 烟气自动监测 (mg/Nm³, 11%O₂, 干基)
  pm_hourly        DECIMAL(8,2) COMMENT '颗粒物小时均值',
  nox_hourly       DECIMAL(8,2) COMMENT 'NOx 小时均值',
  so2_hourly       DECIMAL(8,2) COMMENT 'SO₂ 小时均值',
  hcl_hourly       DECIMAL(8,2) COMMENT 'HCl 小时均值',
  co_hourly        DECIMAL(8,2) COMMENT 'CO 小时均值',
  o2_hourly        DECIMAL(5,2) COMMENT '含氧量 %',
  flow_hourly      DECIMAL(10,2) COMMENT '湿基流量 m³/h',

  -- 工况标记（枚举：正常运行/启炉/停炉/故障/事故/烘炉/停炉降温/停运）
  status_mark      ENUM('normal','startup','shutdown','fault','accident','warming','cooling','offline')
                   DEFAULT 'normal' COMMENT '自动标记工况',

  -- 环保耗材累计用量 (kg)
  activated_carbon DECIMAL(10,3) COMMENT '活性炭小时累计用量',
  lime             DECIMAL(10,3) COMMENT '石灰(或石灰浆)小时用量',
  ammonia          DECIMAL(10,3) COMMENT '氨水/尿素小时用量',

  -- 炉渣、飞灰产率实时累计 (t)
  slag_hourly      DECIMAL(8,3) COMMENT '炉渣小时累计 t',
  flyash_hourly    DECIMAL(8,3) COMMENT '飞灰小时累计 t',

  -- 渗滤液 (t)
  leachate_hourly  DECIMAL(8,3) COMMENT '渗滤液小时累计 t',

  -- 数据质量标识
  is_valid         TINYINT(1)   DEFAULT 1 COMMENT '0=无效/1=有效',
  mark_reason      VARCHAR(255) COMMENT '人工或系统标记原因',

  -- 索引
  UNIQUE KEY uk_plant_unit_time (plant_code, unit_no, data_time),
  KEY idx_time (data_time),
  KEY idx_status (status_mark)
) ENGINE=InnoDB
  DEFAULT CHARSET = utf8mb4
  COMMENT='HJ1307-2023 小时级数据表';

-- 2. 日汇总表（用于 GB 18485 日均值判定、HJ1307 日均值计算）
CREATE TABLE IF NOT EXISTS `incinerator_daily` (
  id               BIGINT AUTO_INCREMENT PRIMARY KEY,
  plant_code       VARCHAR(32) NOT NULL,
  unit_no          TINYINT     NOT NULL,
  data_date        DATE        NOT NULL,

  -- 日运行时间 (min)
  run_time_min     INT COMMENT '当日非停运时间(min)',

  -- 日均值 (mg/Nm³, 11%O₂)
  pm_daily         DECIMAL(8,2),
  nox_daily        DECIMAL(8,2),
  so2_daily        DECIMAL(8,2),
  hcl_daily        DECIMAL(8,2),
  co_daily         DECIMAL(8,2),

  -- 炉膛温度达标率
  temp_ge850_rate  DECIMAL(5,2) COMMENT '≥850℃ 5-min 均值占比 %',
  temp_lt850_cnt   INT COMMENT '当日低于850℃累计次数',

  -- 环保耗材日用量 (kg)
  activated_carbon_daily DECIMAL(10,3),
  lime_daily             DECIMAL(10,3),
  ammonia_daily          DECIMAL(10,3),

  -- 固废日产量 (t)
  slag_daily       DECIMAL(8,3),
  flyash_daily     DECIMAL(8,3),

  -- 渗滤液日产量 (t)
  leachate_daily   DECIMAL(8,3),

  -- 标记统计
  startup_cnt      TINYINT  DEFAULT 0,
  shutdown_cnt     TINYINT  DEFAULT 0,
  fault_cnt        TINYINT  DEFAULT 0,
  accident_cnt     TINYINT  DEFAULT 0,
  warming_cnt      TINYINT  DEFAULT 0,
  cooling_cnt      TINYINT  DEFAULT 0,
  offline_cnt      TINYINT  DEFAULT 0,

  -- 合规判定结果
  exceed_flag      TINYINT(1) DEFAULT 0 COMMENT '0=未超标/1=任一污染物日均值超标',
  temp_violation   TINYINT(1) DEFAULT 0 COMMENT '0=炉温合规/1=低于850℃累计>5次',

  -- 索引
  UNIQUE KEY uk_plant_unit_date (plant_code, unit_no, data_date),
  KEY idx_date (data_date),
  KEY idx_exceed (exceed_flag)
) ENGINE=InnoDB
  DEFAULT CHARSET = utf8mb4
  COMMENT='HJ1307-2023 日汇总表';

-- 3. 辅助：标准限值配置表（不同地区/时段可不同）
CREATE TABLE IF NOT EXISTS `standard_limits` (
  id        INT AUTO_INCREMENT PRIMARY KEY,
  region    VARCHAR(32)  NOT NULL COMMENT '地区/许可证编号',
  unit_no   TINYINT      NOT NULL COMMENT '炉号',
  pollutant VARCHAR(10)  NOT NULL COMMENT 'PM,NOx,SO2,HCl,CO',
  daily_lim DECIMAL(8,2) NOT NULL COMMENT '日均值限值 mg/Nm³',
  hourly_lim DECIMAL(8,2) NOT NULL COMMENT '小时均值限值 mg/Nm³',
  UNIQUE KEY uk_region_unit_pol (region, unit_no, pollutant)
) ENGINE=InnoDB
  DEFAULT CHARSET = utf8mb4
  COMMENT='GB 18485 及地方标准排放限值配置';

