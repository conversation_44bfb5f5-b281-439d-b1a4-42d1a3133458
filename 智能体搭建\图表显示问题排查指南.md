# 图表显示问题排查指南

## 🚨 当前问题现象
Word文档中图表显示为红色X标记，而不是实际的图表图片。

## 🔍 问题排查步骤

### 第一步：检查工作流接口配置
**这是最关键的检查项！**

#### 检查方法：
1. 打开Dify工作流编辑器
2. 找到 `md_to_word_http_post` 工作流节点
3. 检查HTTP请求配置

#### 正确配置：
```
URL: http://*************:8089/office/word/convert_with_charts
Method: POST
Content-Type: application/json
```

#### 错误配置（导致图表不显示）：
```
URL: http://*************:8089/office/word/convert
```

**如果URL是错误的，这就是问题根源！必须修改为正确的URL。**

### 第二步：检查智能体输出格式
检查智能体是否按照正确格式输出：

#### 正确的输出格式：
```json
{
  "markdown": "包含图表标记的markdown内容",
  "charts": {
    "chart_temperature_trend": { /* ECharts配置 */ },
    "chart_waste_input": { /* ECharts配置 */ },
    "chart_emission_trend": { /* ECharts配置 */ },
    "chart_status_distribution": { /* ECharts配置 */ },
    "chart_material_consumption": { /* ECharts配置 */ },
    "chart_compliance_comparison": { /* ECharts配置 */ },
    "chart_alarm_statistics": { /* ECharts配置 */ }
  }
}
```

#### 检查要点：
1. **JSON格式正确**：包含markdown和charts两个字段
2. **markdown字段包含图表标记**：如 `![炉膛温度变化趋势](chart_temperature_trend)`
3. **charts对象完整**：包含所有7个图表配置
4. **键名一致**：图表标记中的chart_id与charts对象键名完全匹配

### 第三步：检查图表标记插入
在markdown内容中必须包含以下7个图表标记：

```markdown
![炉膛温度变化趋势](chart_temperature_trend)
![垃圾投加量变化](chart_waste_input)
![污染物排放趋势](chart_emission_trend)
![工况分布](chart_status_distribution)
![环保耗材消耗](chart_material_consumption)
![达标情况对比](chart_compliance_comparison)
![预警报警统计](chart_alarm_statistics)
```

**常见错误**：
- ❌ 使用"图表分析"、"如图所示"等文字
- ❌ 只有charts配置，markdown中没有图表标记
- ❌ 图表标记的chart_id与charts对象键名不匹配

### 第四步：检查ECharts配置完整性
每个图表配置必须包含：

```json
{
  "title": { "text": "图表标题" },
  "tooltip": { "trigger": "axis" },
  "xAxis": { "type": "category", "data": [...] },
  "yAxis": { "type": "value" },
  "series": [{ "type": "line/bar", "data": [...] }]
}
```

## 🛠️ 解决方案

### 方案1：修改工作流配置（最重要）
如果工作流URL错误，必须修改：
1. 进入Dify工作流编辑器
2. 找到HTTP请求节点
3. 将URL改为：`http://*************:8089/office/word/convert_with_charts`
4. 保存并发布工作流

### 方案2：强化提示词要求
在提示词中增加更严格的检查要求：
1. 强制要求插入所有7个图表标记
2. 提供具体的插入位置说明
3. 增加输出前的自检清单

### 方案3：测试验证
使用最小测试用例验证：

```json
{
  "markdown": "# 测试报告\n\n![测试图表](chart_test)",
  "charts": {
    "chart_test": {
      "title": { "text": "测试图表" },
      "xAxis": { "type": "category", "data": ["A", "B", "C"] },
      "yAxis": { "type": "value" },
      "series": [{ "type": "bar", "data": [10, 20, 30] }]
    }
  }
}
```

## 📋 问题诊断清单

### 工作流配置检查
- [ ] URL是否为 `/office/word/convert_with_charts`
- [ ] HTTP方法是否为POST
- [ ] Content-Type是否为application/json

### 数据格式检查
- [ ] 输出是否为JSON格式
- [ ] 是否包含markdown字段
- [ ] 是否包含charts字段
- [ ] charts对象是否为空

### 图表标记检查
- [ ] markdown中是否包含图表标记
- [ ] 图表标记语法是否正确：`![标题](chart_id)`
- [ ] 是否包含所有7个必需的图表标记
- [ ] chart_id是否与charts对象键名一致

### 配置对象检查
- [ ] 每个chart_id是否都有对应的ECharts配置
- [ ] ECharts配置格式是否正确
- [ ] 配置是否包含真实数据

## 🎯 最可能的问题原因

根据经验，图表显示为红色X的最常见原因是：

1. **工作流接口错误（90%概率）**：使用了不支持图表的 `/office/word/convert` 接口
2. **图表标记缺失（8%概率）**：markdown中没有插入图表标记
3. **配置对象缺失（2%概率）**：charts对象为空或不完整

## 🔧 快速修复步骤

1. **立即检查工作流URL**：确认是否为 `/office/word/convert_with_charts`
2. **如果URL错误**：立即修改为正确的URL并重新测试
3. **如果URL正确**：检查智能体输出的markdown是否包含图表标记
4. **验证修复**：使用简单的测试用例验证图表是否正常显示

## 📞 技术支持

如果按照以上步骤仍无法解决问题，请提供：
1. 工作流的完整配置截图
2. 智能体的完整输出内容
3. 生成的Word文档截图
4. 服务器端日志信息

这样可以进行更深入的问题诊断。
