{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "lstmWithAttention_multivar_sites.ipynb", "provenance": [{"file_id": "1bE9BR1VLYohcN5Ko1Xfqnulh5XF0Unyu", "timestamp": 1595599435653}, {"file_id": "1IxQcZpOXeCS9CtXRmPjnFz7k5vwDTcqP", "timestamp": 1595516852382}], "collapsed_sections": [], "toc_visible": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "metadata": {"id": "ukOLd5z9o2NQ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 357}, "executionInfo": {"status": "ok", "timestamp": 1598545060435, "user_tz": -60, "elapsed": 776, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "63270e5f-59fc-40f6-e13c-1a6c14c65aac"}, "source": ["  !nvidia-smi"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["Thu Aug 27 16:17:39 2020       \n", "+-----------------------------------------------------------------------------+\n", "| NVIDIA-SMI 450.57       Driver Version: 418.67       CUDA Version: 10.1     |\n", "|-------------------------------+----------------------+----------------------+\n", "| GPU  Name        Persistence-M| Bus-Id        Disp.A | Volatile Uncorr. ECC |\n", "| Fan  Temp  Perf  Pwr:Usage/Cap|         Memory-Usage | GPU-Util  Compute M. |\n", "|                               |                      |               MIG M. |\n", "|===============================+======================+======================|\n", "|   0  Tesla T4            Off  | 00000000:00:04.0 Off |                    0 |\n", "| N/A   64C    P8    11W /  70W |      0MiB / 15079MiB |      0%      Default |\n", "|                               |                      |                 ERR! |\n", "+-------------------------------+----------------------+----------------------+\n", "                                                                               \n", "+-----------------------------------------------------------------------------+\n", "| Processes:                                                                  |\n", "|  GPU   GI   CI        PID   Type   Process name                  GPU Memory |\n", "|        ID   ID                                                   Usage      |\n", "|=============================================================================|\n", "|  No running processes found                                                 |\n", "+-----------------------------------------------------------------------------+\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "AIdpFAYUpDRR", "colab_type": "code", "colab": {}}, "source": ["import pandas as pd\n", "from matplotlib import pyplot as plt\n", "import numpy as np\n", "from sklearn.metrics import mean_squared_error\n", "from sklearn.metrics import mean_absolute_error"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "GdwmDyu4pG2O", "colab_type": "text"}, "source": ["# 导入数据"]}, {"cell_type": "code", "metadata": {"id": "Iib6DHLKDN84", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 54}, "executionInfo": {"status": "ok", "timestamp": 1598545060644, "user_tz": -60, "elapsed": 969, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "34b20fa4-a236-42e8-ae48-33467fd2a6c3"}, "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "rylZ0333POOT", "colab_type": "text"}, "source": ["这次我们准备使用五个监测站的数据。假定Marylebone Road监测站的数据出现异常，此时我们用Bloomsbury, Eltham, Harlington和N_Kensington的数据来推断Marylebone的数据。注意，与之前多变量预测相同，该实验依旧是多变量预测单变量。分多次预测，实现多变量预测多变量。"]}, {"cell_type": "code", "metadata": {"id": "E9MpBLKwpIlm", "colab_type": "code", "colab": {}}, "source": ["Marylebone_Road=pd.read_csv('/content/drive/My Drive/air_inference/data/Marylebone_Road_clean.csv')\n", "Bloomsbury=pd.read_csv('/content/drive/My Drive/air_inference/data/Bloomsbury_clean.csv')\n", "Eltham=pd.read_csv('/content/drive/My Drive/air_inference/data/Eltham_clean.csv')\n", "Harlington=pd.read_csv('/content/drive/My Drive/air_inference/data/Harlington_clean.csv')\n", "N_Kensington=pd.read_csv('/content/drive/My Drive/air_inference/data/N_Kensington_clean.csv')"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "ihnTMkg0pWjr", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 289}, "executionInfo": {"status": "ok", "timestamp": 1598545063059, "user_tz": -60, "elapsed": 3374, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "d01177db-e6a1-43e5-c313-1de5aa5ccc37"}, "source": ["Marylebone_Road.head()"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>site</th>\n", "      <th>code</th>\n", "      <th>date</th>\n", "      <th>nox</th>\n", "      <th>no2</th>\n", "      <th>no</th>\n", "      <th>o3</th>\n", "      <th>pm2.5</th>\n", "      <th>ws</th>\n", "      <th>wd</th>\n", "      <th>air_temp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>London Marylebone Road</td>\n", "      <td>MY1</td>\n", "      <td>2018-01-01 00:00:00</td>\n", "      <td>219.745313</td>\n", "      <td>81.948282</td>\n", "      <td>89.868883</td>\n", "      <td>20.680073</td>\n", "      <td>10.368619</td>\n", "      <td>4.594288</td>\n", "      <td>258.053368</td>\n", "      <td>5.187445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>London Marylebone Road</td>\n", "      <td>MY1</td>\n", "      <td>2018-01-01 01:00:00</td>\n", "      <td>221.095446</td>\n", "      <td>81.975320</td>\n", "      <td>90.731783</td>\n", "      <td>20.623365</td>\n", "      <td>10.251473</td>\n", "      <td>4.602090</td>\n", "      <td>257.695571</td>\n", "      <td>5.219898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>London Marylebone Road</td>\n", "      <td>MY1</td>\n", "      <td>2018-01-01 02:00:00</td>\n", "      <td>222.430163</td>\n", "      <td>82.046845</td>\n", "      <td>91.555615</td>\n", "      <td>20.582777</td>\n", "      <td>10.169084</td>\n", "      <td>4.613062</td>\n", "      <td>257.370167</td>\n", "      <td>5.255796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>London Marylebone Road</td>\n", "      <td>MY1</td>\n", "      <td>2018-01-01 03:00:00</td>\n", "      <td>222.483880</td>\n", "      <td>81.654154</td>\n", "      <td>91.846755</td>\n", "      <td>20.745120</td>\n", "      <td>10.014187</td>\n", "      <td>4.630636</td>\n", "      <td>257.105888</td>\n", "      <td>5.300848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>London Marylebone Road</td>\n", "      <td>MY1</td>\n", "      <td>2018-01-01 04:00:00</td>\n", "      <td>221.593263</td>\n", "      <td>81.130682</td>\n", "      <td>91.607309</td>\n", "      <td>20.959725</td>\n", "      <td>9.813301</td>\n", "      <td>4.661511</td>\n", "      <td>256.906359</td>\n", "      <td>5.350748</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     site code  ...          wd  air_temp\n", "0  London Marylebone Road  MY1  ...  258.053368  5.187445\n", "1  London Marylebone Road  MY1  ...  257.695571  5.219898\n", "2  London Marylebone Road  MY1  ...  257.370167  5.255796\n", "3  London Marylebone Road  MY1  ...  257.105888  5.300848\n", "4  London Marylebone Road  MY1  ...  256.906359  5.350748\n", "\n", "[5 rows x 11 columns]"]}, "metadata": {"tags": []}, "execution_count": 8}]}, {"cell_type": "code", "metadata": {"id": "pQ3uen_qQCza", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 204}, "executionInfo": {"status": "ok", "timestamp": 1598545063060, "user_tz": -60, "elapsed": 3369, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "dce34650-a04a-46f5-f556-e9f43dfe5d7d"}, "source": ["Marylebone_Road=Marylebone_Road[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']]\n", "Bloomsbury=Bloomsbury[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']]\n", "Eltham=Eltham[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']]\n", "Harlington=Harlington[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']]\n", "N_Kensington=N_Kensington[['nox','no2','no','o3','pm2.5','ws','wd','air_temp']]\n", "\n", "dataset=pd.concat([Marylebone_Road,Bloomsbury])\n", "dataset.head()\n"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>nox</th>\n", "      <th>no2</th>\n", "      <th>no</th>\n", "      <th>o3</th>\n", "      <th>pm2.5</th>\n", "      <th>ws</th>\n", "      <th>wd</th>\n", "      <th>air_temp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>219.745313</td>\n", "      <td>81.948282</td>\n", "      <td>89.868883</td>\n", "      <td>20.680073</td>\n", "      <td>10.368619</td>\n", "      <td>4.594288</td>\n", "      <td>258.053368</td>\n", "      <td>5.187445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>221.095446</td>\n", "      <td>81.975320</td>\n", "      <td>90.731783</td>\n", "      <td>20.623365</td>\n", "      <td>10.251473</td>\n", "      <td>4.602090</td>\n", "      <td>257.695571</td>\n", "      <td>5.219898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>222.430163</td>\n", "      <td>82.046845</td>\n", "      <td>91.555615</td>\n", "      <td>20.582777</td>\n", "      <td>10.169084</td>\n", "      <td>4.613062</td>\n", "      <td>257.370167</td>\n", "      <td>5.255796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>222.483880</td>\n", "      <td>81.654154</td>\n", "      <td>91.846755</td>\n", "      <td>20.745120</td>\n", "      <td>10.014187</td>\n", "      <td>4.630636</td>\n", "      <td>257.105888</td>\n", "      <td>5.300848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>221.593263</td>\n", "      <td>81.130682</td>\n", "      <td>91.607309</td>\n", "      <td>20.959725</td>\n", "      <td>9.813301</td>\n", "      <td>4.661511</td>\n", "      <td>256.906359</td>\n", "      <td>5.350748</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          nox        no2         no  ...        ws          wd  air_temp\n", "0  219.745313  81.948282  89.868883  ...  4.594288  258.053368  5.187445\n", "1  221.095446  81.975320  90.731783  ...  4.602090  257.695571  5.219898\n", "2  222.430163  82.046845  91.555615  ...  4.613062  257.370167  5.255796\n", "3  222.483880  81.654154  91.846755  ...  4.630636  257.105888  5.300848\n", "4  221.593263  81.130682  91.607309  ...  4.661511  256.906359  5.350748\n", "\n", "[5 rows x 8 columns]"]}, "metadata": {"tags": []}, "execution_count": 9}]}, {"cell_type": "code", "metadata": {"id": "IKaQ6QfRSmio", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 419}, "executionInfo": {"status": "ok", "timestamp": 1598545063060, "user_tz": -60, "elapsed": 3362, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "9764d2ce-dff7-495a-c4c1-2faab38068ec"}, "source": ["Marylebone_Road\n", "Bloomsbury\n", "<PERSON><PERSON>\n", "Harlington\n", "N_Kensington"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>nox</th>\n", "      <th>no2</th>\n", "      <th>no</th>\n", "      <th>o3</th>\n", "      <th>pm2.5</th>\n", "      <th>ws</th>\n", "      <th>wd</th>\n", "      <th>air_temp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>18.634584</td>\n", "      <td>16.685778</td>\n", "      <td>1.270981</td>\n", "      <td>60.070209</td>\n", "      <td>6.872474</td>\n", "      <td>4.594288</td>\n", "      <td>258.053368</td>\n", "      <td>5.187445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>19.176644</td>\n", "      <td>17.122874</td>\n", "      <td>1.339436</td>\n", "      <td>59.637749</td>\n", "      <td>6.795094</td>\n", "      <td>4.602090</td>\n", "      <td>257.695571</td>\n", "      <td>5.219898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>19.723134</td>\n", "      <td>17.548214</td>\n", "      <td>1.418449</td>\n", "      <td>59.211174</td>\n", "      <td>6.717488</td>\n", "      <td>4.613062</td>\n", "      <td>257.370167</td>\n", "      <td>5.255796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20.133219</td>\n", "      <td>17.844811</td>\n", "      <td>1.492463</td>\n", "      <td>58.913020</td>\n", "      <td>6.621239</td>\n", "      <td>4.630636</td>\n", "      <td>257.105888</td>\n", "      <td>5.300848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>19.865833</td>\n", "      <td>17.747791</td>\n", "      <td>1.381354</td>\n", "      <td>58.671554</td>\n", "      <td>6.515320</td>\n", "      <td>4.661511</td>\n", "      <td>256.906359</td>\n", "      <td>5.350748</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17515</th>\n", "      <td>58.800669</td>\n", "      <td>42.477744</td>\n", "      <td>10.645537</td>\n", "      <td>3.426733</td>\n", "      <td>25.599702</td>\n", "      <td>2.322937</td>\n", "      <td>122.323683</td>\n", "      <td>3.055791</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17516</th>\n", "      <td>58.908322</td>\n", "      <td>42.657677</td>\n", "      <td>10.598397</td>\n", "      <td>3.370981</td>\n", "      <td>25.819725</td>\n", "      <td>2.312240</td>\n", "      <td>120.287580</td>\n", "      <td>3.010667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17517</th>\n", "      <td>59.006263</td>\n", "      <td>42.836211</td>\n", "      <td>10.545836</td>\n", "      <td>3.281494</td>\n", "      <td>26.037294</td>\n", "      <td>2.298849</td>\n", "      <td>118.331870</td>\n", "      <td>2.968728</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17518</th>\n", "      <td>59.081172</td>\n", "      <td>42.984552</td>\n", "      <td>10.497945</td>\n", "      <td>3.231871</td>\n", "      <td>26.253455</td>\n", "      <td>2.292898</td>\n", "      <td>116.428207</td>\n", "      <td>2.931535</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17519</th>\n", "      <td>59.140646</td>\n", "      <td>43.112254</td>\n", "      <td>10.453448</td>\n", "      <td>3.220678</td>\n", "      <td>26.492812</td>\n", "      <td>2.291138</td>\n", "      <td>114.545510</td>\n", "      <td>2.896878</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>17520 rows × 8 columns</p>\n", "</div>"], "text/plain": ["             nox        no2         no  ...        ws          wd  air_temp\n", "0      18.634584  16.685778   1.270981  ...  4.594288  258.053368  5.187445\n", "1      19.176644  17.122874   1.339436  ...  4.602090  257.695571  5.219898\n", "2      19.723134  17.548214   1.418449  ...  4.613062  257.370167  5.255796\n", "3      20.133219  17.844811   1.492463  ...  4.630636  257.105888  5.300848\n", "4      19.865833  17.747791   1.381354  ...  4.661511  256.906359  5.350748\n", "...          ...        ...        ...  ...       ...         ...       ...\n", "17515  58.800669  42.477744  10.645537  ...  2.322937  122.323683  3.055791\n", "17516  58.908322  42.657677  10.598397  ...  2.312240  120.287580  3.010667\n", "17517  59.006263  42.836211  10.545836  ...  2.298849  118.331870  2.968728\n", "17518  59.081172  42.984552  10.497945  ...  2.292898  116.428207  2.931535\n", "17519  59.140646  43.112254  10.453448  ...  2.291138  114.545510  2.896878\n", "\n", "[17520 rows x 8 columns]"]}, "metadata": {"tags": []}, "execution_count": 10}]}, {"cell_type": "markdown", "metadata": {"id": "ageyQmUhU0ko", "colab_type": "text"}, "source": ["先将每个监测站的columns更名。加上后缀为监测站的首字母"]}, {"cell_type": "code", "metadata": {"id": "9zdzb4LqRkp_", "colab_type": "code", "colab": {}}, "source": ["col_<PERSON>lebone=['nox_M','no2_M','no_M','o3_M','pm2.5_M','ws_M','wd_M','air_temp_M']\n", "col_Bloomsbury=['nox_B','no2_B','no_B','o3_B','pm2.5_B','ws_B','wd_B','air_temp_B']\n", "col_Eltham=['nox_E','no2_E','no_E','o3_E','pm2.5_E','ws_E','wd_E','air_temp_E']\n", "col_Harlington=['nox_H','no2_H','no_H','o3_H','pm2.5_H','ws_H','wd_H','air_temp_H']\n", "col_N_Kensington=['nox_N','no2_N','no_N','o3_N','pm2.5_N','ws_N','wd_N','air_temp_N']\n", "\n", "Marylebone_Road.columns=col_Marylebone\n", "Bloomsbury.columns=col_Bloomsbury\n", "Eltham.columns=col_Eltham\n", "Harlington.columns=col_Harlington\n", "N_Kensington.columns=col_N_Kensington"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "UVo7UDMpU8dW", "colab_type": "text"}, "source": ["接下来将所有监测站的数据拼接起来"]}, {"cell_type": "code", "metadata": {"id": "oc3G44CBS4Nh", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 224}, "executionInfo": {"status": "ok", "timestamp": 1598545063061, "user_tz": -60, "elapsed": 3353, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "60ac6b17-4f75-4581-a5a0-5839dd72c5a1"}, "source": ["dataset=Bloomsbury.join(Marylebone_Road)\n", "dataset=dataset.join(Eltham)\n", "dataset=dataset.join(Harlington)\n", "dataset=dataset.join(N_Kensington)\n", "dataset.head()\n"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>nox_B</th>\n", "      <th>no2_B</th>\n", "      <th>no_B</th>\n", "      <th>o3_B</th>\n", "      <th>pm2.5_B</th>\n", "      <th>ws_B</th>\n", "      <th>wd_B</th>\n", "      <th>air_temp_B</th>\n", "      <th>nox_M</th>\n", "      <th>no2_M</th>\n", "      <th>no_M</th>\n", "      <th>o3_M</th>\n", "      <th>pm2.5_M</th>\n", "      <th>ws_M</th>\n", "      <th>wd_M</th>\n", "      <th>air_temp_M</th>\n", "      <th>nox_E</th>\n", "      <th>no2_E</th>\n", "      <th>no_E</th>\n", "      <th>o3_E</th>\n", "      <th>pm2.5_E</th>\n", "      <th>ws_E</th>\n", "      <th>wd_E</th>\n", "      <th>air_temp_E</th>\n", "      <th>nox_H</th>\n", "      <th>no2_H</th>\n", "      <th>no_H</th>\n", "      <th>o3_H</th>\n", "      <th>pm2.5_H</th>\n", "      <th>ws_H</th>\n", "      <th>wd_H</th>\n", "      <th>air_temp_H</th>\n", "      <th>nox_N</th>\n", "      <th>no2_N</th>\n", "      <th>no_N</th>\n", "      <th>o3_N</th>\n", "      <th>pm2.5_N</th>\n", "      <th>ws_N</th>\n", "      <th>wd_N</th>\n", "      <th>air_temp_N</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>38.719371</td>\n", "      <td>27.599582</td>\n", "      <td>7.252141</td>\n", "      <td>47.360318</td>\n", "      <td>7.497625</td>\n", "      <td>4.598855</td>\n", "      <td>257.279906</td>\n", "      <td>5.378717</td>\n", "      <td>219.745313</td>\n", "      <td>81.948282</td>\n", "      <td>89.868883</td>\n", "      <td>20.680073</td>\n", "      <td>10.368619</td>\n", "      <td>4.594288</td>\n", "      <td>258.053368</td>\n", "      <td>5.187445</td>\n", "      <td>10.387007</td>\n", "      <td>7.380368</td>\n", "      <td>1.960881</td>\n", "      <td>59.104229</td>\n", "      <td>10.931926</td>\n", "      <td>4.508552</td>\n", "      <td>256.806923</td>\n", "      <td>5.348268</td>\n", "      <td>25.652376</td>\n", "      <td>17.162654</td>\n", "      <td>5.536853</td>\n", "      <td>58.319162</td>\n", "      <td>4.669857</td>\n", "      <td>6.536290</td>\n", "      <td>254.682024</td>\n", "      <td>4.977652</td>\n", "      <td>18.634584</td>\n", "      <td>16.685778</td>\n", "      <td>1.270981</td>\n", "      <td>60.070209</td>\n", "      <td>6.872474</td>\n", "      <td>4.594288</td>\n", "      <td>258.053368</td>\n", "      <td>5.187445</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>38.976582</td>\n", "      <td>27.836512</td>\n", "      <td>7.265368</td>\n", "      <td>47.042127</td>\n", "      <td>7.449653</td>\n", "      <td>4.603798</td>\n", "      <td>257.009139</td>\n", "      <td>5.412134</td>\n", "      <td>221.095446</td>\n", "      <td>81.975320</td>\n", "      <td>90.731783</td>\n", "      <td>20.623365</td>\n", "      <td>10.251473</td>\n", "      <td>4.602090</td>\n", "      <td>257.695571</td>\n", "      <td>5.219898</td>\n", "      <td>10.823717</td>\n", "      <td>7.652696</td>\n", "      <td>2.068089</td>\n", "      <td>58.843670</td>\n", "      <td>10.720061</td>\n", "      <td>4.498159</td>\n", "      <td>256.500375</td>\n", "      <td>5.379024</td>\n", "      <td>26.603729</td>\n", "      <td>17.836684</td>\n", "      <td>5.717718</td>\n", "      <td>57.659575</td>\n", "      <td>4.672704</td>\n", "      <td>6.554847</td>\n", "      <td>254.312810</td>\n", "      <td>5.012679</td>\n", "      <td>19.176644</td>\n", "      <td>17.122874</td>\n", "      <td>1.339436</td>\n", "      <td>59.637749</td>\n", "      <td>6.795094</td>\n", "      <td>4.602090</td>\n", "      <td>257.695571</td>\n", "      <td>5.219898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>39.251382</td>\n", "      <td>28.072885</td>\n", "      <td>7.290429</td>\n", "      <td>46.715825</td>\n", "      <td>7.416401</td>\n", "      <td>4.621557</td>\n", "      <td>256.762603</td>\n", "      <td>5.453970</td>\n", "      <td>222.430163</td>\n", "      <td>82.046845</td>\n", "      <td>91.555615</td>\n", "      <td>20.582777</td>\n", "      <td>10.169084</td>\n", "      <td>4.613062</td>\n", "      <td>257.370167</td>\n", "      <td>5.255796</td>\n", "      <td>11.238689</td>\n", "      <td>7.897627</td>\n", "      <td>2.178986</td>\n", "      <td>58.579590</td>\n", "      <td>10.543322</td>\n", "      <td>4.510796</td>\n", "      <td>256.218836</td>\n", "      <td>5.420729</td>\n", "      <td>27.500740</td>\n", "      <td>18.461178</td>\n", "      <td>5.895449</td>\n", "      <td>57.038377</td>\n", "      <td>4.669182</td>\n", "      <td>6.579388</td>\n", "      <td>254.014423</td>\n", "      <td>5.051040</td>\n", "      <td>19.723134</td>\n", "      <td>17.548214</td>\n", "      <td>1.418449</td>\n", "      <td>59.211174</td>\n", "      <td>6.717488</td>\n", "      <td>4.613062</td>\n", "      <td>257.370167</td>\n", "      <td>5.255796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>37.985254</td>\n", "      <td>27.997451</td>\n", "      <td>6.513879</td>\n", "      <td>46.400863</td>\n", "      <td>7.358787</td>\n", "      <td>4.636919</td>\n", "      <td>256.538550</td>\n", "      <td>5.502388</td>\n", "      <td>222.483880</td>\n", "      <td>81.654154</td>\n", "      <td>91.846755</td>\n", "      <td>20.745120</td>\n", "      <td>10.014187</td>\n", "      <td>4.630636</td>\n", "      <td>257.105888</td>\n", "      <td>5.300848</td>\n", "      <td>11.546266</td>\n", "      <td>8.070745</td>\n", "      <td>2.266678</td>\n", "      <td>58.360156</td>\n", "      <td>10.361331</td>\n", "      <td>4.524491</td>\n", "      <td>255.957051</td>\n", "      <td>5.467779</td>\n", "      <td>28.379700</td>\n", "      <td>19.068324</td>\n", "      <td>6.072721</td>\n", "      <td>56.435795</td>\n", "      <td>4.654887</td>\n", "      <td>6.636912</td>\n", "      <td>253.861244</td>\n", "      <td>5.099631</td>\n", "      <td>20.133219</td>\n", "      <td>17.844811</td>\n", "      <td>1.492463</td>\n", "      <td>58.913020</td>\n", "      <td>6.621239</td>\n", "      <td>4.630636</td>\n", "      <td>257.105888</td>\n", "      <td>5.300848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>38.973919</td>\n", "      <td>28.512513</td>\n", "      <td>6.822754</td>\n", "      <td>46.033610</td>\n", "      <td>7.302818</td>\n", "      <td>4.658491</td>\n", "      <td>256.342472</td>\n", "      <td>5.554477</td>\n", "      <td>221.593263</td>\n", "      <td>81.130682</td>\n", "      <td>91.607309</td>\n", "      <td>20.959725</td>\n", "      <td>9.813301</td>\n", "      <td>4.661511</td>\n", "      <td>256.906359</td>\n", "      <td>5.350748</td>\n", "      <td>11.794165</td>\n", "      <td>8.191950</td>\n", "      <td>2.349305</td>\n", "      <td>58.183813</td>\n", "      <td>10.154809</td>\n", "      <td>4.533285</td>\n", "      <td>255.699470</td>\n", "      <td>5.517365</td>\n", "      <td>29.266844</td>\n", "      <td>19.683219</td>\n", "      <td>6.250279</td>\n", "      <td>55.827704</td>\n", "      <td>4.636542</td>\n", "      <td>6.716429</td>\n", "      <td>253.903225</td>\n", "      <td>5.156423</td>\n", "      <td>19.865833</td>\n", "      <td>17.747791</td>\n", "      <td>1.381354</td>\n", "      <td>58.671554</td>\n", "      <td>6.515320</td>\n", "      <td>4.661511</td>\n", "      <td>256.906359</td>\n", "      <td>5.350748</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       nox_B      no2_B      no_B  ...      ws_N        wd_N  air_temp_N\n", "0  38.719371  27.599582  7.252141  ...  4.594288  258.053368    5.187445\n", "1  38.976582  27.836512  7.265368  ...  4.602090  257.695571    5.219898\n", "2  39.251382  28.072885  7.290429  ...  4.613062  257.370167    5.255796\n", "3  37.985254  27.997451  6.513879  ...  4.630636  257.105888    5.300848\n", "4  38.973919  28.512513  6.822754  ...  4.661511  256.906359    5.350748\n", "\n", "[5 rows x 40 columns]"]}, "metadata": {"tags": []}, "execution_count": 12}]}, {"cell_type": "markdown", "metadata": {"id": "qsw13S-QuS9t", "colab_type": "text"}, "source": ["# 多站点多变量进行预测"]}, {"cell_type": "code", "metadata": {"id": "3zTUeXsCuZ3V", "colab_type": "code", "colab": {}}, "source": ["var_origin=dataset.values"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "hw0hFQeWup8R", "colab_type": "text"}, "source": ["数据进行归一化操作"]}, {"cell_type": "code", "metadata": {"id": "T9bUOj3purhd", "colab_type": "code", "colab": {}}, "source": ["from sklearn.preprocessing import MinMaxScaler\n", "scaler = MinMaxScaler(feature_range=(0, 1))\n", "scaled = scaler.fit_transform(var_origin)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "k0vvmm5puuTJ", "colab_type": "text"}, "source": ["将数据转为cuda类型"]}, {"cell_type": "code", "metadata": {"id": "5b-DE_7huwo4", "colab_type": "code", "colab": {}}, "source": ["import torch\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "var= torch.FloatTensor(scaled).to(device)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "vAm8BF6Nu3eI", "colab_type": "text"}, "source": ["划分训练集，验证集和测试集"]}, {"cell_type": "code", "metadata": {"id": "S5Z9FaVUu7ji", "colab_type": "code", "colab": {}}, "source": ["def splitData(var,per_val,per_test):\n", "    num_val=int(len(var)*per_val)\n", "    num_test=int(len(var)*per_test)\n", "    train_size=int(len(var)-num_val-num_test)\n", "    train_data=var[0:train_size]\n", "    val_data=var[train_size:train_size+num_val]\n", "    test_data=var[train_size+num_val:train_size+num_val+num_test]\n", "    return train_data,val_data,test_data"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "hAbQ9M0pu-6W", "colab_type": "text"}, "source": ["我们的验证集合测试集都取10%"]}, {"cell_type": "code", "metadata": {"id": "12s54oyKu_qb", "colab_type": "code", "colab": {}}, "source": ["train_data,val_data,test_data=splitData(var,0.1,0.1)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "B9MMKedCvBcQ", "colab_type": "text"}, "source": ["查看长度"]}, {"cell_type": "code", "metadata": {"id": "3CL_EC11vDhf", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1598545076624, "user_tz": -60, "elapsed": 16898, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "9792c70f-ab2d-4c5c-b748-063ba9ee6bd6"}, "source": ["print('The length of train data, validation data and test data are:',len(train_data),',',len(val_data),',',len(test_data))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The length of train data, validation data and test data are: 14016 , 1752 , 1752\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "xM8DYu0RvGSt", "colab_type": "text"}, "source": ["\n", "取一定大小的窗口进行滑动，每个窗口的label值是窗口下一个预测的第一个空气污染物的值"]}, {"cell_type": "code", "metadata": {"id": "Neov5unqwMkx", "colab_type": "code", "colab": {}}, "source": ["train_window = 240\n", "def create_train_sequence(input_data, tw):\n", "    inout_seq = []\n", "    L = len(input_data)\n", "    for i in range(L-tw):\n", "        train_seq = input_data[i:i+tw]\n", "        train_label = input_data[i+tw:i+tw+1]\n", "        inout_seq.append((train_seq ,train_label))\n", "    return inout_seq"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "Nq2VNCQZwQNb", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1598545076625, "user_tz": -60, "elapsed": 16889, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "933c214b-7b3f-41f6-8fb6-eeb122f1fb49"}, "source": ["train_inout_seq = create_train_sequence(train_data, train_window)\n", "print('The total number of train windows is',len(train_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of train windows is 13776\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "SRKEVERCwOQs", "colab_type": "text"}, "source": ["注意，与上面创建train_data的sequence不同，验证集数据(实验是96个验证集数据)只是label。其数据部分还是需要借助于train集中的数据，大小为一个窗口。而这一个窗口的数据并不会在训练过程中被使用"]}, {"cell_type": "code", "metadata": {"id": "pYIJhKWkwWSW", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1598545076904, "user_tz": -60, "elapsed": 17161, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "68eb95e2-cdc9-4415-9857-d41cb43b48e6"}, "source": ["def create_val_sequence(train_data,val_data, tw):\n", "    temp=torch.cat((train_data,val_data))   #先将训练集和测试集合并\n", "    inout_seq = []\n", "    L = len(val_data)\n", "    for i in range(L):\n", "        val_seq = temp[-(train_window+L)+i:-L+i]\n", "        val_label = test_data[i:i+1]\n", "        inout_seq.append((val_seq ,val_label))\n", "\n", "    return inout_seq\n", "\n", "val_inout_seq = create_val_sequence(train_data, val_data,train_window)\n", "print('The total number of validation windows is',len(val_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of validation windows is 1752\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "5Ite_y8OWk4Y", "colab_type": "text"}, "source": [" 此时的label的shape是[1,40]。注意，真正的label只有这40个值中的前五个"]}, {"cell_type": "code", "metadata": {"id": "WodbuYEG6Inl", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1598545076905, "user_tz": -60, "elapsed": 17155, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "d8616bfc-9d56-49f3-9084-c918cfb9b8e3"}, "source": ["val_inout_seq[0][1].shape"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["torch.<PERSON><PERSON>([1, 40])"]}, "metadata": {"tags": []}, "execution_count": 22}]}, {"cell_type": "code", "metadata": {"id": "9Rr2_qTvTKkm", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 34}, "executionInfo": {"status": "ok", "timestamp": 1598545076905, "user_tz": -60, "elapsed": 17148, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "becf8f29-c44a-42a6-b6c5-a66dc066e33d"}, "source": ["def create_test_sequence(train_data,val_data,test_data, tw):\n", "    temp=torch.cat((train_data,val_data))   #先将训练集和测试集合并\n", "    temp=torch.cat((temp,test_data))\n", "    inout_seq = []\n", "    L = len(test_data)\n", "    for i in range(L):\n", "        test_seq = temp[-(train_window+L)+i:-L+i]\n", "        test_label = test_data[i:i+1]\n", "        inout_seq.append((test_seq ,test_label))\n", "\n", "    return inout_seq\n", "\n", "test_inout_seq = create_test_sequence(train_data, val_data, test_data,train_window)\n", "print('The total number of validation windows is',len(val_inout_seq))"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["The total number of validation windows is 1752\n"], "name": "stdout"}]}, {"cell_type": "markdown", "metadata": {"id": "C-1_1zW9wdih", "colab_type": "text"}, "source": ["# 定义LSTM"]}, {"cell_type": "markdown", "metadata": {"id": "sFYIJDyqwhH5", "colab_type": "text"}, "source": ["这次的数据的维度为40维"]}, {"cell_type": "code", "metadata": {"id": "zF00gWiSwgqg", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"status": "ok", "timestamp": 1598545076906, "user_tz": -60, "elapsed": 17142, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "b67f4d8b-1500-40b4-e892-2ebedba5895d"}, "source": ["train_data.shape"], "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<PERSON>.<PERSON><PERSON>([14016, 40])"]}, "metadata": {"tags": []}, "execution_count": 24}]}, {"cell_type": "code", "metadata": {"id": "HhfChsGuobK1", "colab_type": "code", "colab": {}, "executionInfo": {"status": "ok", "timestamp": 1600295112216, "user_tz": -60, "elapsed": 4151, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}}, "source": ["from torch import nn\n", "import torch.nn.functional as F\n", "import torch.nn.init as init\n", "class LSTMwithAttention(nn.Module):\n", "    def __init__(self,input_size=40,hidden_layer_size=50,output_size=1,num_layers=2):\n", "        super().__init__()\n", "        self.input_size=input_size\n", "        self.hidden_layer_size=hidden_layer_size\n", "        self.num_layers=num_layers\n", "        self.batch_size=1    #注意，该实验的batch_size都为1\n", "\n", "\n", "        self.conv1d=nn.Conv1d(in_channels=40,out_channels =20, kernel_size = 2)\n", "        self.lstm=nn.LSTM(20,hidden_layer_size,num_layers,dropout=0.2)\n", "\n", "        self.linear1=nn.Linear(self.hidden_layer_size,self.hidden_layer_size)\n", "        self.linear2=nn.Linear(self.hidden_layer_size,output_size)\n", "        self.linear=nn.Linear(self.hidden_layer_size,output_size)\n", "        init_rnn(self.lstm,'xavier')\n", "        self.dropout=nn.Dropout(0.5)\n", "        self.attn_weights=None\n", "\n", "\n", "    def attention_net(self, lstm_output, final_state):\n", "\n", "        lstm_output = lstm_output.permute(1, 0, 2)\n", "        hidden = final_state.view(-1,self.hidden_layer_size,self.num_layers)   # hidden : [batch_size, n_hidden * num_directions(=2), 1(=n_layer)]\n", "        self.attn_weights = torch.bmm(lstm_output, hidden).squeeze(2) # attn_weights : [batch_size, n_step]\n", "\n", "        soft_attn_weights = torch.tanh(self.attn_weights)\n", "\n", "        context = torch.bmm(lstm_output.transpose(1, 2), soft_attn_weights).squeeze(2)\n", "        return context # context : [batch_size, n_hidden * num_directions(=2)]\n", "\n", "        \n", "    def forward(self,input_seq):\n", "        \n", "        input_seq=input_seq.unsqueeze(0).permute(0,2,1)\n", "        input_seq=self.conv1d(input_seq)\n", "\n", "        input_seq=input_seq.permute(0,2,1)\n", "        lstm_out, self.hidden_cell = self.lstm(input_seq.reshape(len(input_seq[0]),1,20))\n", "        attn_output = self.attention_net(lstm_out,self.hidden_cell[0])\n", "        out=self.linear1(attn_output.view(-1,self.hidden_layer_size))\n", "        out=torch.tanh(out)\n", "        predictions = self.linear2(out)\n", "\n", "        return predictions[-1]\n", "\n", "\n", "#设定初始化\n", "def init_rnn(x, type='uniform'):\n", "    for layer in x._all_weights:\n", "        for w in layer:\n", "            if 'weight' in w:\n", "                if type == 'xavier':\n", "                    init.xavier_normal_(getattr(x, w))\n", "                elif type == 'uniform':\n", "                    stdv = 1.0 / math.sqrt(x.hidden_size)\n", "                    init.uniform_(getattr(x, w), -stdv, stdv)\n", "                elif type == 'normal':\n", "                    stdv = 1.0 / math.sqrt(x.hidden_size)\n", "                    init.normal_(getattr(x, w), .0, stdv)\n", "                else:\n", "                    raise ValueError\n"], "execution_count": 1, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "Y4wFgdDb3yRu", "colab_type": "text"}, "source": ["# 模型训练准备"]}, {"cell_type": "markdown", "metadata": {"id": "khFmQ2awIfht", "colab_type": "text"}, "source": ["与之前一样，分别多nox,no2,no,o3和pm2.5进行预测"]}, {"cell_type": "code", "metadata": {"id": "MNLpUCgxTa9e", "colab_type": "code", "colab": {}}, "source": ["import copy\n", "\n", "epochs=5\n", "\n", "\n", "#为了实现多变量预测多个单变量，我这里用了五个LSTM模型\n", "model_nox=LSTMwithAttention().to(device)\n", "model_no2=LSTMwithAttention().to(device)\n", "model_no=LSTMwithAttention().to(device)\n", "model_o3=LSTMwithAttention().to(device)\n", "model_pm25=LSTMwithAttention().to(device)\n", "\n", "loss_function=nn.MSELoss()\n", "optimizer_nox = torch.optim.SGD(model_nox.parameters(), lr=0.004,momentum=0.2, weight_decay=6e-4)\n", "optimizer_no2 = torch.optim.SGD(model_no2.parameters(), lr=0.004,momentum=0.4, weight_decay=6e-4)\n", "optimizer_no = torch.optim.SGD(model_no.parameters(), lr=0.004,momentum=0.3, weight_decay=6e-4)\n", "optimizer_o3 = torch.optim.SGD(model_o3.parameters(), lr=0.004,momentum=0.2, weight_decay=6e-4)\n", "optimizer_pm25 = torch.optim.SGD(model_pm25.parameters(), lr=0.004,momentum=0.2, weight_decay=6e-4)\n", "\n", "\n", "attr_dic={\n", "    'nox':model_nox,\n", "    'no2':model_no2,\n", "    'no':model_no,\n", "    'o3':model_o3,\n", "    'pm2.5':model_pm25\n", "}\n", "\n", "index_dic={\n", "    'nox':0,\n", "    'no2':1,\n", "    'no':2,\n", "    'o3':3,\n", "    'pm2.5':4\n", "    \n", "}\n", "\n", "optimizer_dic={\n", "    'nox':optimizer_nox,\n", "    'no2':optimizer_no2,\n", "    'no':optimizer_no,\n", "    'o3':optimizer_o3,\n", "    'pm2.5':optimizer_pm25\n", "}\n", "\n", "loss_train_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "loss_val_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "value_train_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n", "\n", "value_val_dic={\n", "    'nox':[],\n", "    'no2':[],\n", "    'no':[],\n", "    'o3':[],\n", "    'pm2.5':[]\n", "}\n"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "yNE6Evl4i_40", "colab_type": "code", "colab": {}}, "source": ["def train_model(attr,model):\n", "  model.train()\n", "  print('训练',attr,'模型')\n", "  for i in range(epochs):\n", "    #train\n", "    add=0\n", "    for seq,label in train_inout_seq:   \n", "        optimizer_dic[attr].zero_grad()\n", "        label=label.to(device)\n", "        model.hidden_cell = (torch.zeros(2, 1, model.hidden_layer_size).to(device),torch.zeros(2, 1, model.hidden_layer_size).to(device))\n", "        \n", "        y_pred = model(seq)\n", "\n", "        if(i==epochs-1):  #对最后一次epoch的值进行记录\n", "          value_train_dic[attr].append(y_pred)\n", "\n", "        single_loss = loss_function(y_pred[0], label[0,index_dic[attr]])   #这里只预测label[attr]的数值，即某个单一的空气污染物\n", "        add+=single_loss\n", "        single_loss .backward()\n", "        optimizer_dic[attr].step()\n", "    loss_train=add/len(train_inout_seq)\n", "    loss_train_dic[attr].append(loss_train)\n", "\n", "\n", "    #val\n", "    add=0 \n", "    t=0\n", "\n", "    val_inputs=train_data[-train_window:]\n", "    fut_pred = len(val_data)\n", "\n", "    for seq,label in val_inout_seq:\n", "      with torch.no_grad():\n", "        seq = val_inputs[-train_window:].to(device)\n", "        label=label.to(device)\n", "        y_pred=model(seq)\n", "        single_loss=loss_function(y_pred[0],label[0,index_dic[attr]])  \n", "\n", "        add+=single_loss\n", "\n", "        if(i==epochs):  #对最后一次epoch的值进行记录\n", "          value_val_dic[attr].append(y_pred)\n", "\n", "        temp=copy.deepcopy(val_data[t])\n", "        temp[index_dic[attr]]=y_pred\n", "        temp=temp.view(1,-1)\n", "        \n", "        val_inputs=torch.cat((val_inputs,temp),0)\n", "        t+=0\n", "\n", "    loss_val=add/len(val_inout_seq)\n", "    loss_val_dic[attr].append(loss_val)\n", "\n", "    print(f'epoch: {i:3}  train_loss:{loss_train:10.8f} val_loss:{loss_val:10.8f}')\n", "  print('----------------------')"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "bTXI3MxItlVd", "colab_type": "code", "colab": {}}, "source": ["def test_model(attr,model):\n", "  temp=torch.cat((train_data,val_data))\n", "  test_inputs=temp[-train_window:,:]\n", "\n", "  fut_pred = len(test_data)\n", "  test_list=[]\n", "  test_results=copy.deepcopy(test_data)\n", "\n", "  # model.eval()\n", "\n", "  for i in range(fut_pred):\n", "      seq = test_inputs[-train_window:].to(device)\n", "      with torch.no_grad():\n", "          model.hidden = (torch.zeros(1, 1, model.hidden_layer_size),\n", "                          torch.zeros(1, 1, model.hidden_layer_size))\n", "          y_pred=model(seq)\n", "          temp=copy.deepcopy(test_data[i]).view(1,-1)\n", "          # temp[index_dic[attr]]=y_pred\n", "          # temp=temp.view(1,-1)\n", "          test_inputs=torch.cat((test_inputs,temp),0)\n", "          test_results[i]=y_pred\n", "\n", "\n", "\n", "\n", "  actual_predictions = scaler.inverse_transform(np.array(test_results.cpu()))\n", "\n", "\n", "\n", "\n", "  x = np.arange(len(train_data)+len(val_data), len(dataset), 1)\n", "  plt.figure(figsize=(8, 6))\n", "  plt.grid(True)\n", "\n", "  plt.plot(dataset.loc[len(dataset)-len(test_data):,attr+'_B'].values,color=\"red\",label='real value')\n", "  plt.plot(actual_predictions[:,index_dic[attr]],label='prediction')\n", "\n", "  plt.title('hours vs '+attr)\n", "  plt.ylabel(attr)\n", "  plt.xlabel('hour')\n", "\n", "  plt.legend(loc='upper right',fontsize=15)\n", "\n", "  y_true=dataset.loc[len(dataset)-len(test_data):,attr+'_B'].values\n", "  y_pred=actual_predictions[:,index_dic[attr]]\n", "\n", "  print('mse: ',mean_squared_error(y_true, y_pred))\n", "  print('mae: ',mean_absolute_error(y_true, y_pred))\n", "\n", "  y_pred=pd.DataFrame(y_pred)\n", "  y_pred.to_csv(\"/content/drive/My Drive/air_inference/result/attention_\"+attr+\".csv\",header = None, index = None)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "0e9ukU-Asayk", "colab_type": "text"}, "source": ["下面的函数用于预测未来96小时的情况"]}, {"cell_type": "code", "metadata": {"id": "ZjdoPJX1sZwV", "colab_type": "code", "colab": {}}, "source": ["def predict_future(attr,model):\n", "  temp=torch.cat((train_data,val_data))\n", "  test_inputs=temp[-train_window:,:]\n", "\n", "  fut_pred = 96\n", "  test_list=[]\n", "  test_results=copy.deepcopy(test_data)\n", "\n", "  model.eval()\n", "\n", "  for i in range(fut_pred):\n", "      seq = test_inputs[-train_window:].to(device)\n", "      with torch.no_grad():\n", "          model.hidden = (torch.zeros(1, 1, model.hidden_layer_size),\n", "                          torch.zeros(1, 1, model.hidden_layer_size))\n", "          y_pred=model(seq)\n", "          temp=copy.deepcopy(test_data[i])\n", "          temp[index_dic[attr]]=y_pred\n", "          temp=temp.view(1,-1)\n", "          test_inputs=torch.cat((test_inputs,temp),0)\n", "          test_results[i]=y_pred\n", "\n", "\n", "\n", "\n", "  actual_predictions = scaler.inverse_transform(np.array(test_results.cpu()))\n", "\n", "\n", "\n", "\n", "\n", "  plt.figure(figsize=(8, 6))\n", "  plt.grid(True)\n", "\n", "  plt.plot(dataset.loc[len(dataset)-len(test_data):len(dataset)-len(test_data)+fut_pred,attr+'_B'].values,color=\"red\",label='real value')\n", "  plt.plot(actual_predictions[:fut_pred,index_dic[attr]],label='prediction')\n", "\n", "  plt.title('hours vs '+attr)\n", "  plt.ylabel(attr)\n", "  plt.xlabel('hour')\n", "\n", "  plt.legend(loc='upper right',fontsize=15)\n", "\n", "  y_true=dataset.loc[len(dataset)-len(test_data):len(dataset)-len(test_data)+fut_pred-1,attr+'_B'].values\n", "  y_pred=actual_predictions[:fut_pred,index_dic[attr]]\n", "\n", "  print('mse: ',mean_squared_error(y_true, y_pred))\n", "  print('mae: ',mean_absolute_error(y_true, y_pred))\n", "\n", "  y_pred=pd.DataFrame(y_pred)\n", "\n", "  y_pred.to_csv('/content/drive/My Drive/air_inference/result24/attention'+attr+'.csv',index=False)"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "7seFmeq0OXZc", "colab_type": "text"}, "source": ["# 开始训练"]}, {"cell_type": "code", "metadata": {"id": "dcEij6uAMKg5", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 170}, "executionInfo": {"status": "ok", "timestamp": 1598553191062, "user_tz": -60, "elapsed": 964459, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "d1da5670-d6bf-4d14-ac10-5902e12c22c1"}, "source": ["%%time\n", "train_model('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 nox 模型\n", "epoch:   0  train_loss:0.00034260 val_loss:0.05305503\n", "epoch:   1  train_loss:0.00031121 val_loss:0.05249328\n", "epoch:   2  train_loss:0.00028994 val_loss:0.05228006\n", "epoch:   3  train_loss:0.00026491 val_loss:0.05245958\n", "epoch:   4  train_loss:0.00023687 val_loss:0.05222928\n", "----------------------\n", "CPU times: user 20min 36s, sys: 6.42 s, total: 20min 43s\n", "Wall time: 20min 47s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "RRsvxtbiuDaF", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598553204471, "user_tz": -60, "elapsed": 13421, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "af1345a7-efbc-4a58-f4d0-6c1831161b7e"}, "source": ["test_model('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  515.0450108045877\n", "mae:  18.788251828061007\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "X6kn4OmhsiOb", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598553205105, "user_tz": -60, "elapsed": 638, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "ebb6c4eb-fa2c-4d09-8991-fe7acb099687"}, "source": ["predict_future('nox',model_nox)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  572.6265101794039\n", "mae:  18.763797274980625\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "gvn89ILdjgYX", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 170}, "executionInfo": {"status": "ok", "timestamp": 1598549208710, "user_tz": -60, "elapsed": 77, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "46757862-b77c-4e8d-d4e8-3472d4740f33"}, "source": ["%%time\n", "train_model('no2',model_no2)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 no2 模型\n", "epoch:   0  train_loss:0.00040018 val_loss:0.08028214\n", "epoch:   1  train_loss:0.00026284 val_loss:0.07468236\n", "epoch:   2  train_loss:0.00020007 val_loss:0.07320442\n", "epoch:   3  train_loss:0.00017805 val_loss:0.07054795\n", "epoch:   4  train_loss:0.00020342 val_loss:0.06984508\n", "----------------------\n", "CPU times: user 20min 51s, sys: 6.25 s, total: 20min 58s\n", "Wall time: 21min 2s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "uiobF6CHvCYW", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598549208713, "user_tz": -60, "elapsed": 71, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "092638a1-efb3-4044-ea3c-a7d4f2ddcfda"}, "source": ["test_model('no2',model_no2)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  167.1912215853781\n", "mae:  11.319692062686451\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "vPsE5qYGs_Xp", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598549208716, "user_tz": -60, "elapsed": 64, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "835e2553-b503-4fda-ecac-cb137b9e3162"}, "source": ["predict_future('no2',model_no2)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  44.573215462171476\n", "mae:  6.2045390449292634\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "5yEx1bQSLxJu", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 170}, "executionInfo": {"status": "ok", "timestamp": 1598554451085, "user_tz": -60, "elapsed": 524819, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "f3a11b82-dd70-4ea9-de93-1eb72af37ef4"}, "source": ["%%time\n", "train_model('no',model_no)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 no 模型\n", "epoch:   0  train_loss:0.00021928 val_loss:0.03156358\n", "epoch:   1  train_loss:0.00021559 val_loss:0.03140092\n", "epoch:   2  train_loss:0.00021388 val_loss:0.03153671\n", "epoch:   3  train_loss:0.00020603 val_loss:0.03133287\n", "epoch:   4  train_loss:0.00019807 val_loss:0.03124058\n", "----------------------\n", "CPU times: user 20min 34s, sys: 6.53 s, total: 20min 41s\n", "Wall time: 20min 46s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "7xIGo5JwP5eQ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598554464428, "user_tz": -60, "elapsed": 13356, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "913bb12e-49fc-4627-eb24-9f9738fb96fe"}, "source": ["test_model('no',model_no)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  117.91268963591345\n", "mae:  8.726190519308876\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "dW9_jy_ctCld", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598554465071, "user_tz": -60, "elapsed": 647, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "f91e6865-209a-4095-e882-538b85fe931e"}, "source": ["predict_future('no',model_no)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  167.71104172033515\n", "mae:  10.37248849574092\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "x2tnzs_tQBu1", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 68}, "outputId": "ec1b1848-9cc0-4607-ac4f-ce9ad2abf883"}, "source": ["%%time\n", "train_model('o3',model_o3)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 o3 模型\n", "epoch:   0  train_loss:0.00020181 val_loss:0.07171986\n", "epoch:   1  train_loss:0.00022290 val_loss:0.07398169\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "13s3RdWuULJ3", "colab_type": "code", "colab": {}}, "source": ["test_model('o3',model_o3)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "C50wUN7ptFFF", "colab_type": "code", "colab": {}}, "source": ["predict_future('o3',model_o3)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "4gC2bva0UT_m", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 170}, "executionInfo": {"status": "ok", "timestamp": 1598551470159, "user_tz": -60, "elapsed": 1261970, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "085fa087-075d-4bc1-e800-1a078da81430"}, "source": ["%%time\n", "train_model('pm2.5',model_pm25)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["训练 pm2.5 模型\n", "epoch:   0  train_loss:0.00047169 val_loss:0.02479235\n", "epoch:   1  train_loss:0.00028211 val_loss:0.02550243\n", "epoch:   2  train_loss:0.00019098 val_loss:0.02392089\n", "epoch:   3  train_loss:0.00012745 val_loss:0.02500652\n", "epoch:   4  train_loss:0.00011120 val_loss:0.02553888\n", "----------------------\n", "CPU times: user 20min 50s, sys: 6.33 s, total: 20min 57s\n", "Wall time: 21min 2s\n"], "name": "stdout"}]}, {"cell_type": "code", "metadata": {"id": "lf7IELqHYaEJ", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598551483969, "user_tz": -60, "elapsed": 13814, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "9fd73761-566c-4bed-ceb0-62ebdcd613d4"}, "source": ["test_model('pm2.5',model_pm25)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  7.5193666373158665\n", "mae:  2.3347578356197425\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "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******************************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\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "Tn-WSf9VtHWs", "colab_type": "code", "colab": {"base_uri": "https://localhost:8080/", "height": 438}, "executionInfo": {"status": "ok", "timestamp": 1598551484638, "user_tz": -60, "elapsed": 673, "user": {"displayName": "<PERSON><PERSON>", "photoUrl": "", "userId": "03419135428294618807"}}, "outputId": "2c2c1e25-a77c-47b1-a045-0815497c3f20"}, "source": ["predict_future('pm2.5',model_pm25)"], "execution_count": null, "outputs": [{"output_type": "stream", "text": ["mse:  3.9041684242596566\n", "mae:  1.860136937246324\n"], "name": "stdout"}, {"output_type": "display_data", "data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAfQAAAGDCAYAAADd8eLzAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4yLjIsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+WH4yJAAAgAElEQVR4nOzdd1zV1f/A8ddhbxQRUFDBiXuAWqnlyMyy5c8cLc3ULLNsuNKyXWapmZnl1tyWmWbZt9KWlltx50RxICrIHvee3x8fNFRk3suFy/v5ePDo3s/9fM7nzQl5c87nDKW1RgghhBBlm4OtAxBCCCFE8UlCF0IIIeyAJHQhhBDCDkhCF0IIIeyAJHQhhBDCDkhCF0IIIeyAJHQhSoBS6rhS6k5bxyGEsF+S0IUQpYZS6l6l1J9KqXil1Fml1EyllHce5x9XSqUqpZKyv34qyXiFKE0koQthR5RSTraOoZh8gXeAqkB9IBiYkM8192mtvbK/7rJ2gEKUVpLQhSg5zZRSu5VSCUqppUoptysfKKUGKqUOK6UuKqW+U0pVzT4eqpTSORO1UmqDUmpA9ut+Sqm/lFKTlFIXgDeUUrWVUr9l3ydOKbU0t2CUUj8opZ677tgupVR3ZZiklIpVSl1WSkUppRrdpJwNSqn3lVKbs89dpZTyuy7+J5VSJ5VSl5RSg5VSLbPrIl4pNfVKWVrrRVrrH7XWKVrrS8AMoE2Ra1yIckQSuhAlpydwNxAGNAH6ASilOgLvZ39eBTgBLClEua2Bo0Ag8C7wNvATUBEIAT69yXWLgT5X3iilGgA1gO+Bu4DbgboYreaewIU8YngC6J8dfxYwJZcY6wC9gMnAGOBOoCHQUyl1x03KvR3Ym8d9ARYqpc4rpX5SSjXN51wh7JYkdCFKzhSt9Wmt9UVgNdAs+/ijwGyt9XatdTowGrhVKRVawHJPa60/1Vpnaa1TgUyMxFxVa52mtf7zJtetxOg1qJEjjm+yY8gEvIFwQGmt92utz+QRwwKt9R6tdTLwGkaSdszx+dvZsfwEJAOLtdaxWusY4A+g+fUFKqU6A32B1/O476NAaPb3ux5Yp5SqkMf5QtgtSehClJyzOV6nAF7Zr6titMoB0FonYbSGgwtY7snr3o8AFLBZKbVXKdU/t4u01okYrfHe2Yf6AAuzP/sVmAp8BsQqpb5USvkUMIYTgDPgn+PYuRyvU3N575XjPUqpW4BFQA+t9aGb3VRr/ZfWOjW7i/59IB5ol0ecQtgtSehC2N5pjBYmAEopT6ASEIPRmgXwyHF+0HXXX7Nlotb6rNZ6oNa6KvA0ME0pVfsm914M9FFK3Qq4YbRyr5QzRWsdATTA6Hofnsf3UC3H6+oYLfy4PM6/KaVUc+A7oL/W+pdCXq4x/pgRotyRhC6E7S0GnlRKNVNKuQLvAf9orY9rrc9jJPbHlFKO2a3tWnkVppR6WCkVkv32EkaSM9/k9LUYf0y8BSzVWpuzy2iplGqtlHLG+KMiLY8yyI6vgVLKI7usFVprUwG+9+tjbwT8CAzVWq/O59zqSqk2SikXpZSbUmo4Rq/AX4W9rxD2QBK6EDamtf4Z47nz18AZjITdO8cpAzFaxxcwBpFtzKfIlsA/SqkkjJbuC1rroze5dzrwDcYAtUU5PvLBGGF+CaML/QJ5Tx9bAMzFeKzgBjyfT4w38zJQGZiVY2751UFxSqnpSqnp2W+9gc+zY4zBGHDYVWud1+A9IeyW0lrnf5YQQtyEUmoD8JXWeqatYxGiPJMWuhBCCGEHJKELIYQQdkC63IUQQgg7IC10IYQQwg5IQhdCCCHsQJnYmcnf31+HhoZarLzk5GQ8PT0tVp6QOrU0qU/Lkzq1LKlPy8tZp9u2bYvTWlcuzPVlIqGHhoaydetWi5W3YcMG2rdvb7HyhNSppUl9Wp7UqWVJfVpezjpVSp3I++wbSZe7EEIIYQckoQshhBB2QBK6EEIIYQckoQshhBB2QBK6EEIIYQfKxCh3IYSwN5cvXyY2NpbMzExbh1Ikvr6+7N+/39ZhlEnOzs4EBATg4+Nj0XIloQshRAm7fPky586dIzg4GHd3d5RStg6p0BITE/H29rZ1GGWO1prU1FRiYmIALJrUpctdCCFKWGxsLMHBwXh4eJTJZC6KTimFh4cHwcHBxMbGWrRsSehCCFHCMjMzcXd3t3UYwobc3d0t/rhFEroQQtiAtMzLN2v8/5eELoQQQtgBGRQnhBCi9NMaUlPBZAIXF3B2Bgdpk+YkCV0IIYTtmM2QkfHfF4CDAxs2bqTDffcRtX49jYKDISnJSOY5OTuDuzv4+UHFiuDoWKQQjh8/TlhYGKtXr6Zbt27F/IZsRxK6EEKIkpWVBefOwYUL/yXx62VP6+LsWfD3NxK2tzc4OV37B0ByMhw/DidOGOdUqmScVw5b75LQhRBCFJvJZMJkMuHi4pLXSRAbayRpkwl8fY0E7OpqfF251myG8+eN1/XqQaNGNy9TayOpX7gAly7BxYugFHh5GYnd29t4XQ4GIZa/P2GEEEIU2+DBg4mMjOTbb7+lYcOGuLm58c8//wCwatUqIiMjcXNzIygoiBHDh5N56hRERUFMDAdiY+k9fjzVOnbEo04dGt5xB5NnzcLs7Gwkdnd34wuMbvVcJCcn4+npyWfTphkJu0YNaNIE6tShZf/+PPbKK3D6NGf++ov+Dz1EzerVcXd3p27duowdO5aMm/UMZFNKMXXq1GuOvfHGG/j7+19zLDo6mt69e+Pn54eHhwddunTh4MGDRazV4pGELoQQokiOHz/OiBEjGD16ND/88ANhYWEsW7aM7t2706pVK75btYpxw4fz5RdfMHrUKCNJh4cT4+REvQYNmDZtGmvXrmXgwIGMGzeO8ePHF/jenp6edOvWjWXLlv130MGBoxcusHX3bno//TQ0a0actzd+/v5MfOEFfpw8meG9ezNnxgyG9usHp08bPQHJyUX6/i9evEjbtm05ePAg06dPZ9myZSQnJ3PnnXeSmppapDKLQ7rchRCiNBg2DHbutM29mzWDyZMLfdmFCxf4+eefadasGWAsazp8+HCeeOIJpn30EZw8CZUq4Tp8OEPee4/RkydTycuLTp060alTp6vXtG3blpSUFGbMmMHo0aMLfP/evXvTo0cPTp8+TdWqVQFYunQpFStWpEuXLuDkROM2bfioTRujGz8hgTbnz+Pp5UX/117j06FDcXF2NhI7GF32WVnGc/oCmDRpEsnJyezcuRM/Pz8A2rRpQ2hoKLNnz2bIkCEF/l4sQVroQgghiiQ4OPhqMgc4dOgQ0dHR9OzalayoKLISE8mqWpWOjz1GWloae/buBSAtLY1x48ZRu3ZtXF1dcXZ2ZsyYMRw7doysrKwC379r1654eXmxfPnyq8eWLl3KQw89hHN2V73WmsmTJ9OgUSPcq1bFuV49Hh05kvSMDKIrVYLGjSEkxLg4Lg527YKjR8m+OM/7//zzz3Tu3BkfHx+ysrLIysrC29ubiIgItm7dWuDvw1KkhS6EEKVBEVrIthYYGHjN+7i4OADu6dUr1/NPnjwJwMiRI5k5cybjxo2jRYsWVKhQgVWrVvHOO++QlpaGl5dXge7v5ubGAw88wNKlS3nhhRc4ePAgu3btYsKECVfPmTx5MsOHD2fkyJHccccdVKxYkS1btjBkyBDSMjKMZ/bZrWuqVzdG1F+8aLw/dcpI7hUrQoUKN9w/Li6Ov//+m6VLl97w2ZUeiJIkCV0IIUSRXLN8qdb4Zbeuv3z7bZp37nzDvPCwsDAAli9fztChQxkxYsTVz77//vsixdCrVy/uu+8+oqOjWbp0KZUrV6Zjx45XP1++fDk9evTg3XffvXps3759uRfm6moMrqtWDVdXVzKcneHyZSPBe3hw6brNVPz8/Lj//vt57bXXbijKFjvRSUIXQghRPFrDyZPUc3cnOCiI4ykpDGzd+qanp6am4urqevW9yWRiyZIlRbr1XXfdRYUKFVi2bBlLly6lR48eOOb4Q+L6ewEsXLgw70IdHAgJCWH/uXPGyPn4eMwnTvDLunXGs/js5+ydOnVi2bJlNGzYsFRstiMJXQghRNFpDdHRcP48DlWq8PGkSTz+xBNcTkyka9euuLi4cPToUb799ltWrFiBh4cHnTt35rPPPqN27dr4+fnx2WefkZ6eXqTbOzs70717dyZOnMiZM2eYNm3aNZ937tyZKVOm0Lp1a2rVqsXChQs5fPhwvuU+9NBDfPbZZzRv3pyaNWsyc8YMLqelGQl9714IDOSlIUP46quv6NixI0OHDiU4OJhz587x22+/0bZtW/r06VOk76moJKELIYQoupMnjalfQUEQHEyv3r3x8fXlvffeY/bs2Tg6OlKzZk26det2ddGZTz/9lMGDBzNkyBDc3d3p27cvDz30EIMGDSpSCL1792bWrFlUrVqVdu3aXfPZ66+/zvnz5xk7diwA3bt3Z8qUKdx33315ljlu3DhiY2MZO3YsLi4uPPfcczRs1IjPpk41FsA5dQp/4O958xgzfTovDhtGfEICVapUoW3btjRp0qRI30txKJ3PKL7SIDIyUltyxOCGDRto3769xcoTUqeWJvVpeaWpTvfv30/9+vVtHUaxJF6+jHdCgrGEa2CgMVK8HKzGdlVaGsTHG19JSeDmlveKdrm4/ucg58+oUmqb1jqyMOVZrYWulJoNdANitdaNso81A6YDbkAW8KzWerO1YhBCCGEFWuMSF2cMFgsIKH/JHIwEHhRkfGVm3nxN+hJkzXnoc4G7rzv2IfCm1roZ8Hr2eyGEEKWd1saI7+hoiIrC9eJFqFwZqlUrf8n8es7O4Olp6yis10LXWv+ulAq9/jDgk/3aFzhtrfsLIYSwgJQU4xn5lVXUlAIfH1L9/HAPDpZkXopY9Rl6dkJfk6PLvT6wDlAYvQO3aa1P3OTaQcAggMDAwIiiTmnITVJSUoEXLhAFI3VqWVKfllea6tTX15fatWvbOoybM5txSkzEOT4ep7Q0tFJkeXmR5e1NlqcnODhgMpmumR4mCu/w4cMkJCRcfZ/zZ7RDhw6l5xn6TTwDvKi1/lop1ROYBdyZ24la6y+BL8EYFGfJwSylaXCMvZA6tSypT8srTXW6f/9+myw8UiApKcb+4unpxnPiatVQlSrh7OREzn3PEhMTS+/3UEa4ubnRvHnzq++L+zNa0gm9L/BC9uvlwMwSvr8QQoibuXABTpwwVnirUwd8fKRLvQwp6c1ZTgN3ZL/uCPxbwvcXQghxveyV3jh2DDw8oEED8PWVZF7GWHPa2mKgPeCvlDoFjAMGAp8opZyANLKfkQshhLARkwkOH4bExP+moDnIRpxlkTVHud9szbsIa91TCCFEIZjN/yXz0FBjpzFRZsmfYUIIUR6ZzXDkiJHMw8LKRDKfOnXqNTu8bdiwAaUUe/bsKXAZX375Jd9+++0Nx0NDQ3nllVcsEqetyFruQghR3mht7POdkGBsF1qpkq0jKpIWLVqwadMmatWqVeBrvvzySxo1asSDDz54zfGVK1dSqYzWwxWS0IUQojzR2hj8Fh9vrPJWuXKJ3To1NdWi24z6+Phwyy23WKSsnNPHyirpchdCiPIiNRX27zfWYA8ONjZVKaLBgwcTGRnJt99+S3h4OG5ubrRt25Z9+/ZdPUcpxcSJExk2bBiVK1emcePGAKSlpTFixAiqVauGq6srTZs2Ze3atdeUn56eznPPPUeFChXw8/PjxRdfJDMz85pzcutyN5lMvP/++9StWxdXV1dCQkLo168fAO3bt2fbtm3MmzcPpRRKKebOnQvk3uW+bNkyGjdujKurK9WqVWPMmDFkZWVd/Xzu3LkopYiKiqJz5854enoSHh7ON998U+R6LQ5J6EIIYe+0hrNnYd8+YxORmjWhSpViF3vixAleeuklXnvtNRYtWkRCQgJdunQhLS3t6jkTJkzgzJkzLFiwgClTpgDQo0cP5s6dy6uvvsrq1atp2bIl999/Pzt37rx63ahRo5g5cyavvfYaCxcu5MSJE3z88cf5xvT0008zbtw4evbsyZo1a/j4449JSUkBYNq0aYSHh3PPPfewadMmNm3axL333ptrOT/99BO9evWiRYsWrFq1iqFDh/LRRx/x3HPP3XDuI488wv3338/KlSupU6cOvXv35tSpU4WqS0uQLnchhCgF3ly9l32nL1u+YG2G1DRjepqTk7H62z8HrzmlQVUfxt3XsNBFx8XFsWrVKm677TYAIiIiqFWrFnPnzmXw4MEAVKlShaVLl1695pdffuH7779nw4YN3HGHsSzJXXfdxaFDh3j33XdZvnw5Fy5cYPr06bz55pu8/PLLAHTp0oUGDRrkGc+BAweYNWsWn3zyCc8///zV47169TK+zwYN8PT0pHLlyvl21b/++uu0b9+eefPmAXD33cZeY6NHj2bs2LGEhIRcPffFF1+kf//+V+sgMDCQNWvWXK2DkiItdCGEsFdms7GUq9kM7m7g7m7RxWICAgKuJnOAGjVqEBERwebN/+2Kfc8991xzzc8//0xQUBBt2rQhKyvr6lenTp3YunUrAFFRUaSlpfHAAw9cvc7BweGa97lZv349wNUu9qIymUxs376dhx9++JrjvXr1wmw2s2nTpmuO33XXXVdfV6pUiYCAAGmhCyFEeVWUFnKekpLg33/BwQvq1jWSuYUFBATkeuzMmTNX3wde95w+Li6Os2fP4uzsfP2lVzd7OXv2bK7l53a/nC5cuICnpyc+Pj55npefuLg4MjMzr8autSYjy4yrd0UADp44w/nE9KvnV6hQ4ZrrXVxcrnnsUFIkoQshhL25fNlYMMbZ2Ujmrq5WuU1sbGyuxxo2/O+PE3Vdj4Cfnx/BwcG5zgW/Iigo6GpZfn5+ed4vp0qVKpGcnMzly5eLldQrVPTD2dmZwydiOHo+idRMEyaz5vSpaAB8fCuUylVxpctdCCHsSXy80TJ3dYV69ayWzMFIsBs3brz6Pjo6mu3bt9OqVaubXtOpUyfOnj2Ll5cXkZGRN3wBNG7cGDc3N1atWnX1OrPZfM373HTs2BGA+fPn3/Sc3FrPmSYzZg1JaVkcOpvIwdhk6jduxtdfr8Bk1vi6OxNS0Z3t63/AwcGB7l074u9lvXotKmmhCyGEvUhLMxaMcXc3WuZO1v0V7+/vz2OPPcY777yDu7s748aNIyAgIM9n2J07d6ZLly507tyZkSNH0rBhQy5fvszOnTtJS0vj/fffp1KlSgwaNIhx48bh5OREw4YNmTFjBklJSXnGU69ePQYNGsTLL79MbGwst99+O/Hx8axYsYIlS5YAEB4ezrp161i5+nvcvXypGBSCu3cFTGYzaVkmnJ0cqODpzFtvvsmD993De6Oep3fv3kRFRfHWm+MYOHDgNQPiShNJ6EIIYQ/MZiOZKwW1alk9mYMxCO7VV19l1KhRnDhxgsjISBYtWoSbm9tNr1FK8c033/Dee+8xefJkoqOj8fPzo1mzZgwdOvTqeR9++CGZmZm89dZbODg48Nhjj/HSSy9dHfV+M9OmTaNGjRrMnDmTDz74gICAAO666y6yTGYup2XR79mX2PfvUZ54tA9JiYmMn/I5/fr1w8nBgUqeLoT5ewLwQLeuLFmyhHfeeYeFCxcSEBDAyy+/zJtvvmmZyrMCpbW2dQz5ioyM1FdGP1pCcTeRFzeSOrUsqU/LK011un//furXr2/ZQk+ehHPnjGResaJly87Fo48+ysGDB7Hk72ZLyzKZSUjNJCE1k+T0LDTg4uiAj7szXq5OeLo64ehgu4fh1/8c5PwZVUpt01pHFqY8aaELIURZFx9vJPOAgBJJ5qWZ1pqUDBMXkzNISM3ErDWuTo5U9nbD190JN2fHGwbq2QtJ6EIIUZZlZMDx4+DhYexlXk6ZtSY+JZO4xHTSskw4KkVFD2f8PF1wdykfqa58fJdCCGGPruyaZjYby7k6lNzEpenTp+Pt7V1i97sZk1lzKTmD80npZJrMuDs7ElLRA193Z5t2p9uCJHQhhCirYmKMBWTCwowlXcsRs1lzITmd84kZZJnNeLo6EVLRHS9XJ7vtUs+PJHQhhCiL4uONDVcqVy6z+5kXhdaaSykZnLtstMi93ZwJ8PbA01XSmdSAEELYgNa66C3J9HRjT3MPD2NP83JAa83ltCzOJqSRnmXCw8WJan4eeJXRRG6NGWZlsyaEEKIMc3Z2JjU1FQ8Pj8JffGW+OZT4c3NbScs0cTo+laT0LFydHKlRyRMft7LdtZ6amprrevbFIQldCCFKWEBAADExMQQHB+Pu7l64xHTqFCQnG/PN7fy5ucmsiU1MIy4pAwcFVSu4U8nTpUwncq01qampxMTE3LBxTXFJQhdCiBJ2ZeOQ06dPk5mZWfALU1Lg/Hnw9jaen2fvSmYLaWlpea4IV1ypmSbiUzIxmTWero74uDlzPkFx3mp3LDnOzs4EBgYWe1e460lCF0IIG/Dx8SncL/SYGGjbFkJDYdMmcHGxWmwFsWHDBpo3b27xcuOS0nnju72s2X2G8CBv3uvemBbVy/diOQUlCV0IIUo7kwkef9wYDLd4sc2TuTVorVm5I4a31uwjJd3Ey53r8vQdtXBxsv8xApYiCV0IIUq7Dz+E9eth9mxjFzU7c+5yGqO/ieLXA7FE1KjI+P9rTO0A2y9aU9ZIQhdCiNLsn3/gtdegVy/IY1vSsuhKq/yN7/aSYTLzWrcG9LsttNyt8GYpktCFEKK0unwZ+vQx1mifPt3YGtVOxF5O49WVe/h5/zkia1RkwsNNr25dKopGEroQQpRGaWnw4IMQHQ2//w4VKtg6IovQWrN0y0neXbufjCwzY++tz5NtwqRVbgGS0IUQorTJzDS62NevhwUL4LbbbB2RRRyPS2b0N1FsOnqBW2r68X73JtIqtyBJ6EIIUZqYzfDkk/DddzB1Kjz2mK0jKraMLDMz/zzKJz//i4uTA+93b0yvyGo4SKvcoiShCyFEaaE1DB0KCxfCu+/CkCG2jqjYNh25wGur9nA4NokuDQN564FGBPrY9wp3tiIJXQghSouPPoJp02D4cBg92tbRFMv5xHTeW7uflTtiqObnzux+kXQMt+xSp+JaktCFEKI0OHUK3njDGAg3fnyZHdGelmli9l/HmLb+CBlZZoZ2rM2z7Wvj7uJo69DsniR0IYQoDV591VgRbuLEMpnMzWbNd7tOM2HdQWLiU7mzfiCv3hNOzcpetg6t3JCELoQQtrZ5szGaffRoCAuzdTSFtuX4Rd5es4/dpxJoFOzDRw835dZalWwdVrkjCV0IIWxJa3jxRQgMLHPPzWPiU3l/7X7W7D5DFV83JvZsyoPNgmX0uo1IQhdCCFtatgw2boSZM41tUcuAxLRMVv6bwY8/b0ApeKFTHZ6+oyYeLpJSbMlqta+Umg10A2K11o1yHB8KDAFMwPda6xHWikEIIUq11FQYMQKaNSsT67THJqYx56/jfLXpBInpWdzXtCqjuoYTXMHd1qEJrNtCnwtMBeZfOaCU6gA8ADTVWqcrpQKseH8hhCjdJk40lnadNw8cS+8o8ONxyXzx+1G+3n6KLJOZro2q0NLrEv0esPx+6KLorJbQtda/K6VCrzv8DPCB1jo9+5xYa91fCCFKtYMH4Z13oHt3aN/e1tHkat/py0zbcJi1UWdwcnSgR0QIg9rVJNTfkw0bNtg6PHGdkn7gURdop5R6F0gDXtFabynhGIQQwrZMJqOL3d3dWN61lNl24iJTfz3M+oPn8XJ1YuDtNXmqTRgBssJbqaa01tYr3Gihr7nyDF0ptQdYDzwPtASWAjV1LkEopQYBgwACAwMjlixZYrG4kpKS8PKSuZGWJHVqWVKfllea6rTa4sXU+vJL9o0ZQ+ydd9o6nKuOxJtYeTiTPXEmvJ2hc6gznao74+l846j10lSf9iJnnXbo0GGb1jqyMNeXdEL/ERivtV6f/f4IcIvW+nxe5URGRuqtW7daLK4NGzbQvpR2cZVVUqeWJfVpeaWmTvfuhRYtoFs3WLGiVCwis/tUPJP+d4j1B8/j5+nC07fX5PFba+Q5ar3U1KcdyVmnSqlCJ/SS7nL/FugArFdK1QVcgLgSjkEIIWwjMxP69gUfH/j8c5sn80PnEvn4p4Os23uOCh7OjLi7Hn1vDcXTVaaflUXWnLa2GGgP+CulTgHjgNnA7Oyu9wygb27d7UIIYZc++AC2bYPlyyHAdpN8TlxIZvLP//Ltzhg8XZx48c669G8birebs81iEsVnzVHufW7yUdnf3FcIIQrrwAF4+23o3Rt69LBJCOcT0/n0139Z9E80To6KQbfXZPDttajo6WKTeIRlSb+KEEJYm9YwbJgxqv2TT0r89snpWcz44ygzfj9KWpaZ3i2r8XynOrIvuZ2RhC6EENa2Zg2sW2csJFOCXe2ZJjNLtpzkk5//JS4pnXsaB/HKXfVkBzQ7JQldCCGsKT3d2HwlPByee65Ebqm15sc9Z5mw7iBH45JpFebHjCciaF69YoncX9iGJHQhhLCmSZPgyBGjhe5s/UFnfx+9wPgfD7AjOp46AV7M6htJx/AAVCmYHiesSxK6EEJYy+nTxvKu998Pd91l1VttO3GRif87xF+HLxDo48r4/2vM/7UIwcnRwar3FaWHJHQhhLCWUaOMuecTJ1rtFrtOxvPx/w7x+6Hz+Hu5MPbe+jx2Sw3cnEvvZi/COiShCyGENWzZAgsWwOjRUKuWxYs/dzmN8T8c4JsdMVT0cGZU13CeyGd1N2Hf5P+8EEJYw9tvQ8WKRkK3oLRME7P+PMZn6w+TZdI8074Wz7avJYvCCEnoQghhcbt3w+rV8Oab4O1tsWL3nk7gma+2E30xhS4NAxlzTwOqV/KwWPmibJOELoQQlvbee0YiHzrUYkX+tPcsw5bupIK7MwsHtKZNbX+LlS3sgyR0IYSwpEOHYNkyGDHC6HIvJq01X/x+lPE/HqBJSAVmPBFBgLes8CZuJAldCCEs6YMPwNXVWEymmDKyzIxZGcXybae4t0kVPn64qYxeFzclCV0IISzlxAljZPszz0BgYLGKyjSZGbJoO//bd44XOlba9cAAACAASURBVNVh2J11ZHEYkSdJ6EIIYSkffmjscT58eLGKyTKZeWHJDv637xxvPdCQJ24NtUx8wq7JEkJCCGEJZ87ArFnQty9Uq1bkYkxmzUvLdrE26ixj760vyVwUmCR0IYSwhEmTjFXhRo4schFms2bEit18t+s0o7qGM6BdTQsGKOydJHQhhCiu+HiYPh169oTatYtUhMmsGfXNbr7efoqXOtdl8B2WX11O2Dd5hi6EEMX1+eeQmFjk1rnJrBm+fBff7Ijh+U51eL5THQsHKMoDSehCCFEcqakweTLcfTc0a1boy7NMZl5ctovVu07zcue6DJVkLopIEroQQhTH3LkQG2vsrFZImdmj2ddGnWXk3eE801662UXRSUIXQoiiysqCCROgdWu4/fZCXZqWaWLoYmNq2th768sAOFFsktCFEKKoVqyAY8eM/c4LsehLYlomA+dv5e+jF3nz/ob0vS3UejGKckMSuhBCFIXWxjKv4eFw//0FvuxCUjr95mxh35nLTO7VjAebB1sxSFGeSEIXQoiiWLcOdu2COXPAoWAzgGPiU3l81j/EXEplxhMRdAwv3vKwQuQkCV0IIYrigw8gJAQeeaRApx88m8iTczaTmJbFgqda0yrMz8oBivJGEroQQhTWX3/Bb78Z09VcXPI//XAcgxdsw93FkSVP30LDqr4lEKQobyShCyFEYb33Hvj7w4AB+Z66fOtJRn8TRa3KXsx5siVVK7iXQICiPJKELoQQhbFzJ6xdC++8A56eNz1Na82kn/9lyi//0ra2P9Mea4GPm3MJBirKG0noQghRGO+/Dz4+MGTITU/JyDIz6uvdfLMjhh4RIbzfvTHOjrJ1hrAuSehCCFFQBw/C8uXGqnAVKuR6SkJKJoO/2samoxd48c66PN+pNqoQc9SFKCpJ6EIIUVDjx4ObGwwbluvHJy+m8OTcLZy4kMykXk15qHlICQcoyjNJ6EIIURDR0bBgATz7LAQE3PBx1KkEnpy7mYwsMwueas0tNSvZIEhRnklCF0KIgvjoI+O/r7xyw0ebjlxg4PytVPBwZsmgW6gd4F3CwQkhCV0IIfJ37hzMmAFPPAHVql3z0S/7z/HMwu3U8PNgwVOtCfJ1s1GQoryThC6EEPmZNAkyMm7YInXVzhheXraLBlV9mPtkK/w8819kRghrkYQuhBB5uXQJpk2Dnj2hTp2rhxdvjubVlVG0CvVjZt9IvGWOubAxSehCCJGXTz+FxER49dWrh/637xxjVkZxR93KTH8sAjdnRxsGKIRBVjoQQoibSUqCTz4xtkdt3BiAXSfjGbp4O42Dffn8UUnmovSwWkJXSs1WSsUqpfbk8tnLSimtlPK31v2FEKLYpk+HixdhzBjAmGf+1LwtVPZ2ZWbflri7SDIXpYc1W+hzgbuvP6iUqgbcBURb8d5CCFE8aWnw8cdw553QqhXxKRn0nbOZTJNmTr9WVPZ2tXWEQlzDaglda/07cDGXjyYBIwBtrXsLIUSxzZ4NZ8/CmDFkZJkZtGAbpy6m8uXjEdQO8LJ1dELcQGltvbyqlAoF1mitG2W/fwDoqLV+QSl1HIjUWsfd5NpBwCCAwMDAiCVLllgsrqSkJLy85B+kJUmdWpbUp+UVpk5VVhatH3uMdH9/dnz6KfP3ZfDrySwGNXHltqoylhjkZ9QactZphw4dtmmtIwtzfYn9ZCqlPIBXMbrb86W1/hL4EiAyMlK3b9/eYrFs2LABS5YnpE4tTerT8gpVp0uXwrlzuM2cyTmvWvx6MopBt9fk1XvqWzXGskR+Ri2vuHVakqPcawFhwK7s1nkIsF0pFVSCMQghRP6mTYOaNdne6FZe+3YvbWv7M6JLPVtHJUSeSqyFrrWOAq7uaJBfl7sQQtjEnj3w++/Ejp/IM4t2EOjryqd9muMk+5mLUs6a09YWA5uAekqpU0qpp6x1LyGEsJjPPyfD3ZNn3SO4nJrFl49HUlGWdBVlgNVa6FrrPvl8HmqtewshRJEkJsL8+bz/1NtsjUnk0z7NqV/Fx9ZRCVEg0ockhBBXfPUVa4ObMsezLk+2CeW+plVtHZEQBSYJXQghALTm6LxljOj2Is2rVWB0VxnRLsoWSehCCAGk/fYHzzb4P5xcXJj6aAtcnOTXoyhbZIUEIYQAXl++gwMBtZnTqwnBFdxtHY4QhSZ/ggohyr0Vv+5lmXdtnjMdp0PTarYOR4gikYQuhCjXjpxP4vWfjtA6OooXB3S2dThCFJkkdCFEuZWeZeL5RdtxTUvlk4R/cKxX19YhCVFkktCFEOXWBz8cYO+ZRCasmUjQwL62DkeIYpGELoQol37Zf445fx2nX+xO7kw/Dd262TokIYpFEroQotw5m5DGK8t3Ud/PlVHz34RBg8BJJv2Isk1+goUQ5YrJrBm2dAdpmWamJvyBm9IwYICtwxKi2CShCyHKlam/HubvoxeZ8EB9at37CHTvDlWq2DosIYpNutyFEOXGP0cv8Mkvh3ioeTA9DvwOly7BM8/YOiwhLEJa6EKIciEpQzNqyU6q+3nw9oONUO0GQf36cMcdtg5NCIuQhC6EsHtaa2ZGpXMxWfPNs7fhFbUTtmyBTz8FpWwdnhAWIV3uQgi7N+ev4+w8b2L0PeE0CvaFqVPBwwMef9zWoQlhMZLQhRB2bduJS7z/w36aBzjS77ZQOHECFi40Rrb7+to6PCEsRrrchRB2KzYxjWcXbqOKrzsDGoNSCj780OhmHz7c1uEJYVHSQhdC2KVMk5nnFu0gITWT6Y9F4Oms4MwZmDUL+vaFkBBbhyiERUlCF0LYpfE/HGDzsYu8370xDar6GAc//hgyM2HUKNsGJ4QVSEIXQtid1btOM/PPY/S9tQYPNTda4k4JCTB9OvTpA7Vq2ThCISwvz4SulOqf43WIUuoXpVS8UmqjUkr2GRRClDoHzl5m5Ne7iahRkTH3Nrh6POTrryE5GUaPtmF0QlhPfi3053K8nggsBfyACcDn1gpKCCGKIj4lg0Hzt+Hl6sS0R1vg4pT9K+7yZYJXroSHHoKGDW0bpBBWUpgu97pa6y+11mat9UqMxC6EEKVCVvYguLMJaUx/PIJAH7f/Ppw2DeekJBgzxnYBCmFl+U1bC1FKTQEUUFkp5ay1zsz+zNm6oQkhRMGN//EAfx6O48P/a0KL6hX/+yAtDSZN4mLLlvhFRNguQCGsLL+EnnOi5lbAC7iklAoCvrNaVEIIUQjf7ohhxh/HeOLWGvRsWe3aD5csgdhYokeMkG5FYdfyTOha63k3OX4WeNUqEQkhRCHsPhXPyK930zrMj9e6Nbj2Q61h8mRo1Ij4Fi1sE6AQJaTI09aUUt0sGYgQQhRW7OU0Bs3fhr+XK5892gJnx+t+pW3YALt2wbBhsgmLsHvFmYfe0mJRCCFEIaVlmhi4YBuX0zKZ8UQk/l6uN540eTL4+8Mjj5R8gEKUsCIndK31OEsGIoQQBaW1ZtTXu9l1Mp6JPZv9txJcTocPw+rV8Mwz4O5e8kEKUcLyTehKKR+l1A3LKimlmlgnJCGEyNv0347y7c7TvNS5Lnc3Csr9pE8/BScnI6ELUQ7kt1JcT+AA8LVSaq9SKmc3+1xrBiaEELn5ed85Plx3gG5NqjC0Y+3cT0pIgNmzoXdvqFKlZAMUwkbya6G/CkRorZsBTwILlFIPZX8mI0yEECVqT0wCQxfvoFFVXyb0aGpsh5qbWbMgKQleeKFkAxTChvKbh+6otT4DoLXerJTqAKxRSlUDtNWjE0KIbKfjU+k/dwt+ni7M6huJu4tj7ieaTEZ3e7t2IAvJiHIkvxZ6Ys7n59nJvT3wACALIgshSkRiWib9524hNcPE7H4tCci5rOv11q2D48fh+edLLD4hSoP8WujPcF3XutY6USl1N9DTalEJIUS2LJOZIYt28G9sEnP6taRekHfeF8ycCZUrw/33l0yAQpQS+a0Utyvne6WUT45rfrBWUEIIAcb0tNdW7eH3Q+f5oHtjbq9bOe8Lzp0zpqoNGwYuLiUTpBClRIHmoSulnlZKnQV2A9uyv7bmc81spVSsUmpPjmMTlFIHlFK7lVIrlVIVihO8EMK+ffTTQRZvPsmQDrXo3ap6/hfMnw9ZWfDUU9YPTohSpqALy7wCNNJah2qtw7K/auZzzVzg7uuO/S+7nCbAIWB0oaIVQpQbM/84ymfrj9CnVTVeuate/hdobYxub9MGwsOtH6AQpUxBE/oRIKUwBWutfwcuXnfsJ611Vvbbv4GQwpQphCgfVmw7xTvf7+eexkG882Djm09Py+mvv+DgQWmdi3Irv0FxV4wGNiql/gHSrxzUWhdnGGl/YGkxrhdC2KGf9p5l5Ne7aVfHn0m9muHoUMAlL2bNAm9vePhh6wYoRCmltM5/OrlSajPwJxAFmK8cv9n2qjmuCwXWaK0bXXd8DBAJdNc3CUApNQgYBBAYGBixZMmSfOMsqKSkJLy8vCxWnpA6tbTyWp9R57P4ZEc61b0dGNHSDTengiVzx+RkbuvRg3OdOnHolVdyPae81qm1SH1aXs467dChwzatdWShCtBa5/sF7CjIeblcFwrsue5YP2AT4FHQciIiIrQlrV+/3qLlCalTSyuP9fnHofO67pi1uuvk3/Wl5PTCXfzFF1qD1n//fdNTymOdWpPUp+XlrFNgqy5kzi1ol/sP2S3m1Vzb5X7x5pfcKHv++gjgDq11oZ7JCyHs18bDcTw1bwth/p58NaA1FTwKOeVs5kxo1AhatbJOgEKUAQVN6H0wlnoddd3xm450V0otxlhVzl8pdQoYh/Es3hX4X/Ygl7+11oMLGbMQwo78ffQC/edtoUYlDxYOaI2fZyGTeVQUbNkCkyZBQQbPCWGnCprQGwDPAm0xEvsfwPS8LtBa98nl8KxCRSeEsGubjlzgqXlbCKnowcIBt1DJy7XwhXzxBbi6wmOPWT5AIcqQgk5bmwfUB6YAn2Ik+DwHxAkhRF42HIyl35zNBFdwZ9HA1lT2LkIyT0oyFpN5+GHw97d8kEKUIQVtoTfSWjfI8X69UmqfNQISQti/H/ecZeji7dQN9GZ+/1ZFa5kDLFkCiYkwWJ7cCVHQFvp2pdQtV94opVqTz9KvQgiRm1U7YxiyaDuNg31ZNLCI3exXfPGFMRjuttssF6AQZVRBW+gRGAvLRGe/rw4cVEpFAVobS7kKIUSe5m86zrjv9tI6zI9ZfVvi6VrQX0G52LrV+Jo6VQbDCUHBE/r1a7ILIUSBZZrMvLV6Hwv+PsGd9QOZ+khz3Jwdi1foF1+Ah4cMhhMiW4ESutb6hLUDEULYp4SUTIYs2s6fh+N4+o6ajOgSXvDlXG9aaAIsWgSPPAK+vpYJVIgyrhj9XUIIkbej55MYMG8rJy+lMKFHEx6OrGaZgr/6ClJSZDCcEDlIQhdCWJzWmuXbTvHGd3txc3Zk0cBbaBnqZ6nCYfp0iIyEiAjLlCmEHZCELoSwqPiUDMas3MP3UWe4paYfE3s2o2oFd8vdYONG2LPHWO5VCHGVJHQhhMVsPBLHy8t2cT4xnVFdwxnYrmbxn5df79NPjefmvXtbtlwhyjhJ6EKIYktOz2L8jweYv+kENf09WflsGxqHWGGw2vHjsHw5vPIKeHpavnwhyjBJ6EKIYtl4OI4RX+8mJj6V/m3CeKVLXTxcrPSrZdIkcHCA55+3TvlClGGS0IUQRZKUnsX7a/ez8J9owvw9Wfb0rZYb+Jabixdh1ixjqlpwsPXuI0QZJQldCFFofx2OY8SK3ZxOSOWptmG8clc93F2KuVBMfqZPh+Rko7tdCHEDSehCiALL2Sqv6e/JisG3ElHDiq3yK9LTYcoU6NIFGje2/v2EKIMkoQshCmT9wVjGrtzD6YRUBrQN45Uu9Yq/fGtBffUVnDsHw4eXzP2EKIMkoQsh8nQ6PpW3Vu/jx71nqVm5BFvlV5jN8PHH0KwZdOxYcvcVooyRhC6EyFWmycycv44x+ed/MWvN8C71GNAuDFenEmqVX7F2LezfDwsXyq5qQuRBEroQ4ga7T8UzYsVuDpxN5M76AYy7ryHV/DxsE8xHH0G1avDww7a5vxBlhCR0IcRVKRlZTPrfIWb9eYzK3q588XgEXRoG2S6grVvht9+MLndnZ9vFIUQZIAldCAEYC8SM/GY3Jy+m8kjr6ozqGo6Pm42T6Mcfg48PDBhg2ziEKAMkoQtRzqVmmPjgh/3M23SCMH9Plgy6hVtqVrJ1WBAdbSzzOmyYkdSFEHmShC5EObYj+hIvL9vF0bhknmwTysi7w0tuKlp+PvnEGAT3wgu2jkSIMkESuhDlUKbJzJRf/uWz9Yep4uvOogGtua22v63D+k9CAsyYAT17GgPihBD5koQuRDlz8mIKQxfvYOfJeHpEhPD6fQ1s/6z8ejNmQGIivPyyrSMRosyQhC5EObJ612le/SYKFEx9pDndmlS1dUg3ysw0utvbt4cWLWwdjRBlhiR0IcqBpPQs3lmzjyVbTtK8egWm9G5uu3nl+Vm+HE6dMjZjEUIUmCR0IezcrwfOMXblHs5cTuPZ9rV4sXNdnB0dbB1W7rQ2pqqFh0PXrraORogyRRK6EHbqfGI6b63Zx+pdp6kT4FXya7AXxdKlsH07zJkDDqX0jw4hSilJ6ELYGa01y7ed4t3v95OaYeLFO+syuH3Nkl+DvbBSUozd1Fq0gMcft3U0QpQ5ktCFsCOHY5N4dWUUm49dpGVoRd7v3pjaAd62DqtgJkwwnp0vWgSOpfyPDyFKIUnoQtiBtEwT0zYc4fMNh3F3duSD7o3pGVkNB4cysjtZdDSMHw+9ekG7draORogySRK6EGXcnpgEXly6k39jk3iwWVXG3NuAyt6utg6rcEaONAbEffihrSMRosyShC5EGWUya2b8cZSPfzqIn6cL8/q34o66lW0dVuH9+ScsWQKvvw7Vq9s6GiHKLEnoQpRBMfGpvLR0J/8cu0jXRkG891BjKnq62DqswjObjbXaQ0JgxAhbRyNEmSYJXYgy5oeoM4z8ejcms2ZCjyb0iAhBqTLyrPx6ixYZ09QWLgRPT1tHI0SZJgldiDIiLdPEO9/v46u/o2ka4suUPs2pUakMJ8HMTHjzTWjaFHr3tnU0QpR5ktCFKAMOxyby3KIdHDibyNO31+Tlu+rh4lTGF16ZPx8OH4bvvpNFZISwAKsldKXUbKAbEKu1bpR9zA9YCoQCx4GeWutL1opBCHvw7Y4YRn8ThYeLI3OfbEn7egG2Dqn40tPhrbegVSvo1s3W0QhhF6z5Z/Fc4O7rjo0CftFa1wF+yX4vhMhFRpaZN77by7ClO2kc4svaF9rZRzIHmDXLmHv+9ttQVp//C1HKWK2FrrX+XSkVet3hB4D22a/nARuAkdaKQYiyKj7NzCMz/mbriUs81TaMUV3DS++GKoWVmgrvvgtt20LnzraORgi7obTW1ivcSOhrcnS5x2utK2S/VsClK+9zuXYQMAggMDAwYsmSJRaLKykpCS8vL4uVJ6ROLenQJRNTt6eSZlb0b+TKLVXsa6hLyPLl1J42jR2TJpHQrFmJ3Vd+Ri1L6tPyctZphw4dtmmtIwtzvc0Sevb7S1rrivmVExkZqbdu3WqxuDZs2ED79u0tVp6QOrUErTVf/X2CN1fvo5IbzB/UjnpBZWQd9oJKSoJataBxY/j55xK9tfyMWpbUp+XlrFOlVKETekn/6X9OKVVFa31GKVUFiC3h+wtRKqVlmnjt2z0s33aKjuEB/F9wkv0lc4BJkyA21nh2LoSwqJJ+KPcd0Df7dV9gVQnfX4hS50xCKr2+/Jvl207xfMfazHwiEk9nOxwoFh0N778P//d/cOutto5GCLtjzWlrizEGwPkrpU4B44APgGVKqaeAE0BPa91fiLLgj3/P88KSnaRnmpj+WAR3NwqydUjW8/LLxn8//ti2cQhhp6w5yr3PTT7qZK17ClFWmMyaKb/8y5Rf/6VOgBfTHo2gdoAdDzD65RdYscKYe16jhq2jEcIu2dfwWSHKgLikdIYt2cmfh+Po3iKYdx5shIeLHf9TzMyEoUOhZk0YPtzW0Qhht+z4t4gQpU/UqQQGzt/KpZQMxv9fY3pGViu7G6sU1NSpsH8/rFoFbm62jkYIuyUJXYgSsmb3aV5ZvotKnq58/cxtNAr2tXVI1nf2LIwbB127wn332ToaIeyaJHQhrMxs1kz++RBTfj1MZI2KTH88An8vV1uHVTLGjoW0NJg8WZZ4FcLKJKELYUVpmSZeXLqTH/ac5eGIEN55qBGuTo62DqtkREfDvHnwzDNQt66toxHC7klCF8JKElIzGTBvC1tPXGLsvfV5qm2Y/T8vz+nK9LRXXrFtHEKUE5LQhbCC2MtpPDF7M0fOJ/Fpn+Z0a1LV1iGVrPPnYcYMePRRqF7d1tEIUS5IQi8Cs1lz8lIKR88nczQumWNxSZy8mEp8aiaXs7+S0rOo6OFCkK8bQT5uBPm6Ub+KN63DKlGjkkf5aqmVMycuJPP4rM3EJaUzu19L2tWpbOuQSt6UKcaz85GymaIQJUUSegForTlxIYU/D8ex8UgcG49cID4l8+rn3m5O1KjkQUUPF6pVdMfX3RkvVycuJmdw9nIaR84n8ce/50nOMAEQ5ONGqzA/OoYH0LVxUPl5ploO7D9zmcdnbcZkNrNo4C00q5brZoL2LTHRmKr24INQv76toxGi3JCEnofUDBNfbz/F3I3HORybBEAVXzfurB9Iy9CK1KrsRZi/J36eLvm2uLXWHI5N4u9jF/nn6AU2Hb3Ad7tO8873LvRpVZ1HWleniq97SXxbwkp2n4rn8VmbcXd2ZMmg2+x75be8fPEFxMfDqFG2jkSIckUSei7OJKQyf9MJFv0TTUJqJo2CfXjz/oa0q+NPmL9nkbrLlVLUCfSmTqA3j99SA601fx6OY97G40xdf5hpG45wd8MgBt5es3y26sq4rccv8uScLfh6OLN44C1U8/OwdUi2kZ4OEydCx47QqpWtoxGiXJGEnoPWmkWbo3lr9T4yTWa6NAyif9swImtUtPgzb6UU7epUpl2dypy8mMKCv0+weHM030edoVWYH4PvqEn7ugE4OMiz9tJu45E4BszbSpCPGwsHti7fPS3z5sGZMzB/vq0jEaLckYSeLTEtk9HfRLFm9xna1fHnvYcal1grq5qfB6/eU5/nO9VhyeZoZv95jP5zt1InwIs+rarzYPNg/DxdSiQWUTjrD8YyeME2alTy4KsBrQnwLsdLm6anw/jxEBkJnWQPJiFKmiR0YE9MAs8t2s7JS6kM71KPZ+6oZZOWsZerEwPa1aTvbaF8v/sMc/46xltr9vH+D/vpGB7AwxHVaFvHHzdnGURXGny97RQjv95NvSBvFjzVWv7omjABjh6Fzz+XVeGEsIFyl9Bn/XmM6b+l4LTpFzJNmkyTmcS0TAK83Vgy6BZahvrZOkScHR14sHkwDzYP5uDZRFZsO8nKHTGs23sOF0cHmoT40jLMj5ahFWkcXAF/r/wH5QnL0Vrzxe9H+eCHA7SpXYnpj0Xg7eZs67Bs6/hxePdd6NED7rrL1tEIUS6Vu4Qe5ONG7QoOhFT1x8XJAWdHB3zcnel3W2ipbGHVC/JmzL0NGHF3OH/+G8emoxfYfOwiM34/yucbNGC07EP9PQjz96KGnwdVKrhR1dedqhXcqVLBDZ/ynmwsyGzWvPP9fmb/dYz7mlblo4ebyLRDgBdeAEdHmDTJ1pEIUW6Vu4R+b5MqeF48SPv2TW0dSqE4OzrQITyADuEBgDGlbsfJSxw8m8jxuGSOXUhh18l41kadwWTW11zr4+ZESEUPgiu6E1LRnXqB3jSs6kvdIC9JRoWQkpHF8OW7+T7qDP3bhDH23voyaBFgzRr47jvj+XlIiK2jEaLcKncJ3V64uzhyWy1/bqvlf83xLJOZ80npnI5P5XR8GjHxqcRcSiUmPpUTF5L58984UjONBW6cHIypdLfU9OPuhkFEhvrhKAkqVycvpjBowTYOnL3Mq/eEM7BdTXnMAZCaCs8/bywgM2yYraMRolyThG5nnBwdqOLrThVfdyJq3Pi52aw5cTGFvacT2Hv6MntiElj4TzRz/jpOJU8XOjcI5N4mVWhTy19an9k2Ho5jyKLtZJk1c/q1pH29AFuHVHp88AEcOwa//goupe+RlRDliST0csbBQRHm70mYv+fVDUOS07PYcPA86/aeZc3uMyzZcpLqfh70blWNhyOqUdm7nOzdfR2tNXP+Os67a/cT5u/JjCciCfP3tHVYpcehQ0Y3e58+0KGDraMRotyThC7wdHXi3iZVuLdJFdKzTKzbe45F/5zgwx8PMvGnQ3RpFMTTt9ekSUj5WcEuPiWDkV/vZt3ec9xZP5BJvZrKSPaczGYYMADc3f/bJlUIYVOS0MU1XJ0cub9pVe5vWpUj55NY/E80S7ee5PvdZ2hb259n29fi1lqV7Pr58ZbjF3lh8Q7OJ6Uz9t769G8TJo8frjd9OvzxB8yeDVWq2DoaIQTgYOsAROlVq7IXY7s1YOOojozuGs7Bc4k8MvMfHpy2kc3HLto6PIvLMpmZ8su/9PpiE85ODnz9zG0MaFdTkvn1oqONbVHvvBP69bN1NEKIbNJCF/nydnPm6Ttq0fe2UL7ZHsNn6w/T84tN9IwMYVTX+qVy/n5h7Yi+xKsr97D/zGUebFaVtx9sJF3sudEaBg82uty//FJWhBOiFJGELgrMzdmRR1pX58HmVZnyy2Fm/nGU/+07x+h76uOvdf4FlEIJKZl8uO4AizZHE+jtxuePtuDuRkF2/UihWBYuhB9+gE8+gbAwW0cjhMhBErooNA8XJ0Z1Deeh5sGMWRnFiBW7aVDJgQYt0gjyLRubk2SazKzYdoqPfzrIxeQM+rcJ48XOdfFylX8SNxUba6wId+utMGSIraMRn05N3wAAFLtJREFUQlxHnqGLIqsX5M2yp2/l3YcacTjeTJfJv7M26oytw8qT2axZtTOGzhN/Y/Q3UVT38+C759ryWrcGkszzojUMGgRJSTBzprHMqxCiVJHfYKJYHBwUj7augWPcERYdc+HZhdvpERHCG/c3LFUJUmvNrwdimbDuIAfOJhIe5M3MJyLpVD9AutcLYs4cWLXKmKLWoIGtoxFC5KL0/MYVZVqQpzEq/JOf/2Xa/7d373E6l/kfx1+fMYNhYkbDhOE3DpND5DSFqNDuIyKbbXXQrtistk1S/MJuZWs7bFsquxVZpPpVKmpZSVshOWQzyNhQTjk3KDSEMa7fH9ftYbI5jLnNd+77fj8fj/txH+b2vT9dc3V/5vu9Dp85a/h0wzc8c2NLmqZXDjo0Pt+6l4dnfM78Nbuok1qRv93Ygq5Nq2v2+ulat85fau/YUdu7ipRiSugSNgll4hhyZQMuO78qd05ays9Hz2d4l0b0bZcRyFlw7t4DjPzXF7yRvYnkxAQe6H4BvVrXJqGMRppOW0EB9O4NcXEwcaK/F5FSSQldwu7iOlWYMfBShrz5GQ9O/5wFa3fxRM8LSa5QMsvb8g4eZuzcdYz7eB35BUfo174OAzpmUrmClqEV2V/+AvPnw8svQ+3aQUcjIiehhC5nRUrFsoy7OYsJ8zfw53dXcuXTcxnepRHdm9U4a5e68wuOMOnfGxn14ZfszDtE16bV+d8rG5Ch/dfPzJIlcP/90LMn3HRT0NGIyCkooctZY2bc0r4OF2Wk8Ie3VzDo9WVMXLCBEVc3pkXtlLB9zncH8nlryRZemL+eDbv2c3GdKvy9d8OwfkbM+c9/4KqroFo1GD1aG8iIRAAldDnrLkxPZurt7Zi8ZDOPv7eaHs8t4JrmNejbrg4Xplc+4/H1Nbl5vLRwA1OyN7PvUAHN0isz/uYsOjXUzPVi+ewzv61rQgJ88AGce27QEYnIaVBClxIRF2dcl1WLq5pW57nZaxg/bz3/WLaV+tWSuLZlOj1a1DzlpjT7Dx1m0bpv+PjLncxbs4Mvvs6jbJk4ujWrTu+2GTSvFTvV4M6a7Gz46U+hYkVf4zwzM+iIROQ0KaFLiUoqF889nRty6+X1eGf5NqYs2cxjM1fxl/dWcV6l8lSvXJ7qlROpXrk8Dtjx3UF/yzvIV7v2kV/gKBsfx8UZVejZqhY9WtYkNSk267WHlXMwZw706AHJyTB7trZ2FYkwSugSiMqJCfRqXZterWuzYec+pi/fyvqd+9m253tWbtvLh6u+powZqeeUo2pSOTKrJXFFo2q0r5/KRRlVKJ+gncqK7eBBn8T/+U+YPh2++grq1vXJXDPaRSJOIAndzO4C+gEOyAH6OucOBBGLBC8jtSIDOunS7ll18CBMmwY5ObBqFaxcCV98AYcOQWKiv8x+771w7bWQosmEIpGoxBO6mdUEBgKNnXPfm9kbwA3AxJKORSTqOQdvvw1DhsD69X5jmLp1oVEj6NIFOnTwO8AlJgYdqYgUU1CX3OOBRDPLByoAWwOKQyR6LV0Kd90FH30ETZrAjBk+eZePjIp4IlI0Jb6Po3NuC/AEsBHYBuxxzv2rpOMQiVp5eTBgALRq5deTjx7tk3uXLkrmIlHMnHMl+4FmKcAU4HpgN/AmMNk593/Hva8/0B8gLS2t1aRJk8IWQ15eHklJSWE7nqhNw+1M2zMlO5sGjz9OudxctlxzDRt+/WsO6/cCqI+Gm9oz/Aq3aceOHbOdc1lFOoBzrkRvQE9gfKHnvYHnTvZvWrVq5cJp9uzZYT2eqE3DrcjtuXu3c/36OQfOnX++c/PmnZW4Ipn6aHipPcOvcJsCi10R82sQpZM2Am3MrIL57byuAFYGEIdI5NuyBYYNg4wMmDAB7rkHli2Ddu2CjkxESliJT4pzzi0ys8nAEuAwsBQYW9JxiES0nBxfCW3SJDhyxC83GzYMWrYMOjIRCUggs9ydcyOAEUF8tkjEe+UV6NPHT3AbMAAGDtSubiKineJEIsrIkX5NeYcOMGUKVKkSdEQiUkoEMYYuIkV15AjcfbdP5j17wsyZSuYi8gNK6CKlmXN+q9ZeveCpp+COO/y4eTkVpBGRH9Ild5HS5sgRKufkwDvvwNSp8OWXYAaPPgpDh/rHIiLHUUIXKU2ys+GOO2ixcCEkJPitWgcNgquvhlq1go5OREoxJXSR0mDHDvjDH2DcOKhaldWDB9PgvvugcuWgIxORCKExdJEg7dgBjzwC558PL7zgi6l88QXbunVTMheRItEZukhJcw7+/W949ll4/XVfk7xzZ3jySV/WVETkDCihi5xNzsHy5fDZZ362+qpVsGKFn+iWlAS/+Q387nfQuHHQkYpIhFNCFzkbvvvO7+g2ZoxP5gDx8ZCZ6WuTDxoEv/oVnHNOsHGKSNRQQhcJpyVLYOxYn8zz8qB5c5/UO3SAunX9zHURkbNACV2kuL79Fl591c9QX7bM77F+ww3w29/CxRdr3biIlAgldJEzlZsLDz4I48fDgQPQooWf6NarFyQnBx2diMQYJXSRotq/32/D+thj/nHfvnDbbSpdKiKBUkIXOV2HD8NLL8H998OWLXDNNfDnP0ODBkFHJiKijWVETsk5eOstuPBCuOUWSE+HuXPh7beVzEWk1FBCDwfn/KVX54KORMJt1ixo0wauvdb/fqdMgYUL4dJLg45MROQHdMn9eJs3+xnLubl+B6+DB/19QYH/+dGkvW8fbNt27HbokF9nXKWKv6Wm+klS7dr5W3p6cP9NUnTZ2TB8OLz/vv/djR8PvXv737GISCmkbyfwSXrWLHjuOV+usqAAKlTwNafLlvW3wl/kZv5nNWrAZZf5++Rk2LsXvvkGdu2C7dt9Evjb3/y/qV0bMjKgYsVjt9RUqF8f6tXz97VqKWEEbfVquO8+ePNNOPdcGDnS7+RWvnzQkYmInJSyxyef+FnKq1b5L/DBg/364Tp1in/s/Hy/S9j8+bBggU/yubn+7H7fPv/44MFj709MhLZt/R8Jl18OrVv7BL9/v78dOuSTfpxGSsLKOZg3D0aN8uPiiYl+4tvgwVCpUtDRiYiclthO6K+95pN5jRp+9nLPnuE9E0tIgKwsf7vzzv/++ZEjsHUrrFkDa9f6Pb8//hgeeODE4/GXXOJjrVcvfHHGqvx8P7wyahQsXQopKTBkiE/k1aoFHZ2ISJHEZkJ3Dv74R584L73Uz2BOTS35OOLi/PhserrfGvSo3bv9WX12NpQp4y//Jyb6rUQfegiaNfProPv10y5kZ2rdOr+b26ef+sIoY8bAL3/ph0JERCJQ7CX077+n0UMP+THzPn38F3m5ckFH9UPJydC1q78d7/rrfdz9+8O0aX670bS0Eg8xor3xhq9yZgaTJsF11+kPIxGJeLE3GDt0KGmzZvkNQSZMKH3J/FRq1fIzr59+2t9fdBGsXBl0VJHh++/9/Ijrr/dn5cuW+cdK5iISBWIvod9/PzmPPAJDh0buF3lcnB+TX7jQT5Rr3x4WLQo6qtJtxQpfKOX55/3vfu5cv+pARCRKxF5CT01lV9u2QUcRHi1a+LH2lBTo1AnefTfoiEof5/xyxIsu8qsKZs70V2dUxlREokzsJfRoU6+eT+oNGkD37vDii0FHVHrs2gU9esDtt/tJh8uXw5VXBh2ViMhZoYQeDdLSYM4cv369Tx+/TenWrUFHFayVK331sxkz4Mkn4Z13NHlQRKKaEnq0qFTp2OXkGTP8pK+xY/1a91izcKGfV3DwoN/Q5667tBmPiEQ9fctFk4QEP+Fr+XI/vn7rrdCxo19zHSumT4crrvD76S9Y4Df1ERGJAUro0Sgz06+zHzfOL81q1swv0Yv2anATJ/oa5Y0b+3kFdesGHZGISIlRQo9WZr52d06On+F9yy3w85/Djh1BRxZ+zsGjj/ptfDt1gtmztXWriMQcJfRoV7s2fPCBrxo2YwY0aQIvvxw9Y+sFBTBwIPz+99Crl7/kfs45QUclIlLilNBjQVwc3H03LF7sE3zv3n4G+HvvRfZl+AMH4MYb4ZlnfEGVl1/2pW5FRGKQEnosadrU7yj32mu+dnvnzvCTn/iz2r17g46uaHbvhi5dfN3yJ57wN81kF5EYpm/AWBMX56uMrVrly4YuXw5XX+1nhbduDcOG+Rrxpdny5X72+vz58Mor/uxcRCTGKaHHqrJl/djzpk3w4YcwfLhf9jZyJLRt62eLr14ddJT/7dVXoU0b2L/fb6bTq1fQEYmIlApK6LGufHk/M/xPf4J58+Dbb+Hhh32Sv+ACGDCgdMyMz8/3BWluusmfnS9ZApdcEnRUIiKlRiAJ3cySzWyyma0ys5VmFiXVUqJAUpKfMb5mja+5PmYMpKf7pW+33uqff/ppyc2Sz8/368ubNIG//hUGDfJ/bJx3Xsl8vohIhAjqDH0UMNM51xBoBqigd2mTluarlOXk+EvzlSr5CWi33ebLkNarBw8+CBs3np3PP3DAf35mpl9fnpgIU6fCU0+pUpqIyI8o8YRuZpWBy4DxAM65Q8653SUdh5ymRo3g8cf9WfGuXbB+vV8eVr8+jBjha4p37kzq3Llw+HDxP2/PHnjsMahTx1dJq1HDz8JfutRXkxMRkR9lroTXIZtZc2As8Dn+7DwbuNM5t++49/UH+gOkpaW1mjRpUthiyMvLIykpKWzHi1Xlt2/nvJkzOe/ddymfm8uBqlXZ2r0727p2JT8lhfi9e0lau5aktWuJ37uX/bVrs69ePfbXqoWLjz92oIICyu3cSc2pU6kxbRrx+/bxTVYWG3v1Ynfz5n7XuxijPhp+atPwUnuGX+E27dixY7ZzrkjFKIJI6FnAJ0A759wiMxsF7HXO3Xeif5OVleUWL14cthjmzJlDhw4dwna8mFdQQM6jj9L0o4/8rnRly/pL9ps2HXtPXNyxcfeEBH8GfuCAX09+dA18XBz07An33OM3volh6qPhpzYNL7Vn+BVuUzMrckKPP/Vbwm4zsNk5tyj0fDIwLIA4JFzKlGFX+/Zw771+ffvzz0Nuri8K07y5v09J8cvgcnL8OvI1a6BiRf96crK/79bNj82LiEiRlXhCd85tN7NNZtbAObcauAJ/+V2iQcOGfuLaj2na1N+0dlxEJOyCOEMHuAN4xczKAuuAvgHFISIiEhUCSejOuWVAkcYGRERE5MS0U5yIiEgUUEIXERGJAkroIiIiUUAJXUREJAoooYuIiEQBJXQREZEooIQuIiISBZTQRUREooASuoiISBRQQhcREYkCSugiIiJRoMTroZ8JM9sBfBXGQ6YCO8N4PFGbhpvaM/zUpuGl9gy/wm36P865qkX5xxGR0MPNzBYXtXC8nJzaNLzUnuGnNg0vtWf4FbdNdcldREQkCiihi4iIRIFYTehjgw4gCqlNw0vtGX5q0/BSe4Zfsdo0JsfQRUREok2snqGLiIhElZhL6GbW2cxWm9kaMxsWdDyRxsxqmdlsM/vczP5jZneGXq9iZu+b2Zeh+5SgY400ZlbGzJaa2fTQ8zpmtijUV183s7JBxxgpzCzZzCab2SozW2lmbdVHi8fM7gr9P7/CzF4zs/Lqo0VjZhPMLNfMVhR67Uf7pXl/DbXtcjNrearjx1RCN7MywLNAF6AxcKOZNQ42qohzGBjsnGsMtAFuD7XhMOBD51wm8GHouRTNncDKQs8fA55yztUHvgVuCSSqyDQKmOmcawg0w7er+ugZMrOawEAgyznXBCgD3ID6aFFNBDof99qJ+mUXIDN06w+MPtXBYyqhAxcDa5xz65xzh4BJwM8CjimiOOe2OeeWhB5/h/+irIlvxxdDb3sRuCaYCCOTmaUDXYFxoecGdAImh96iNj1NZlYZuAwYD+CcO+Sc2436aHHFA4lmFg9UALahPlokzrm5wDfHvXyifvkz4CXnfQIkm1n1kx0/1hJ6TWBToeebQ6/JGTCzDKAFsAhIc85tC/1oO5AWUFiR6mngHuBI6Pm5wG7n3OHQc/XV01cH2AG8EBrCGGdmFVEfPWPOuS3AE8BGfCLfA2SjPhoOJ+qXRc5XsZbQJUzMLAmYAgxyzu0t/DPnl05o+cRpMrNuQK5zLjvoWKJEPNASGO2cawHs47jL6+qjRRMa1/0Z/o+lGkBF/vvSsRRTcftlrCX0LUCtQs/TQ69JEZhZAj6Zv+Kceyv08tdHLweF7nODii8CtQO6m9kG/DBQJ/wYcHLo8iaorxbFZmCzc25R6PlkfIJXHz1zPwHWO+d2OOfygbfw/VZ9tPhO1C+LnK9iLaF/CmSGZmaWxU/qmBZwTBElNLY7HljpnHuy0I+mATeHHt8MTC3p2CKVc264cy7dOZeB75OznHM3AbOBX4TepjY9Tc657cAmM2sQeukK4HPUR4tjI9DGzCqEvgOOtqn6aPGdqF9OA3qHZru3AfYUujT/o2JuYxkzuwo/XlkGmOCcezjgkCKKmbUHPgZyODbe+3v8OPobQG18ZbzrnHPHT/6QUzCzDsAQ51w3M6uLP2OvAiwFfumcOxhkfJHCzJrjJxiWBdYBffEnMOqjZ8jMHgCux690WQr0w4/pqo+eJjN7DeiAr6r2NTAC+Ac/0i9Dfzg9gx/a2A/0dc4tPunxYy2hi4iIRKNYu+QuIiISlZTQRUREooASuoiISBRQQhcREYkCSugiIiJRQAldJAaZWUbhik8iEvmU0EUkLArtGCYiAVBCF4ldZczs76Ea1/8ys0Qza25mn4TqL79dqDbzHDPLCj1ODW1Ti5n1MbNpZjYLX/pRRAKihC4SuzKBZ51zFwC7gWuBl4ChzrkL8bsBjjiN47QEfuGcu/ysRSoip6SELhK71jvnloUeZwP1gGTn3Eeh117E1xU/lfe1hapI8JTQRWJX4T23C4Dkk7z3MMe+L8of97N94QxKRM6MErqIHLUH+NbMLg09/xVw9Gx9A9Aq9PgXiEipo1mpIlLYzcAYM6vAsSplAE8Ab5hZf+CdoIITkRNTtTUREZEooEvuIiIiUUAJXUREJAoooYuIiEQBJXQREZEooIQuIiISBZTQRUREooASuoiISBRQQhcREYkC/w8eczC3PK/kOwAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 576x432 with 1 Axes>"]}, "metadata": {"tags": [], "needs_background": "light"}}]}, {"cell_type": "code", "metadata": {"id": "Qc1qpi-7fzlL", "colab_type": "code", "colab": {}}, "source": ["# def train_model_val(attr,model):\n", "#   model.train()\n", "#   print('训练',attr,'模型')\n", "#   for i in range(epochs):\n", "#     #train\n", "#     add=0\n", "#     for seq,label in val_inout_seq:   \n", "#         optimizer_dic[attr].zero_grad()\n", "#         seq=seq.to(device)\n", "#         label=label.to(device)\n", "#         model.hidden_cell = (torch.zeros(2, 1, model.hidden_layer_size).to(device),torch.zeros(2, 1, model.hidden_layer_size).to(device))\n", "#         # model.hidden_cell = (torch.zeros(1, 1, model.hidden_layer_size).to(device),torch.zeros(1, 1, model.hidden_layer_size).to(device))\n", "        \n", "#         y_pred = model(seq)\n", "\n", "#         # if(i==epochs-1):  #对最后一次epoch的值进行记录\n", "#         #   value_train_dic[attr].append(y_pred)\n", "\n", "#         single_loss = loss_function(y_pred[0], label[0,index_dic[attr]])   #这里只预测label[attr]的数值，即某个单一的空气污染物\n", "#         add+=single_loss\n", "#         single_loss .backward()\n", "#         optimizer_dic[attr].step()\n", "#     loss_train=add/len(train_inout_seq)\n", "#     # loss_train_dic[attr].append(loss_train)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "j5eX3gelgEEx", "colab_type": "code", "colab": {}}, "source": ["# train_model_val('nox',model_nox)"], "execution_count": null, "outputs": []}]}