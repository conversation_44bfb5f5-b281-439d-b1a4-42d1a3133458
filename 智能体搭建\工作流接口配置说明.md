# 工作流接口配置说明

## 问题诊断

### 当前问题现象
用户反馈Word文档中图表显示为红色X标记，而不是实际的图表图片。

### 问题根本原因
智能体可能调用了错误的接口或使用了错误的数据格式。

## 正确的工作流配置

### 1. 接口地址配置
**必须使用支持图表的接口**：
```
URL: http://*************:8089/office/word/convert_with_charts
```

**错误的接口**（不支持图表）：
```
URL: http://*************:8089/office/word/convert
```

### 2. HTTP方法配置
```
Method: POST
Content-Type: application/json
```

### 3. 请求体格式
**正确格式**：
```json
{
  "markdown": "包含图表标记的markdown内容",
  "charts": {
    "chart_temperature_trend": {
      "title": { "text": "炉膛温度变化趋势" },
      "tooltip": { "trigger": "axis" },
      "xAxis": { "type": "category", "data": ["1月", "2月", "3月"] },
      "yAxis": { "type": "value" },
      "series": [{ "type": "line", "data": [850, 860, 855] }]
    },
    "chart_waste_input": {
      "title": { "text": "垃圾投加量变化" },
      "tooltip": { "trigger": "axis" },
      "xAxis": { "type": "category", "data": ["07-01", "07-02", "07-03"] },
      "yAxis": { "type": "value" },
      "series": [{ "type": "bar", "data": [68, 72, 70] }]
    }
  }
}
```

**错误格式**（纯文本）：
```
只包含markdown文本，没有charts对象
```

## 服务器端处理流程

### 1. 接收请求
- 服务器接收JSON格式的POST请求
- 解析出markdown和charts两个字段

### 2. 图表处理
- 在markdown中查找图表标记：`![标题](chart_id)`
- 根据chart_id从charts对象中获取ECharts配置
- 使用matplotlib将ECharts配置转换为PNG图片
- 将图表标记替换为图片路径：`![标题](file://path)`

### 3. Word文档生成
- 使用python-docx解析处理后的markdown
- 将图片嵌入Word文档的对应位置
- 返回包含图表的Word文档下载链接

## 图表标记语法

### 正确的图表标记
```markdown
![炉膛温度变化趋势](chart_temperature_trend)
![垃圾投加量变化](chart_waste_input)
![污染物排放趋势](chart_emission_trend)
```

### 错误的标记方式
```markdown
图表分析
如图所示
[图表]
```

## 调试检查清单

### 1. 工作流配置检查
- [ ] 确认使用 `/office/word/convert_with_charts` 接口
- [ ] 确认HTTP方法为POST
- [ ] 确认Content-Type为application/json

### 2. 数据格式检查
- [ ] 请求体为JSON格式
- [ ] 包含markdown字段
- [ ] 包含charts字段
- [ ] charts对象不为空

### 3. 图表标记检查
- [ ] markdown中包含图表标记
- [ ] 图表标记使用正确语法：`![标题](chart_id)`
- [ ] chart_id与charts对象中的键名一致

### 4. 配置对象检查
- [ ] 每个chart_id都有对应的ECharts配置
- [ ] ECharts配置格式正确
- [ ] 配置包含真实数据，不是空数据

## 常见错误及解决方案

### 错误1：图表显示为红色X
**原因**：使用了错误的接口 `/office/word/convert`
**解决**：改用 `/office/word/convert_with_charts`

### 错误2：图表标记显示为文字
**原因**：charts对象为空或chart_id不匹配
**解决**：确保charts对象包含所有图表配置

### 错误3：JSON解析错误
**原因**：请求体格式不正确
**解决**：确保使用正确的JSON格式

### 错误4：图片生成失败
**原因**：ECharts配置格式错误或数据无效
**解决**：检查ECharts配置的完整性和数据有效性

## 测试验证

### 1. 最小测试用例
```json
{
  "markdown": "# 测试报告\n\n![测试图表](chart_test)",
  "charts": {
    "chart_test": {
      "title": { "text": "测试图表" },
      "xAxis": { "type": "category", "data": ["A", "B", "C"] },
      "yAxis": { "type": "value" },
      "series": [{ "type": "bar", "data": [10, 20, 30] }]
    }
  }
}
```

### 2. 验证步骤
1. 使用正确的接口发送测试请求
2. 检查返回的Word文档
3. 确认图表正确显示为图片而不是红色X

## 总结

图表显示问题的核心在于：
1. **接口选择**：必须使用支持图表的接口
2. **数据格式**：必须使用JSON格式包含charts对象
3. **标记语法**：必须使用正确的图表标记语法
4. **配置完整**：必须提供完整的ECharts配置

只有这四个要素都正确，才能生成包含图表的Word文档。
