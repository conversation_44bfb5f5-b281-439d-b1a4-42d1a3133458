# Ubuntu中文字体显示问题修复指南

## 问题现象

在Ubuntu系统中运行图表生成服务时，出现以下问题：

1. **图表中文显示异常**：标题、坐标轴标签、图例中的中文显示为方框（□□□）
2. **Word文档格式不统一**：正文字体大小不一致，段落格式混乱

## 快速修复方案

### 🚀 一键修复（推荐）

```bash
# 1. 下载并运行快速修复脚本
sudo bash quick_fix_ubuntu.sh

# 2. 重启Python服务
pkill -f 'python.*app.py'
python app.py

# 3. 运行测试验证
python3 test_ubuntu_fix.py
```

### 🔧 手动修复步骤

如果一键修复失败，可以按以下步骤手动修复：

#### 步骤1: 安装中文字体

```bash
# 更新包列表
sudo apt-get update

# 安装基础中文字体包
sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei

# 安装额外中文字体
sudo apt-get install fonts-arphic-ukai fonts-arphic-uming fonts-noto-cjk

# 安装字体工具
sudo apt-get install fontconfig
```

#### 步骤2: 刷新字体缓存

```bash
# 刷新系统字体缓存
sudo fc-cache -fv

# 验证中文字体安装
fc-list :lang=zh
```

#### 步骤3: 清除matplotlib缓存

```bash
# 清除当前用户的matplotlib缓存
rm -rf ~/.cache/matplotlib

# 如果使用sudo运行，也清除root用户缓存
sudo rm -rf /root/.cache/matplotlib
```

#### 步骤4: 重启服务

```bash
# 停止现有服务
pkill -f 'python.*app.py'

# 重新启动服务
python app.py
```

## 验证修复效果

### 1. 系统字体检查

```bash
# 检查已安装的中文字体
fc-list :lang=zh | head -10

# 应该看到类似输出：
# /usr/share/fonts/truetype/wqy/wqy-microhei.ttc: WenQuanYi Micro Hei
# /usr/share/fonts/truetype/wqy/wqy-zenhei.ttc: WenQuanYi Zen Hei
```

### 2. matplotlib字体测试

```python
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

# 检查可用的中文字体
chinese_fonts = [f.name for f in fm.fontManager.ttflist if 'WenQuanYi' in f.name]
print("可用中文字体:", chinese_fonts)

# 测试中文显示
plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei']
plt.figure(figsize=(8, 6))
plt.plot([1, 2, 3], [1, 4, 2], label='测试数据')
plt.title('中文标题测试')
plt.xlabel('X轴标签')
plt.ylabel('Y轴标签')
plt.legend()
plt.savefig('/tmp/font_test.png')
plt.show()
```

### 3. 服务功能测试

```bash
# 运行专门的Ubuntu测试脚本
python3 test_ubuntu_fix.py
```

## 常见问题解决

### Q1: 安装字体后仍显示方框

**解决方案**：
```bash
# 1. 确认字体确实安装
fc-list :lang=zh

# 2. 清除所有matplotlib缓存
find ~ -name ".cache" -exec find {} -name "matplotlib" -exec rm -rf {} + 2>/dev/null

# 3. 重启Python进程
pkill -f python
```

### Q2: 权限问题导致安装失败

**解决方案**：
```bash
# 确保使用sudo权限
sudo bash quick_fix_ubuntu.sh

# 检查字体目录权限
ls -la /usr/share/fonts/truetype/
```

### Q3: matplotlib导入错误

**解决方案**：
```bash
# 重新安装matplotlib
pip3 uninstall matplotlib
pip3 install matplotlib

# 或使用系统包管理器
sudo apt-get install python3-matplotlib
```

### Q4: 服务无法连接

**解决方案**：
```bash
# 检查服务状态
ps aux | grep python

# 检查端口占用
netstat -tlnp | grep 8089

# 重启服务
python app.py
```

## 技术原理

### 字体查找机制

1. **系统字体**：matplotlib通过fontconfig查找系统字体
2. **字体缓存**：matplotlib维护字体缓存以提高性能
3. **字体优先级**：按照rcParams中的字体列表顺序查找

### 修复原理

1. **安装字体包**：提供matplotlib可用的中文字体文件
2. **刷新缓存**：让系统和matplotlib重新扫描字体
3. **配置优先级**：设置中文字体为首选字体
4. **清除缓存**：删除旧的字体缓存，强制重新加载

## 支持的字体

修复后系统将支持以下中文字体：

- **WenQuanYi Micro Hei** (文泉驿微米黑) - 推荐
- **WenQuanYi Zen Hei** (文泉驿正黑)
- **Noto Sans CJK SC** (Google Noto字体)
- **AR PL UKai CN** (文鼎楷体)
- **AR PL UMing CN** (文鼎明体)

## 性能优化

修复后的系统具有以下优化：

1. **字体缓存**：首次加载后字体信息被缓存
2. **优先级设置**：减少字体查找时间
3. **内存管理**：及时释放matplotlib图形对象
4. **并发安全**：避免多进程字体冲突

## 监控和维护

### 日志监控

```bash
# 查看服务日志
tail -f /var/log/chart_service.log

# 查看字体相关错误
grep -i "font\|字体" /var/log/chart_service.log
```

### 定期维护

```bash
# 每月清理字体缓存
rm -rf ~/.cache/matplotlib

# 更新字体包
sudo apt-get update && sudo apt-get upgrade fonts-*
```

---

**最后更新**：2024年7月29日  
**版本**：v2.2.0 - Ubuntu专用修复版  
**适用系统**：Ubuntu 18.04+, Debian 10+
