# 图表集成版 Markdown to Word 转换器

## 功能概述

这是一个增强版的 Markdown 到 Word 文档转换器，支持以下功能：

### ✨ 新增功能
- **图表生成**：支持将 ECharts 配置转换为静态图片并插入到 Word 文档中
- **多种图表类型**：折线图、柱状图、饼图、散点图等
- **高质量输出**：支持高分辨率 PNG/JPEG 格式图片
- **灵活配置**：支持外部图表配置和嵌入式图表配置

### 🔧 保持兼容
- **原有接口**：保持 `/office/word/convert` 接口的完全兼容性
- **无缝升级**：现有系统可以无缝升级到新版本

## 安装部署

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 创建必要目录

```bash
mkdir -p /var/www/files
mkdir -p /tmp/charts
mkdir -p /tmp/md_temp
chmod 775 /var/www/files
```

### 3. 启动服务

```bash
python app.py
```

服务将在 `http://*************:8089` 启动。

## API 接口

### 1. 原有接口（保持兼容）

**POST** `/office/word/convert`

- **功能**：基本的 Markdown 转 Word 功能
- **输入**：纯文本 Markdown 内容
- **输出**：Word 文档下载链接

```bash
curl -X POST "http://*************:8089/office/word/convert" \
  -H "Content-Type: text/plain" \
  -d "# 标题\n\n这是内容"
```

### 2. 新增接口（支持图表）

**POST** `/office/word/convert_with_charts`

- **功能**：支持图表的 Markdown 转 Word 功能
- **输入**：JSON 格式，包含 Markdown 内容和图表配置
- **输出**：包含图表的 Word 文档下载链接

#### 请求格式

```json
{
  "markdown": "# 报告标题\n\n![图表标题](chart_id)\n\n内容...",
  "charts": {
    "chart_id": {
      "title": {"text": "图表标题"},
      "xAxis": {"type": "category", "data": ["A", "B", "C"]},
      "yAxis": {"type": "value"},
      "series": [{"type": "line", "data": [1, 2, 3]}]
    }
  }
}
```

#### 使用示例

```python
import requests
import json

data = {
    "markdown": """
# 数据分析报告

## 温度趋势
![温度变化](chart_temp)

## 结论
温度控制良好。
""",
    "charts": {
        "chart_temp": {
            "title": {"text": "温度变化趋势"},
            "xAxis": {
                "type": "category",
                "data": ["1月", "2月", "3月", "4月"]
            },
            "yAxis": {"type": "value"},
            "series": [{
                "type": "line",
                "name": "温度",
                "data": [850, 860, 855, 870]
            }]
        }
    }
}

response = requests.post(
    "http://*************:8089/office/word/convert_with_charts",
    json=data
)

if response.status_code == 200:
    download_url = response.text
    print(f"下载链接: {download_url}")
```

## 图表配置

### 支持的图表类型

1. **折线图** (`type: "line"`)
2. **柱状图** (`type: "bar"`)
3. **饼图** (`type: "pie"`)
4. **散点图** (`type: "scatter"`)

### 配置格式

图表配置遵循 ECharts 标准格式：

```json
{
  "title": {"text": "图表标题"},
  "tooltip": {"trigger": "axis"},
  "xAxis": {
    "type": "category",
    "data": ["类别1", "类别2", "类别3"]
  },
  "yAxis": {"type": "value"},
  "series": [{
    "type": "line",
    "name": "系列名称",
    "data": [数值1, 数值2, 数值3]
  }]
}
```

### 嵌入式图表配置

也支持直接在 Markdown 中嵌入图表配置：

````markdown
# 报告标题

## 图表部分

```echarts
{
  "title": {"text": "嵌入式图表"},
  "xAxis": {"type": "category", "data": ["A", "B", "C"]},
  "yAxis": {"type": "value"},
  "series": [{"type": "bar", "data": [1, 2, 3]}]
}
```

这种方式会自动解析并生成图表。
````

## 在 Dify 中的使用

### 1. 更新智能体提示词

在垃圾焚烧数据分析智能体的提示词中，可以这样使用：

```markdown
## 工作流程更新

### 标准分析流程（增强七步法）
1. **第一步：text2sql** - 根据用户需求生成SQL查询语句
2. **第二步：sql_execute** - 执行SQL获取数据结果
3. **第三步：数据分析** - 基于查询结果进行专业分析
4. **第四步：图表配置生成** - 根据数据生成ECharts配置
5. **第五步：报告生成** - 按模板格式生成完整的markdown格式报告
6. **第六步：图表标记** - 在markdown中添加图表引用标记
7. **第七步：文档转换** - 使用增强版工作流生成包含图表的Word文档

### 图表标记格式
在生成的markdown报告中，使用以下格式引用图表：
- `![炉膛温度变化趋势](chart_temperature)`
- `![污染物排放对比](chart_emissions)`
- `![工况分布](chart_status)`
```

### 2. 调用方式

在 Dify 工作流中，调用 `/office/word/convert_with_charts` 接口：

```json
{
  "markdown": "{{生成的markdown报告内容}}",
  "charts": {
    "chart_temperature": {{温度图表配置}},
    "chart_emissions": {{排放图表配置}},
    "chart_status": {{工况图表配置}}
  }
}
```

## 测试

运行测试脚本验证功能：

```bash
python test_chart_integration.py
```

测试脚本会验证：
- 基本 Markdown 转换功能
- 图表集成功能
- 嵌入式图表功能

## 故障排除

### 常见问题

1. **图表不显示**
   - 检查图表配置格式是否正确
   - 确认图表ID在markdown和配置中匹配
   - 查看服务器日志获取详细错误信息

2. **中文字体问题**
   - 确保系统安装了中文字体（SimHei、Arial Unicode MS等）
   - 检查matplotlib字体配置

3. **权限问题**
   - 确保输出目录有写权限
   - 检查临时目录权限设置

### 日志查看

服务器日志会记录详细的处理过程：

```bash
# 查看实时日志
tail -f /var/log/app.log

# 或者直接运行时查看控制台输出
python app.py
```

## 性能优化

1. **图片缓存**：相同配置的图表会复用生成的图片
2. **并发控制**：使用锁机制避免并发冲突
3. **资源清理**：自动清理临时生成的图片文件
4. **高分辨率**：默认300 DPI确保图片质量

## 扩展开发

### 添加新图表类型

在 `chart_generator.py` 中添加新的图表生成方法：

```python
def _generate_custom_chart(self, config, chart_id, format, dpi):
    """生成自定义图表"""
    # 实现自定义图表逻辑
    pass
```

### 自定义样式

修改 `ChartGenerator` 类的样式设置：

```python
def __init__(self, output_dir):
    # 自定义matplotlib样式
    plt.style.use('your_custom_style')
    # 设置颜色主题
    sns.set_palette("your_palette")
```

## 版本历史

- **v2.0.0**：新增图表集成功能
- **v1.0.0**：基础 Markdown 转 Word 功能
