# 环境违规行为检测系统

基于YOLOv8的环境违规行为检测系统，用于实时检测工地扬尘、裸土未覆盖、夜间违规施工等环境违规行为。

## 功能特点

- **多类别违规行为检测**：同时检测多种环境违规行为
  - 裸土未覆盖
  - 工地扬尘
  - 土方作业未降尘
  - 夜间违规施工
  - 露天烧烤
  - 垃圾焚烧
  - 渣土车未覆盖

- **多种检测模式**
  - 图片检测：上传图片进行单次检测
  - 视频检测：处理整个视频文件
  - 实时检测：通过摄像头进行实时监控

- **友好的用户界面**
  - 基于Streamlit构建的Web应用界面
  - 检测结果可视化展示
  - 支持检测结果导出为JSON格式

- **高性能实时检测**
  - 基于YOLOv8的高效目标检测
  - 支持GPU加速
  - 支持不同模型大小（精度/速度平衡）

## 安装

### 依赖环境

- Python 3.8+
- PyTorch 1.7+
- OpenCV 4.1+
- Ultralytics (YOLOv8)
- Streamlit

### 安装步骤

1. 克隆仓库
```bash
git clone https://github.com/yourusername/environment-violation-detector.git
cd environment-violation-detector
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 下载预训练模型（可选）
```bash
# 系统首次运行时会自动下载预训练的YOLOv8模型
# 也可以手动下载并放置在项目根目录
```

## 使用方法

### 启动Web应用

```bash
streamlit run app.py
```

启动后，在浏览器中访问 http://localhost:8501 即可使用Web界面。

### 命令行检测

#### 图片检测

```bash
python detect.py --source path/to/image.jpg --conf 0.5 --output results
```

#### 视频检测

```bash
python detect.py --source path/to/video.mp4 --conf 0.5 --output results
```

#### 实时摄像头检测

```bash
python detect.py --source 0 --conf 0.5 --output results
```

### 参数说明

- `--source`: 输入源（图像路径、视频路径或摄像头ID）
- `--weights`: 模型权重路径（默认使用预训练YOLOv8）
- `--conf`: 置信度阈值（0.0-1.0）
- `--output`: 输出目录
- `--save-json`: 保存检测结果为JSON格式

## 训练自定义模型

### 准备数据集

1. 收集并标注数据
   - 收集包含目标环境违规行为的图像
   - 使用标注工具（如Labelimg, CVAT）标注图像中的违规行为
   - 按照YOLOv8格式组织数据（每个图像对应一个txt标注文件）

2. 使用数据准备脚本
```bash
python prepare_data.py --data_dir raw_data --output_dir dataset
```

### 开始训练

```bash
python train.py --data dataset/data.yaml --epochs 100 --batch-size 16 --img-size 640
```

### 参数说明

- `--data`: 数据集配置文件路径
- `--epochs`: 训练轮数
- `--batch-size`: 批次大小
- `--img-size`: 图像大小
- `--weights`: 初始权重路径（可选）
- `--device`: 训练设备（cuda:0, cpu等）

## 系统工作流程

1. **图像输入**：从摄像头、图片或视频获取输入
2. **预处理**：调整大小、归一化等操作
3. **目标检测**：使用YOLOv8模型进行多类别目标检测
4. **后处理**：应用非极大值抑制、过滤低置信度检测结果
5. **结果展示**：在图像上绘制边界框、类别和置信度
6. **结果输出**：保存检测结果（图像、视频或JSON）

## 项目结构

```
environment-violation-detector/
├── app.py                   # Streamlit Web应用
├── model.py                 # 模型定义
├── train.py                 # 训练脚本
├── detect.py                # 检测脚本
├── prepare_data.py          # 数据准备脚本
├── requirements.txt         # 依赖库列表
├── README.md                # 项目说明
└── dataset/                 # 数据集目录（可选）
```

## 注意事项

- 首次运行时，系统会自动下载YOLOv8预训练模型
- GPU可显著提高检测和训练速度
- 建议使用高质量的数据集进行训练，以提高检测准确率
- 对于大型项目，可能需要调整检测参数以获得最佳性能

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 参考资料

- [YOLOv8 官方文档](https://docs.ultralytics.com/)
- [Streamlit 官方文档](https://docs.streamlit.io/) 