"""
测试图表集成功能的示例脚本
"""

import requests
import json

# 服务器配置
SERVER_URL = "http://192.168.0.109:8089"

def test_basic_conversion():
    """测试基本的markdown转换功能"""
    print("测试基本markdown转换...")
    
    markdown_content = """
# 基本测试报告

这是一个**基本**的markdown文档测试。

## 表格测试

| 项目 | 数值 | 单位 |
|------|------|------|
| 温度 | 850 | ℃ |
| 压力 | 1.2 | MPa |

## 列表测试

- 项目1
- 项目2
- 项目3
"""
    
    response = requests.post(
        f"{SERVER_URL}/office/word/convert",
        data=markdown_content.encode('utf-8'),
        headers={'Content-Type': 'text/plain'}
    )
    
    if response.status_code == 200:
        download_url = response.text
        print(f"基本转换成功！下载链接: {download_url}")
        return True
    else:
        print(f"基本转换失败: {response.status_code} - {response.text}")
        return False

def test_chart_conversion():
    """测试带图表的markdown转换功能"""
    print("测试带图表的markdown转换...")
    
    # 准备测试数据
    markdown_content = """
# 垃圾焚烧企业数据分析报告

## 1. 数据概览

本月共处理垃圾 **1200吨**，设备运行稳定。

| 指标 | 数值 | 单位 | 标准值 |
|------|------|------|--------|
| 累计处理量 | 1200 | 吨 | - |
| 运行时间 | 720 | 小时 | - |
| 平均炉温 | 865 | ℃ | ≥850 |
| PM排放 | 15.2 | mg/Nm³ | ≤30 |

## 2. 炉膛温度变化趋势

下图显示了本月炉膛温度的变化情况：

![炉膛温度变化趋势](chart_temperature)

从图表可以看出，炉膛温度始终保持在850℃以上，符合环保要求。

## 3. 污染物排放统计

各类污染物的排放情况如下：

![污染物排放对比](chart_emissions)

## 4. 工况分布分析

设备运行工况分布：

![工况分布](chart_status)

## 5. 决策建议

基于以上数据分析，提出以下建议：

1. **温度控制**：当前温度控制良好，建议保持现有操作参数
2. **排放控制**：PM排放略高，建议增加除尘设备维护频次
3. **运行优化**：正常运行时间占比较高，设备状态良好

## 6. 结论

本月设备运行状态良好，各项环保指标基本达标，建议继续保持现有管理水平。
"""
    
    # 图表配置
    chart_configs = {
        "chart_temperature": {
            "title": {"text": "炉膛温度变化趋势"},
            "tooltip": {"trigger": "axis"},
            "xAxis": {
                "type": "category",
                "data": ["第1周", "第2周", "第3周", "第4周"]
            },
            "yAxis": {"type": "value", "name": "温度(℃)"},
            "series": [{
                "type": "line",
                "name": "1号炉",
                "data": [855, 862, 858, 870],
                "smooth": True
            }, {
                "type": "line", 
                "name": "2号炉",
                "data": [850, 865, 860, 875],
                "smooth": True
            }]
        },
        
        "chart_emissions": {
            "title": {"text": "污染物排放对比"},
            "tooltip": {"trigger": "axis"},
            "xAxis": {
                "type": "category",
                "data": ["PM", "NOx", "SO2", "HCl", "CO"]
            },
            "yAxis": {"type": "value", "name": "浓度(mg/Nm³)"},
            "series": [{
                "type": "bar",
                "name": "实际排放",
                "data": [15.2, 45.8, 12.3, 8.5, 28.7],
                "itemStyle": {"color": "#5470c6"}
            }, {
                "type": "bar",
                "name": "标准限值", 
                "data": [30, 300, 100, 60, 100],
                "itemStyle": {"color": "#ff6b6b"}
            }]
        },
        
        "chart_status": {
            "title": {"text": "设备工况分布"},
            "tooltip": {"trigger": "item"},
            "series": [{
                "type": "pie",
                "radius": "60%",
                "data": [
                    {"name": "正常运行", "value": 680},
                    {"name": "启动", "value": 15},
                    {"name": "停机", "value": 20},
                    {"name": "故障", "value": 5}
                ],
                "emphasis": {
                    "itemStyle": {
                        "shadowBlur": 10,
                        "shadowOffsetX": 0,
                        "shadowColor": "rgba(0, 0, 0, 0.5)"
                    }
                }
            }]
        }
    }
    
    # 准备请求数据
    request_data = {
        "markdown": markdown_content,
        "charts": chart_configs
    }
    
    # 发送请求
    response = requests.post(
        f"{SERVER_URL}/office/word/convert_with_charts",
        json=request_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 200:
        download_url = response.text
        print(f"图表转换成功！下载链接: {download_url}")
        return True
    else:
        print(f"图表转换失败: {response.status_code} - {response.text}")
        return False

def test_embedded_charts():
    """测试嵌入式图表配置"""
    print("测试嵌入式图表配置...")
    
    markdown_content = """
# 嵌入式图表测试

## 温度趋势

```echarts
{
  "title": {"text": "嵌入式温度图表"},
  "xAxis": {
    "type": "category",
    "data": ["1月", "2月", "3月", "4月", "5月", "6月"]
  },
  "yAxis": {"type": "value"},
  "series": [{
    "type": "line",
    "data": [850, 860, 855, 870, 865, 875]
  }]
}
```

这是一个直接嵌入在markdown中的图表配置。
"""
    
    request_data = {
        "markdown": markdown_content,
        "charts": {}
    }
    
    response = requests.post(
        f"{SERVER_URL}/office/word/convert_with_charts",
        json=request_data,
        headers={'Content-Type': 'application/json'}
    )
    
    if response.status_code == 200:
        download_url = response.text
        print(f"嵌入式图表转换成功！下载链接: {download_url}")
        return True
    else:
        print(f"嵌入式图表转换失败: {response.status_code} - {response.text}")
        return False

if __name__ == "__main__":
    print("开始测试图表集成功能...\n")
    
    # 测试基本功能
    success1 = test_basic_conversion()
    print()
    
    # 测试图表功能
    success2 = test_chart_conversion()
    print()
    
    # 测试嵌入式图表
    success3 = test_embedded_charts()
    print()
    
    # 总结
    print("=" * 50)
    print("测试结果总结:")
    print(f"基本转换: {'✓' if success1 else '✗'}")
    print(f"图表转换: {'✓' if success2 else '✗'}")
    print(f"嵌入式图表: {'✓' if success3 else '✗'}")
    
    if all([success1, success2, success3]):
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败，请检查服务器状态和配置。")
